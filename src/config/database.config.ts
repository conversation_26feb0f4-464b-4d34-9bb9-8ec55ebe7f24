import { Prisma } from '../../generated/prisma';

/**
 * Database configuration interface following clean code principles
 * Defines all possible database connection options
 */
export interface DatabaseConfig {
  /**
   * Database connection URL from environment
   */
  url: string;

  /**
   * Connection pool configuration
   */
  connectionPool: {
    /**
     * Maximum number of database connections in the pool
     * @default 20
     */
    max: number;

    /**
     * Minimum number of database connections in the pool
     * @default 2
     */
    min?: number;

    /**
     * Connection timeout in milliseconds
     * @default 20000 (20 seconds)
     */
    timeout?: number;

    /**
     * Maximum time a connection can remain idle before being closed (in milliseconds)
     * @default 10000 (10 seconds)
     */
    idleTimeoutMillis?: number;
  };

  /**
   * Query execution timeout in milliseconds
   * @default 60000 (60 seconds)
   */
  queryTimeout?: number;

  /**
   * Enable query logging in development
   */
  enableLogging?: boolean;
}

/**
 * Prisma client options interface for better type safety
 */
export interface PrismaClientOptions extends Prisma.PrismaClientOptions {
  datasources?: {
    db: {
      url: string;
    };
  };
}

/**
 * Get database configuration from environment variables
 * Validates and returns properly typed configuration
 */
export function getDatabaseConfig(): DatabaseConfig {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error(
      'DATABASE_URL environment variable is required but not defined',
    );
  }

  // Parse connection pool settings from environment or use defaults
  const maxConnections = parseInt(
    process.env.DATABASE_POOL_MAX || '20',
    10,
  );
  const minConnections = parseInt(
    process.env.DATABASE_POOL_MIN || '2',
    10,
  );
  const connectionTimeout = parseInt(
    process.env.DATABASE_CONNECTION_TIMEOUT || '20000',
    10,
  );
  const idleTimeout = parseInt(
    process.env.DATABASE_IDLE_TIMEOUT || '10000',
    10,
  );
  const queryTimeout = parseInt(
    process.env.DATABASE_QUERY_TIMEOUT || '60000',
    10,
  );

  // Validate configuration values
  if (maxConnections < 1) {
    throw new Error('DATABASE_POOL_MAX must be at least 1');
  }

  if (minConnections < 0) {
    throw new Error('DATABASE_POOL_MIN cannot be negative');
  }

  if (minConnections > maxConnections) {
    throw new Error('DATABASE_POOL_MIN cannot exceed DATABASE_POOL_MAX');
  }

  return {
    url: databaseUrl,
    connectionPool: {
      max: maxConnections,
      min: minConnections,
      timeout: connectionTimeout,
      idleTimeoutMillis: idleTimeout,
    },
    queryTimeout,
    enableLogging: process.env.NODE_ENV === 'development',
  };
}

/**
 * Build PostgreSQL connection string with connection pool parameters
 * Appends pool configuration to the DATABASE_URL
 */
export function buildConnectionString(config: DatabaseConfig): string {
  const url = new URL(config.url);

  // Add connection pool parameters to query string
  url.searchParams.set('connection_limit', config.connectionPool.max.toString());
  url.searchParams.set('pool_timeout', (config.connectionPool.timeout! / 1000).toString());

  // Add application name for better connection tracking
  if (!url.searchParams.has('application_name')) {
    url.searchParams.set('application_name', 'orbitum-app');
  }

  // Set statement timeout for query execution
  url.searchParams.set('statement_timeout', config.queryTimeout!.toString());

  // Enable connection pooling mode for better performance
  if (!url.searchParams.has('pgbouncer')) {
    url.searchParams.set('pgbouncer', 'true');
  }

  return url.toString();
}

/**
 * Create Prisma client options with connection pooling configuration
 */
export function createPrismaClientOptions(
  config: DatabaseConfig,
): PrismaClientOptions {
  const connectionString = buildConnectionString(config);

  const options: PrismaClientOptions = {
    datasources: {
      db: {
        url: connectionString,
      },
    },
    log: config.enableLogging
      ? [
          { emit: 'event', level: 'query' },
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'info' },
          { emit: 'event', level: 'warn' },
        ]
      : [
          { emit: 'event', level: 'error' },
          { emit: 'event', level: 'warn' },
        ],
  };

  return options;
}