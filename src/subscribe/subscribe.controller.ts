import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
  Logger,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/user-info.decorator';
import { UserInfo } from '../auth/interfaces/user-info.interface';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';
import { SubscribeService } from './subscribe.service';
import { CreateSubscribeDto } from './dto/create-subscribe.dto';
import { UpdateSubscribeDto } from './dto/update-subscribe.dto';
import { SubscribeResponseDto } from './dto/subscribe-response.dto';
import { SubscriptionStatusResponseDto } from './dto/subscription-status-response.dto';
import { GetSubscriptionStatusParamsDto } from './dto/get-subscription-status-params.dto';
import { SubscriptionListQueryDto } from './dto/subscription-list-query.dto';
import { SubscriptionListApiResponseDto } from './dto/subscription-list-response.dto';

@ApiTags('Subscriptions')
@Controller('subscribe')
@UseGuards(EnhancedJwtAuthGuard)
@ApiBearerAuth('JWT')
export class SubscribeController {
  private readonly logger = new Logger(SubscribeController.name);

  constructor(private readonly subscribeService: SubscribeService) {}

  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Initiate a new subscription',
    description:
      'Initiates a new subscription for a product with the specified details',
  })
  @ApiBody({ type: CreateSubscribeDto })
  @ApiResponse({
    status: 201,
    description: 'Subscription successfully created',
    type: SubscribeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Product, wallet, or user not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async create(
    @Body() createSubscribeDto: CreateSubscribeDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<SubscribeResponseDto> {
    this.logger.log(`Creating subscription for user: ${userInfo.id}`);

    return await this.subscribeService.create(createSubscribeDto, userInfo.id);
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update an existing subscription',
    description:
      'Updates an existing subscription with new wallet, chain, payment provider, pricing, currency, and transaction hash information. Automatically calculates status_buy and dateCutoff based on current time.',
  })
  @ApiBody({ type: UpdateSubscribeDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription successfully updated',
    type: SubscribeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Subscription, wallet, or chain not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async update(
    @Param('id') subscriptionId: string,
    @Body() updateSubscribeDto: UpdateSubscribeDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<SubscribeResponseDto> {
    this.logger.log(
      `Updating subscription ${subscriptionId} for user: ${userInfo.id}`,
    );

    return await this.subscribeService.update(
      subscriptionId,
      updateSubscribeDto,
      userInfo.id,
    );
  }

  @Get(':id/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Retrieve subscription status details',
    description:
      'Retrieves status information for a specific subscription including payment status, checkout status, subscription status, date cutoff, and buy status.',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription status successfully retrieved',
    type: SubscriptionStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid subscription ID format',
  })
  @ApiResponse({
    status: 404,
    description: 'Subscription not found or access denied',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getSubscriptionStatus(
    @Param(new ValidationPipe({ transform: true }))
    params: GetSubscriptionStatusParamsDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<SubscriptionStatusResponseDto> {
    this.logger.log(
      `Retrieving subscription status for ${params.id} for user: ${userInfo.id}`,
    );

    return await this.subscribeService.getSubscriptionStatus(
      params.id,
      userInfo.id,
    );
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Retrieve all subscriptions for authenticated user',
    description:
      'Retrieves a paginated list of all subscriptions for the authenticated user. Supports optional filtering by product ID and includes pagination metadata.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'productId',
    required: false,
    type: String,
    description: 'Filter subscriptions by product ID',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['createdAt', 'updatedAt', 'dateCutoff', 'amount'],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscriptions successfully retrieved',
    type: SubscriptionListApiResponseDto,
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Retrieved 2 subscriptions successfully',
        data: {
          data: [
            {
              id: '550e8400-e29b-41d4-a716-446655440000',
              productId: '550e8400-e29b-41d4-a716-446655440001',
              userId: '550e8400-e29b-41d4-a716-446655440002',
              walletId: null,
              chainId: null,
              paymentProvider: 'stripe',
              paymentStatus: 'UNPAID',
              checkoutStatus: 'PREPARED',
              subscribeStatus: 'ACTIVE',
              amount: '29.99',
              expectedPrice: '0.00',
              totalPrice: '0.00',
              statusBuy: 'IN_PROCESS',
              dateCutoff: '2024-01-31T00:00:00.000Z',
              currencies: 'USD',
              txHash: null,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z',
            },
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getSubscriptions(
    @Query() query: SubscriptionListQueryDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<SubscriptionListApiResponseDto> {
    this.logger.log(
      `Retrieving subscriptions for user: ${userInfo.id} with query: ${JSON.stringify(query)}`,
    );

    return await this.subscribeService.getSubscriptionsByUser(
      query,
      userInfo.id,
    );
  }
}
