import { Decimal } from '@prisma/client/runtime/library';
import { $Enums } from '../../../generated/prisma';

export interface SubscribeEntity {
  id: string;
  productId: string;
  userId: string;
  walletId: string;
  chainId: string;
  paymentProvider: string;
  paymentStatus: string;
  checkoutStatus: string;
  subscribeStatus: string;
  amount: Decimal;
  expectedPrice: Decimal;
  totalPrice: Decimal;
  statusBuy: $Enums.StatusBuy;
  dateCutoff: Date;
  currencies: string;
  txHash: string | null;
  createdAt: Date;
  updatedAt: Date;
}
