/**
 * Constants for Subscribe service
 */

export const SUBSCRIBE_ERROR_MESSAGES = {
  USER_NOT_FOUND: 'User not found',
  PRODUCT_NOT_FOUND: 'Product not found',
  WALLET_NOT_FOUND: 'Wallet not found or does not belong to user',
  CHAIN_NOT_FOUND: 'Chain not found for wallet',
  SUBSCRIPTION_CREATE_FAILED: 'Failed to create subscription',
  SUBSCRIPTION_NOT_FOUND: 'Subscription not found',
  SUBSCRIPTION_UPDATE_FAILED: 'Failed to update subscription',
  SUBSCRIPTION_STATUS_RETRIEVAL_FAILED:
    'Failed to retrieve subscription status',
  SUBSCRIPTION_ACCESS_DENIED:
    'Access denied - subscription does not belong to user',
  INVALID_SUBSCRIPTION_ID: 'Invalid subscription ID format',
  INVALID_PAYMENT_PROVIDER: 'Invalid payment provider',
  INVALID_AMOUNT: 'Invalid subscription amount',
  INVALID_EXPECTED_PRICE: 'Invalid expected price',
  INVALID_TOTAL_PRICE: 'Invalid total price',
} as const;

export const SUBSCRIBE_SUCCESS_MESSAGES = {
  SUBSCRIPTION_CREATED: 'Subscription created successfully',
  SUBSCRIPTION_UPDATED: 'Subscription updated successfully',
  SUBSCRIPTION_STATUS_RETRIEVED: 'Subscription status retrieved successfully',
} as const;

export const PAYMENT_PROVIDERS = {
  STRIPE: 'stripe',
  PAYPAL: 'paypal',
  COINBASE: 'coinbase',
  METAMASK: 'metamask',
} as const;

export const PAYMENT_STATUS = {
  PAID: 'PAID',
  UNPAID: 'UNPAID',
  PENDING: 'PENDING',
  FAILED: 'FAILED',
} as const;

export const CHECKOUT_STATUS = {
  PREPARED: 'PREPARED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
} as const;

export const SUBSCRIBE_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
} as const;

export const STATUS_BUY = {
  IN_PROCESS: 'IN_PROCESS',
  QUEUED_FOR_NEXT_DAY: 'QUEUED_FOR_NEXT_DAY',
} as const;

export const DEFAULT_VALUES = {
  PAYMENT_PROVIDER: PAYMENT_PROVIDERS.STRIPE,
  PAYMENT_STATUS: PAYMENT_STATUS.UNPAID,
  CHECKOUT_STATUS: CHECKOUT_STATUS.PREPARED,
  SUBSCRIBE_STATUS: SUBSCRIBE_STATUS.ACTIVE,
  EXPECTED_PRICE: 0,
  TOTAL_PRICE: 0,
  STATUS_BUY: 'IN_PROCESS' as const,
  CURRENCIES: 'USD',
  // Default date cutoff to 30 days from now
  DATE_CUTOFF_DAYS: 30,
} as const;
