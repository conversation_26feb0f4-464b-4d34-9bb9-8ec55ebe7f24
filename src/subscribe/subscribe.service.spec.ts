import { Test, TestingModule } from '@nestjs/testing';
import { SubscribeService } from './subscribe.service';
import { PrismaService } from '../prisma/prisma.service';
import { SubscriptionListQueryDto } from './dto/subscription-list-query.dto';
import { SubscriptionListApiResponseDto } from './dto/subscription-list-response.dto';
import { Decimal } from '@prisma/client/runtime/library';
import { $Enums } from '../../generated/prisma';

describe('SubscribeService - getSubscriptionsByUser', () => {
  let service: SubscribeService;

  const mockUserId = '01994ba2-3c6e-7b53-9660-0f766d409577';
  const mockProductId = '01997f11-92a3-7355-9f7c-fe2425be19c5';

  const mockSubscription = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409578',
    productId: mockProductId,
    userId: mockUserId,
    walletId: null,
    chainId: null,
    paymentProvider: 'stripe',
    paymentStatus: 'UNPAID',
    checkoutStatus: 'PREPARED',
    subscribeStatus: 'ACTIVE',
    amount: new Decimal('29.99'),
    expectedPrice: new Decimal('0.00'),
    totalPrice: new Decimal('0.00'),
    statusBuy: $Enums.StatusBuy.IN_PROCESS,
    dateCutoff: new Date('2024-01-31T00:00:00.000Z'),
    currencies: 'USD',
    txHash: null,
    createdAt: new Date('2024-01-01T00:00:00.000Z'),
    updatedAt: new Date('2024-01-01T00:00:00.000Z'),
  };

  const mockPrismaService = {
    subscribe: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscribeService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<SubscribeService>(SubscribeService);

    // Mock logger to avoid console output during tests
    jest.spyOn(service['logger'], 'log').mockImplementation();
    jest.spyOn(service['logger'], 'debug').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSubscriptionsByUser', () => {
    it('should return paginated subscriptions without filtering', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockPrismaService.subscribe.count.mockResolvedValue(2);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
        { ...mockSubscription, id: 'another-subscription-id' },
      ]);

      const result = await service.getSubscriptionsByUser(query, mockUserId);

      expect(result).toBeInstanceOf(SubscriptionListApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.status).toBe('success');
      expect(result.data.data).toHaveLength(2);
      expect(result.data.meta.total).toBe(2);
      expect(result.data.meta.page).toBe(1);
      expect(result.data.meta.limit).toBe(10);
      expect(result.data.meta.hasNext).toBe(false);
      expect(result.data.meta.hasPrev).toBe(false);

      expect(mockPrismaService.subscribe.count).toHaveBeenCalledWith({
        where: { userId: mockUserId },
      });
      expect(mockPrismaService.subscribe.findMany).toHaveBeenCalledWith({
        where: { userId: mockUserId },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: expect.objectContaining({
          id: true,
          productId: true,
          userId: true,
        }),
      });
    });

    it('should return filtered subscriptions by productId', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        productId: mockProductId,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockPrismaService.subscribe.count.mockResolvedValue(1);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
      ]);

      const result = await service.getSubscriptionsByUser(query, mockUserId);

      expect(result.data.data).toHaveLength(1);
      expect(result.data.meta.total).toBe(1);

      expect(mockPrismaService.subscribe.count).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          productId: mockProductId,
        },
      });
      expect(mockPrismaService.subscribe.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          productId: mockProductId,
        },
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: expect.any(Object),
      });
    });

    it('should handle pagination correctly for page 2', async () => {
      const query: SubscriptionListQueryDto = {
        page: 2,
        limit: 5,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockPrismaService.subscribe.count.mockResolvedValue(12);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
      ]);

      const result = await service.getSubscriptionsByUser(query, mockUserId);

      expect(result.data.meta.page).toBe(2);
      expect(result.data.meta.limit).toBe(5);
      expect(result.data.meta.total).toBe(12);
      expect(result.data.meta.totalPages).toBe(3);
      expect(result.data.meta.hasNext).toBe(true);
      expect(result.data.meta.hasPrev).toBe(true);

      expect(mockPrismaService.subscribe.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 5, // (page - 1) * limit = (2 - 1) * 5 = 5
          take: 5,
        }),
      );
    });

    it('should handle different sorting options', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'amount',
        sortOrder: 'asc',
      };

      mockPrismaService.subscribe.count.mockResolvedValue(1);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
      ]);

      await service.getSubscriptionsByUser(query, mockUserId);

      expect(mockPrismaService.subscribe.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { amount: 'asc' },
        }),
      );
    });

    it('should return empty results when no subscriptions found', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
      };

      mockPrismaService.subscribe.count.mockResolvedValue(0);
      mockPrismaService.subscribe.findMany.mockResolvedValue([]);

      const result = await service.getSubscriptionsByUser(query, mockUserId);

      expect(result.data.data).toHaveLength(0);
      expect(result.data.meta.total).toBe(0);
      expect(result.data.meta.totalPages).toBe(0);
      expect(result.data.meta.hasNext).toBe(false);
      expect(result.data.meta.hasPrev).toBe(false);
    });

    it('should use default values when query parameters are not provided', async () => {
      const query: SubscriptionListQueryDto = {};

      mockPrismaService.subscribe.count.mockResolvedValue(1);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
      ]);

      await service.getSubscriptionsByUser(query, mockUserId);

      expect(mockPrismaService.subscribe.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0, // (1 - 1) * 10 = 0
          take: 10, // default limit
          orderBy: { createdAt: 'desc' }, // default sort
        }),
      );
    });

    it('should handle database errors gracefully', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
      };

      const dbError = new Error('Database connection failed');
      mockPrismaService.subscribe.count.mockRejectedValue(dbError);

      // Mock the handleServiceError method to throw the expected error
      jest
        .spyOn(service as any, 'handleServiceError')
        .mockImplementation(() => {
          throw dbError;
        });

      await expect(
        service.getSubscriptionsByUser(query, mockUserId),
      ).rejects.toThrow('Database connection failed');
    });

    it('should log appropriate messages during execution', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        productId: mockProductId,
      };

      const logSpy = jest.spyOn(service['logger'], 'log');
      const debugSpy = jest.spyOn(service['logger'], 'debug');

      mockPrismaService.subscribe.count.mockResolvedValue(1);
      mockPrismaService.subscribe.findMany.mockResolvedValue([
        mockSubscription,
      ]);

      await service.getSubscriptionsByUser(query, mockUserId);

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          `Retrieving subscriptions for user: ${mockUserId}`,
        ),
      );
      expect(debugSpy).toHaveBeenCalledWith(
        `Filtering by productId: ${mockProductId}`,
      );
      expect(debugSpy).toHaveBeenCalledWith('Total subscriptions found: 1');
      expect(debugSpy).toHaveBeenCalledWith('Retrieved 1 subscriptions');
      expect(logSpy).toHaveBeenCalledWith(
        `Successfully retrieved 1 subscriptions for user ${mockUserId}`,
      );
    });
  });
});
