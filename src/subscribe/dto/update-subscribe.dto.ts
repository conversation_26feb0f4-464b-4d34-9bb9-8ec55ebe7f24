import {
  <PERSON><PERSON><PERSON>D,
  IsNotEmpty,
  IsString,
  IsOptional,
  ValidateBy,
  ValidationOptions,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Custom validator for Decimal that accepts both string and number
function IsDecimalAmount(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isDecimalAmount',
      validator: {
        validate(value: any): boolean {
          if (value === null || value === undefined) {
            return false;
          }

          // Accept both string and number
          if (typeof value === 'string' || typeof value === 'number') {
            try {
              const decimal = new Decimal(value.toString());
              return decimal.gt(0) && decimal.lte(999999.99);
            } catch {
              return false;
            }
          }

          // If it's already a Decimal instance
          if (value instanceof Decimal) {
            return value.gt(0) && value.lte(999999.99);
          }

          return false;
        },
        defaultMessage(): string {
          return 'Amount must be a positive number up to 999999.99 with maximum 8 decimal places';
        },
      },
    },
    validationOptions,
  );
}

export class UpdateSubscribeDto {
  @ApiProperty({
    description: 'Wallet ID for the subscription',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Wallet ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Wallet ID is required' })
  wallet_id: string;

  @ApiProperty({
    description: 'Chain ID for blockchain network',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Chain ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Chain ID is required' })
  chain_id: string;

  @ApiProperty({
    description: 'Payment provider for the subscription',
    example: 'stripe',
    enum: ['stripe', 'paypal', 'coinbase', 'metamask'],
  })
  @IsString({ message: 'Payment provider must be a string' })
  @IsNotEmpty({ message: 'Payment provider is required' })
  payment_provider: string;

  @ApiProperty({
    description: 'Expected price for the subscription (must be positive)',
    example: 29.99,
    type: 'number',
    minimum: 0.01,
    maximum: 999999.99,
  })
  @IsDecimalAmount({
    message:
      'Expected price must be a positive number up to 999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lt(0)) {
        throw new Error('Expected price must be greater than or equal to 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid expected price format');
  })
  expected_price: Decimal;

  @ApiProperty({
    description: 'Currency code for the payment',
    example: 'USD',
  })
  @IsString({ message: 'Currencies must be a string' })
  @IsNotEmpty({ message: 'Currencies is required' })
  currencies: string;

  @ApiProperty({
    description: 'Total price for the subscription (must be positive)',
    example: 29.99,
    type: 'number',
    minimum: 0.01,
    maximum: 999999.99,
  })
  @IsDecimalAmount({
    message:
      'Total price must be a positive number up to 999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lt(0)) {
        throw new Error('Total price must be greater than or equal to 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid total price format');
  })
  total_price: Decimal;

  @ApiPropertyOptional({
    description: 'Transaction hash for blockchain transactions (optional)',
    example: '0x1234567890abcdef1234567890abcdef12345678',
    type: 'string',
  })
  @IsOptional()
  @IsString({ message: 'Transaction hash must be a string' })
  txHash?: string;
}
