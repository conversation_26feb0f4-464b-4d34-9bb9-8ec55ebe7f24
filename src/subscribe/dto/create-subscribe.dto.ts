import {
  IsUUID,
  IsNotEmpty,
  IsOptional,
  ValidateBy,
  ValidationOptions,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Custom validator for Decimal that accepts both string and number
function IsDecimalAmount(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isDecimalAmount',
      validator: {
        validate(value: any): boolean {
          if (value === null || value === undefined) {
            return false;
          }

          // Accept both string and number
          if (typeof value === 'string' || typeof value === 'number') {
            try {
              const decimal = new Decimal(value.toString());
              return decimal.gt(0) && decimal.lte(999999.99);
            } catch {
              return false;
            }
          }

          // If it's already a Decimal instance
          if (value instanceof Decimal) {
            return value.gt(0) && value.lte(999999.99);
          }

          return false;
        },
        defaultMessage(): string {
          return 'Amount must be a positive number up to 999999.99 with maximum 8 decimal places';
        },
      },
    },
    validationOptions,
  );
}

export class CreateSubscribeDto {
  @ApiProperty({
    description: 'Unique identifier of the product to subscribe to',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Product ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Product ID is required' })
  productId: string;

  @ApiProperty({
    description: 'Subscription amount (must be positive)',
    example: 29.99,
    type: 'number',
    minimum: 0.01,
    maximum: 999999.99,
  })
  @IsDecimalAmount({
    message:
      'Amount must be a positive number up to 999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lte(0)) {
        throw new Error('Amount must be greater than 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid amount format');
  })
  amount: Decimal;

  @ApiPropertyOptional({
    description:
      'Optional wallet ID for payment (if not provided, subscription will be created without wallet)',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID('all', { message: 'Wallet ID must be a valid UUID' })
  walletId?: string;

  @ApiPropertyOptional({
    description:
      'Optional chain ID for blockchain network (if not provided, subscription will be created without chain)',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID('all', { message: 'Chain ID must be a valid UUID' })
  chainId?: string;
}
