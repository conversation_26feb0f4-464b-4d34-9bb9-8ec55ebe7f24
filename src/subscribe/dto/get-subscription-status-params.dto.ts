import { IsUUID, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for validating subscription status retrieval parameters
 * Ensures the subscription ID is a valid UUID
 */
export class GetSubscriptionStatusParamsDto {
  @ApiProperty({
    description: 'Unique identifier of the subscription to retrieve status for',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', {
    message: 'Subscription ID must be a valid UUID',
  })
  @IsNotEmpty({
    message: 'Subscription ID is required',
  })
  id: string;
}
