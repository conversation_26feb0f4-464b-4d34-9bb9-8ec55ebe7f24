import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '../../../generated/prisma';

/**
 * Response DTO for subscription status details
 * Contains essential status information for a subscription
 */
export class SubscriptionStatusResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the subscription',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  subscriptionId: string;

  @ApiProperty({
    description: 'Current payment status of the subscription',
    example: 'UNPAID',
    enum: ['PAID', 'UNPAID', 'PENDING', 'FAILED'],
  })
  paymentStatus: string;

  @ApiProperty({
    description: 'Current checkout status of the subscription',
    example: 'PREPARED',
    enum: ['PREPARED', 'PROCESSING', 'COMPLETED', 'FAILED'],
  })
  checkoutStatus: string;

  @ApiProperty({
    description: 'Current subscription status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE', 'CANCELLED', 'EXPIRED'],
  })
  subscriptionStatus: string;

  @ApiProperty({
    description: 'Date cutoff for the subscription processing',
    example: '2024-01-31T14:00:00.000Z',
    type: Date,
  })
  dateCutoff: Date;

  @ApiProperty({
    description: 'Status of the buy transaction',
    example: 'IN_PROCESS',
    enum: ['IN_PROCESS', 'QUEUED_FOR_NEXT_DAY'],
  })
  statusBuy: $Enums.StatusBuy;
}
