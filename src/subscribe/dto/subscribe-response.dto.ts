import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';
import { $Enums } from '../../../generated/prisma';

export class SubscribeResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the subscription',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description:
      'Generated unique request number for this update (ORB-YYYY-MM-DD-counter)',
    example: 'ORB-2025-10-03-1',
    nullable: true,
  })
  requestNumber: string | null;

  @ApiProperty({
    description: 'Product ID associated with the subscription',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  productId: string;

  @ApiProperty({
    description: 'User ID who created the subscription',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  userId: string;

  @ApiProperty({
    description: 'Wallet ID used for payment',
    example: null,
    nullable: true,
  })
  walletId: string | null;

  @ApiProperty({
    description: 'Chain ID for the blockchain network',
    example: null,
    nullable: true,
  })
  chainId: string | null;

  @ApiProperty({
    description: 'Payment provider used',
    example: 'stripe',
    enum: ['stripe', 'paypal', 'coinbase'],
  })
  paymentProvider: string;

  @ApiProperty({
    description: 'Current payment status',
    example: 'UNPAID',
    enum: ['PAID', 'UNPAID', 'PENDING', 'FAILED'],
  })
  paymentStatus: string;

  @ApiProperty({
    description: 'Current checkout status',
    example: 'PREPARED',
    enum: ['PREPARED', 'PROCESSING', 'COMPLETED', 'FAILED'],
  })
  checkoutStatus: string;

  @ApiProperty({
    description: 'Current subscription status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE', 'CANCELLED', 'EXPIRED'],
  })
  subscribeStatus: string;

  @ApiProperty({
    description: 'Subscription amount',
    example: '29.99',
    type: 'number',
  })
  amount: Decimal;

  @ApiProperty({
    description: 'Expected price for the subscription',
    example: '0.00',
    type: 'number',
  })
  expectedPrice: Decimal;

  @ApiProperty({
    description: 'Total price for the subscription',
    example: '0.00',
    type: 'number',
  })
  totalPrice: Decimal;

  @ApiProperty({
    description: 'Status of the buy transaction',
    example: 'IN_PROCESS',
    enum: ['IN_PROCESS', 'QUEUED_FOR_NEXT_DAY'],
  })
  statusBuy: $Enums.StatusBuy;

  @ApiProperty({
    description: 'Date cutoff for the subscription',
    example: '2024-01-31T00:00:00.000Z',
  })
  dateCutoff: Date;

  @ApiProperty({
    description: 'Currency code for the payment',
    example: 'USD',
  })
  currencies: string;

  @ApiProperty({
    description: 'Transaction hash for blockchain transactions',
    example: '0x1234567890abcdef1234567890abcdef12345678',
    nullable: true,
  })
  txHash: string | null;

  @ApiProperty({
    description: 'Subscription creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Subscription last update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}
