import { ApiProperty } from '@nestjs/swagger';
import { SubscribeResponseDto } from './subscribe-response.dto';

export class PaginationMetaDto {
  @ApiProperty({
    example: 1,
    description: 'Current page number',
  })
  page: number;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
  })
  limit: number;

  @ApiProperty({
    example: 25,
    description: 'Total number of items',
  })
  total: number;

  @ApiProperty({
    example: 3,
    description: 'Total number of pages',
  })
  totalPages: number;

  @ApiProperty({
    example: true,
    description: 'Whether there is a next page',
  })
  hasNext: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether there is a previous page',
  })
  hasPrev: boolean;

  constructor(partial: Partial<PaginationMetaDto>) {
    Object.assign(this, partial);
  }
}

export class SubscriptionListResponseDto {
  @ApiProperty({
    type: [SubscribeResponseDto],
    description: 'Array of subscription entries',
  })
  data: SubscribeResponseDto[];

  @ApiProperty({
    type: PaginationMetaDto,
    description: 'Pagination metadata',
  })
  meta: PaginationMetaDto;

  constructor(partial: Partial<SubscriptionListResponseDto>) {
    Object.assign(this, partial);
  }
}

export class SubscriptionListApiResponseDto {
  @ApiProperty({
    example: 200,
    description: 'HTTP status code',
  })
  code: number;

  @ApiProperty({
    example: 'success',
    description: 'Response status',
  })
  status: string;

  @ApiProperty({
    example: 'Subscriptions retrieved successfully',
    description: 'Response message',
  })
  message: string;

  @ApiProperty({
    type: SubscriptionListResponseDto,
    description: 'List of subscriptions with pagination',
  })
  data: SubscriptionListResponseDto;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Response timestamp',
  })
  timestamp: string;

  constructor(
    subscriptionListData: SubscriptionListResponseDto,
    message?: string,
  ) {
    this.code = 200;
    this.status = 'success';
    this.message = message || 'Subscriptions retrieved successfully';
    this.data = subscriptionListData;
    this.timestamp = new Date().toISOString();
  }
}
