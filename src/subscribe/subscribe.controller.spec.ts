import { Test, TestingModule } from '@nestjs/testing';
import { SubscribeController } from './subscribe.controller';
import { SubscribeService } from './subscribe.service';
import { SubscriptionListQueryDto } from './dto/subscription-list-query.dto';
import { SubscriptionListApiResponseDto } from './dto/subscription-list-response.dto';
import { UserInfo } from '../auth/interfaces/user-info.interface';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';

describe('SubscribeController - getSubscriptions', () => {
  let controller: SubscribeController;

  const mockUserInfo: UserInfo = {
    id: '01994ba2-3c6e-7b53-9660-0f766d409577',
    email: '<EMAIL>',
    username: 'testuser',
    type: 'CONSUMER',
    status: 'ACTIVE',
  };

  const mockSubscriptionListResponse: SubscriptionListApiResponseDto = {
    code: 200,
    status: 'success',
    message: 'Retrieved 2 subscriptions successfully',
    data: {
      data: [
        {
          id: '01994ba2-3c6e-7b53-9660-0f766d409578',
          productId: '01997f11-92a3-7355-9f7c-fe2425be19c5',
          userId: '01994ba2-3c6e-7b53-9660-0f766d409577',
          walletId: null,
          chainId: null,
          paymentProvider: 'stripe',
          paymentStatus: 'UNPAID',
          checkoutStatus: 'PREPARED',
          subscribeStatus: 'ACTIVE',
          amount: 29.99 as any,
          expectedPrice: 0.0 as any,
          totalPrice: 0.0 as any,
          statusBuy: 'IN_PROCESS' as any,
          dateCutoff: new Date('2024-01-31T00:00:00.000Z'),
          currencies: 'USD',
          txHash: null,
          createdAt: new Date('2024-01-01T00:00:00.000Z'),
          updatedAt: new Date('2024-01-01T00:00:00.000Z'),
        },
      ],
      meta: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    },
    timestamp: '2024-01-15T10:30:00.000Z',
  };

  const mockSubscribeService = {
    getSubscriptionsByUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SubscribeController],
      providers: [
        {
          provide: SubscribeService,
          useValue: mockSubscribeService,
        },
      ],
    })
      .overrideGuard(EnhancedJwtAuthGuard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    controller = module.get<SubscribeController>(SubscribeController);

    // Mock logger to avoid console output during tests
    jest.spyOn(controller['logger'], 'log').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSubscriptions', () => {
    it('should return paginated subscriptions successfully', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        mockSubscriptionListResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
      expect(result).toEqual(mockSubscriptionListResponse);
      expect(result.code).toBe(200);
      expect(result.status).toBe('success');
      expect(result.data.data).toHaveLength(1);
      expect(result.data.meta.total).toBe(2);
    });

    it('should handle filtering by productId', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        productId: '01997f11-92a3-7355-9f7c-fe2425be19c5',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const filteredResponse = {
        ...mockSubscriptionListResponse,
        message: 'Retrieved 1 subscription successfully',
        data: {
          ...mockSubscriptionListResponse.data,
          meta: {
            ...mockSubscriptionListResponse.data.meta,
            total: 1,
          },
        },
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        filteredResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
      expect(result.data.meta.total).toBe(1);
      expect(result.message).toBe('Retrieved 1 subscription successfully');
    });

    it('should handle empty query parameters with defaults', async () => {
      const query: SubscriptionListQueryDto = {};

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        mockSubscriptionListResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
      expect(result).toEqual(mockSubscriptionListResponse);
    });

    it('should handle pagination parameters correctly', async () => {
      const query: SubscriptionListQueryDto = {
        page: 2,
        limit: 5,
        sortBy: 'amount',
        sortOrder: 'asc',
      };

      const paginatedResponse = {
        ...mockSubscriptionListResponse,
        data: {
          ...mockSubscriptionListResponse.data,
          meta: {
            page: 2,
            limit: 5,
            total: 12,
            totalPages: 3,
            hasNext: true,
            hasPrev: true,
          },
        },
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        paginatedResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
      expect(result.data.meta.page).toBe(2);
      expect(result.data.meta.limit).toBe(5);
      expect(result.data.meta.hasNext).toBe(true);
      expect(result.data.meta.hasPrev).toBe(true);
    });

    it('should return empty results when no subscriptions found', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
      };

      const emptyResponse = {
        ...mockSubscriptionListResponse,
        message: 'Retrieved 0 subscriptions successfully',
        data: {
          data: [],
          meta: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        emptyResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(result.data.data).toHaveLength(0);
      expect(result.data.meta.total).toBe(0);
      expect(result.message).toBe('Retrieved 0 subscriptions successfully');
    });

    it('should propagate service errors', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
      };

      const serviceError = new Error('Service error occurred');
      mockSubscribeService.getSubscriptionsByUser.mockRejectedValue(
        serviceError,
      );

      await expect(
        controller.getSubscriptions(query, mockUserInfo),
      ).rejects.toThrow('Service error occurred');

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
    });

    it('should log the request with correct parameters', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
        productId: '01997f11-92a3-7355-9f7c-fe2425be19c5',
      };

      const logSpy = jest.spyOn(controller['logger'], 'log');

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        mockSubscriptionListResponse,
      );

      await controller.getSubscriptions(query, mockUserInfo);

      expect(logSpy).toHaveBeenCalledWith(
        `Retrieving subscriptions for user: ${mockUserInfo.id} with query: ${JSON.stringify(query)}`,
      );
    });

    it('should handle different user types', async () => {
      const businessUserInfo: UserInfo = {
        ...mockUserInfo,
        type: 'BUSINESS',
        businessType: 'RETAIL',
      };

      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 10,
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        mockSubscriptionListResponse,
      );

      const result = await controller.getSubscriptions(query, businessUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        businessUserInfo.id,
      );
      expect(result).toEqual(mockSubscriptionListResponse);
    });

    it('should handle maximum limit parameter', async () => {
      const query: SubscriptionListQueryDto = {
        page: 1,
        limit: 100, // Maximum allowed limit
      };

      mockSubscribeService.getSubscriptionsByUser.mockResolvedValue(
        mockSubscriptionListResponse,
      );

      const result = await controller.getSubscriptions(query, mockUserInfo);

      expect(mockSubscribeService.getSubscriptionsByUser).toHaveBeenCalledWith(
        query,
        mockUserInfo.id,
      );
      expect(result).toEqual(mockSubscriptionListResponse);
    });
  });
});
