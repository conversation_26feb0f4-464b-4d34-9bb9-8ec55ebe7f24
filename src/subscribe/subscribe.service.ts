// src/subscribe/services/subscribe.service.ts
import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSubscribeDto } from './dto/create-subscribe.dto';
import { UpdateSubscribeDto } from './dto/update-subscribe.dto';
import { SubscribeResponseDto } from './dto/subscribe-response.dto';
import { SubscriptionStatusResponseDto } from './dto/subscription-status-response.dto';
import { SubscriptionListQueryDto } from './dto/subscription-list-query.dto';
import {
  SubscriptionListResponseDto,
  SubscriptionListApiResponseDto,
  PaginationMetaDto,
} from './dto/subscription-list-response.dto';
import { Decimal } from '@prisma/client/runtime/library';
import {
  SUBSCRIBE_ERROR_MESSAGES,
  DEFAULT_VALUES,
  STATUS_BUY,
} from './constants/subscribe.constants';
import { $Enums } from '../../generated/prisma';

@Injectable()
export class SubscribeService {
  private readonly logger = new Logger(SubscribeService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new subscription
   * @param createSubscribeDto - Subscription creation data
   * @param userId - The ID of the user from token authorization
   * @returns Promise<SubscribeResponseDto> - Created subscription information
   */
  async create(
    createSubscribeDto: CreateSubscribeDto,
    userId: string,
  ): Promise<SubscribeResponseDto> {
    try {
      this.logger.log(
        `Creating subscription for user: ${userId} with data: ${JSON.stringify({
          productId: createSubscribeDto.productId,
          amount: createSubscribeDto.amount.toString(),
          walletId: createSubscribeDto.walletId || 'none',
          chainId: createSubscribeDto.chainId || 'none',
        })}`,
      );

      // Validate input data
      this.validateCreateSubscribeDto(createSubscribeDto);

      // Validate user exists
      await this.validateUserExists(userId);

      // Validate product exists
      await this.validateProductExists(createSubscribeDto.productId);

      // Validate wallet and chain if provided
      if (createSubscribeDto.walletId) {
        await this.validateWalletExists(createSubscribeDto.walletId, userId);
      }

      if (createSubscribeDto.chainId) {
        await this.validateChainExists(createSubscribeDto.chainId);
      }

      // Create subscription in a transaction for data consistency
      return await this.prisma.$transaction(async (prisma) => {
        // Calculate date cutoff (30 days from now)
        const dateCutoff = new Date();
        dateCutoff.setDate(
          dateCutoff.getDate() + DEFAULT_VALUES.DATE_CUTOFF_DAYS,
        );

        const subscriptionData = {
          productId: createSubscribeDto.productId,
          userId: userId,
          walletId: createSubscribeDto.walletId || null,
          chainId: createSubscribeDto.chainId || null,
          paymentProvider: DEFAULT_VALUES.PAYMENT_PROVIDER,
          paymentStatus: DEFAULT_VALUES.PAYMENT_STATUS,
          checkoutStatus: DEFAULT_VALUES.CHECKOUT_STATUS,
          subscribeStatus: DEFAULT_VALUES.SUBSCRIBE_STATUS,
          amount: createSubscribeDto.amount,
          expectedPrice: new Decimal(DEFAULT_VALUES.EXPECTED_PRICE),
          totalPrice: new Decimal(DEFAULT_VALUES.TOTAL_PRICE),
          statusBuy: DEFAULT_VALUES.STATUS_BUY as $Enums.StatusBuy,
          dateCutoff: dateCutoff,
          currencies: DEFAULT_VALUES.CURRENCIES,
        };

        this.logger.debug(
          `Creating subscription with data: ${JSON.stringify({
            ...subscriptionData,
            amount: subscriptionData.amount.toString(),
            expectedPrice: subscriptionData.expectedPrice.toString(),
            totalPrice: subscriptionData.totalPrice.toString(),
          })}`,
        );

        const subscription = await prisma.subscribe.create({
          data: subscriptionData,
        });

        this.logger.log(
          `Successfully created subscription ${subscription.id} for user ${userId}`,
        );

        // Map to response DTO
        return this.mapToResponseDto(subscription);
      });
    } catch (error) {
      return this.handleServiceError(error, 'create');
    }
  }

  /**
   * Update an existing subscription
   * @param subscriptionId - The ID of the subscription to update
   * @param updateSubscribeDto - Subscription update data
   * @param userId - The ID of the user from token authorization
   * @returns Promise<SubscribeResponseDto> - Updated subscription information
   */
  async update(
    subscriptionId: string,
    updateSubscribeDto: UpdateSubscribeDto,
    userId: string,
  ): Promise<SubscribeResponseDto> {
    try {
      this.logger.log(
        `Updating subscription ${subscriptionId} for user: ${userId} with data: ${JSON.stringify(
          {
            walletId: updateSubscribeDto.wallet_id,
            chainId: updateSubscribeDto.chain_id,
            paymentProvider: updateSubscribeDto.payment_provider,
            expectedPrice: updateSubscribeDto.expected_price.toString(),
            totalPrice: updateSubscribeDto.total_price.toString(),
            currencies: updateSubscribeDto.currencies,
            txHash: updateSubscribeDto.txHash,
          },
        )}`,
      );

      // Validate input data
      this.validateUpdateSubscribeDto(updateSubscribeDto);

      // Validate that the subscription exists and belongs to the user
      await this.validateSubscriptionOwnership(subscriptionId, userId);

      // Validate wallet and chain
      await this.validateWalletExists(updateSubscribeDto.wallet_id, userId);
      await this.validateChainExists(updateSubscribeDto.chain_id);

      // Calculate status_buy and dateCutoff based on current time
      const { statusBuy, dateCutoff } = this.calculateStatusAndCutoff();

      // Update subscription in a transaction for data consistency
      return await this.prisma.$transaction(async (prisma) => {
        const updateData = {
          walletId: updateSubscribeDto.wallet_id,
          chainId: updateSubscribeDto.chain_id,
          paymentProvider: updateSubscribeDto.payment_provider,
          expectedPrice: updateSubscribeDto.expected_price,
          totalPrice: updateSubscribeDto.total_price,
          currencies: updateSubscribeDto.currencies,
          txHash: updateSubscribeDto.txHash || null,
          statusBuy: statusBuy as $Enums.StatusBuy,
          dateCutoff: dateCutoff,
          updatedAt: new Date(Date.now()),
        };

        this.logger.debug(
          `Updating subscription ${subscriptionId} with data: ${JSON.stringify({
            ...updateData,
            expectedPrice: updateData.expectedPrice.toString(),
            totalPrice: updateData.totalPrice.toString(),
          })}`,
        );

        // Generate request number once per subscription (only if missing)
        const existing = await prisma.subscribe.findUnique({
          where: { id: subscriptionId },
          select: { requestNumber: true },
        });

        let subscription: any;
        if (!existing?.requestNumber) {
          // Build today's prefix ORB-YYYY-MM-DD
          const now = new Date();
          const yyyy = now.getFullYear();
          const mm = String(now.getMonth() + 1).padStart(2, '0');
          const dd = String(now.getDate()).padStart(2, '0');
          const prefix = `ORB-${yyyy}-${mm}-${dd}`;

          // Start counter from current count to minimize retries
          const base = await prisma.subscribe.count({
            where: { requestNumber: { startsWith: `${prefix}-` } },
          });

          let attempt = 0;
          const maxAttempts = 20;
          while (attempt < maxAttempts) {
            const candidate = `${prefix}-${base + 1 + attempt}`;
            try {
              subscription = await prisma.subscribe.update({
                where: { id: subscriptionId },
                data: { ...updateData, requestNumber: candidate },
              });
              break; // success
            } catch (e: any) {
              if (e?.code === 'P2002') {
                // Unique constraint violation; try next counter
                attempt++;
                continue;
              }
              throw e;
            }
          }

          if (!subscription) {
            // Fallback (should be rare): update without setting requestNumber
            subscription = await prisma.subscribe.update({
              where: { id: subscriptionId },
              data: updateData,
            });
          }
        } else {
          // Already has a request number; just update fields
          subscription = await prisma.subscribe.update({
            where: { id: subscriptionId },
            data: updateData,
          });
        }

        this.logger.log(
          `Successfully updated subscription ${subscription.id} for user ${userId}`,
        );

        // Map to response DTO
        return this.mapToResponseDto(subscription);
      });
    } catch (error) {
      return this.handleServiceError(error, 'update');
    }
  }

  /**
   * Retrieve subscription status details by subscription ID
   * @param subscriptionId - The ID of the subscription to retrieve status for
   * @param userId - The ID of the user from token authorization
   * @returns Promise<SubscriptionStatusResponseDto> - Subscription status information
   */
  async getSubscriptionStatus(
    subscriptionId: string,
    userId: string,
  ): Promise<SubscriptionStatusResponseDto> {
    try {
      this.logger.log(
        `Retrieving subscription status for subscription ${subscriptionId} for user: ${userId}`,
      );

      // Validate that the subscription exists and belongs to the user
      await this.validateSubscriptionOwnership(subscriptionId, userId);

      // Fetch subscription status data
      const subscription = await this.prisma.subscribe.findUnique({
        where: { id: subscriptionId },
        select: {
          id: true,
          paymentStatus: true,
          checkoutStatus: true,
          subscribeStatus: true,
          dateCutoff: true,
          statusBuy: true,
        },
      });

      // This should never happen due to prior validation, but adding for type safety
      if (!subscription) {
        throw new NotFoundException(
          SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND,
        );
      }

      this.logger.log(
        `Successfully retrieved subscription status for ${subscriptionId}`,
      );

      // Map to response DTO
      return this.mapToStatusResponseDto(subscription);
    } catch (error) {
      return this.handleServiceError(error, 'getStatus');
    }
  }

  /**
   * Get all subscriptions for a specific user with pagination and optional filtering
   * @param query - Query parameters for pagination and filtering
   * @param userId - The ID of the user from token authorization
   * @returns Promise<SubscriptionListApiResponseDto> - Paginated list of subscriptions
   */
  async getSubscriptionsByUser(
    query: SubscriptionListQueryDto,
    userId: string,
  ): Promise<SubscriptionListApiResponseDto> {
    try {
      this.logger.log(
        `Retrieving subscriptions for user: ${userId} with query: ${JSON.stringify(
          {
            page: query.page,
            limit: query.limit,
            productId: query.productId || 'all',
            sortBy: query.sortBy,
            sortOrder: query.sortOrder,
          },
        )}`,
      );

      const {
        page = 1,
        limit = 10,
        productId,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;

      // Build where clause
      const whereClause: any = {
        userId: userId,
      };

      // Add product filter if provided
      if (productId) {
        whereClause.productId = productId;
        this.logger.debug(`Filtering by productId: ${productId}`);
      }

      // Get total count for pagination
      const total = await this.prisma.subscribe.count({
        where: whereClause,
      });

      this.logger.debug(`Total subscriptions found: ${total}`);

      // Get paginated data
      const subscriptions = await this.prisma.subscribe.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: {
          id: true,
          productId: true,
          userId: true,
          walletId: true,
          chainId: true,
          paymentProvider: true,
          paymentStatus: true,
          checkoutStatus: true,
          subscribeStatus: true,
          amount: true,
          expectedPrice: true,
          totalPrice: true,
          statusBuy: true,
          dateCutoff: true,
          currencies: true,
          txHash: true,
          requestNumber: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      this.logger.debug(`Retrieved ${subscriptions.length} subscriptions`);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta = new PaginationMetaDto({
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      });

      // Transform data to response DTOs
      const data = subscriptions.map((subscription) =>
        this.mapToResponseDto(subscription),
      );

      const listResponse = new SubscriptionListResponseDto({
        data,
        meta,
      });

      this.logger.log(
        `Successfully retrieved ${data.length} subscriptions for user ${userId}`,
      );

      return new SubscriptionListApiResponseDto(
        listResponse,
        `Retrieved ${total} subscription${total !== 1 ? 's' : ''} successfully`,
      );
    } catch (error) {
      return this.handleServiceError(error, 'getSubscriptionsByUser');
    }
  }

  /**
   * Validate input data for creating subscription
   * @param createSubscribeDto - Subscription creation data
   * @throws BadRequestException if data is invalid
   */
  private validateCreateSubscribeDto(
    createSubscribeDto: CreateSubscribeDto,
  ): void {
    if (!createSubscribeDto.amount || createSubscribeDto.amount.lte(0)) {
      throw new BadRequestException(SUBSCRIBE_ERROR_MESSAGES.INVALID_AMOUNT);
    }
  }

  /**
   * Validate input data for updating subscription
   * @param updateSubscribeDto - Subscription update data
   * @throws BadRequestException if data is invalid
   */
  private validateUpdateSubscribeDto(
    updateSubscribeDto: UpdateSubscribeDto,
  ): void {
    if (
      !updateSubscribeDto.expected_price ||
      updateSubscribeDto.expected_price.lt(0)
    ) {
      throw new BadRequestException(
        SUBSCRIBE_ERROR_MESSAGES.INVALID_EXPECTED_PRICE,
      );
    }
    if (
      !updateSubscribeDto.total_price ||
      updateSubscribeDto.total_price.lt(0)
    ) {
      throw new BadRequestException(
        SUBSCRIBE_ERROR_MESSAGES.INVALID_TOTAL_PRICE,
      );
    }
  }

  /**
   * Calculate status_buy and dateCutoff based on current time
   * @returns Object with statusBuy and dateCutoff
   */
  private calculateStatusAndCutoff(): { statusBuy: string; dateCutoff: Date } {
    const now = new Date();
    const currentHour = now.getHours();

    // Create a date for today at 2:00 PM
    const todayAt2PM = new Date(now);
    todayAt2PM.setHours(14, 0, 0, 0); // 2:00 PM

    // Create a date for tomorrow at 2:00 PM
    const tomorrowAt2PM = new Date(now);
    tomorrowAt2PM.setDate(now.getDate() + 1);
    tomorrowAt2PM.setHours(14, 0, 0, 0); // 2:00 PM tomorrow

    if (currentHour < 14) {
      // Before 2:00 PM
      return {
        statusBuy: STATUS_BUY.IN_PROCESS,
        dateCutoff: todayAt2PM,
      };
    } else {
      // 2:00 PM or later
      return {
        statusBuy: STATUS_BUY.QUEUED_FOR_NEXT_DAY,
        dateCutoff: tomorrowAt2PM,
      };
    }
  }

  /**
   * Validate that subscription exists and belongs to the user
   * @param subscriptionId - Subscription ID to validate
   * @param userId - User ID to check ownership
   * @throws NotFoundException if subscription doesn't exist or doesn't belong to user
   */
  private async validateSubscriptionOwnership(
    subscriptionId: string,
    userId: string,
  ): Promise<void> {
    const subscription = await this.prisma.subscribe.findUnique({
      where: { id: subscriptionId },
      select: { id: true, userId: true },
    });

    if (!subscription) {
      throw new NotFoundException(
        SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND,
      );
    }

    if (subscription.userId !== userId) {
      throw new NotFoundException(
        SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND,
      );
    }
  }

  /**
   * Validate that user exists in the system
   * @param userId - User ID to validate
   * @throws NotFoundException if user doesn't exist
   */
  private async validateUserExists(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      throw new NotFoundException(SUBSCRIBE_ERROR_MESSAGES.USER_NOT_FOUND);
    }
  }

  /**
   * Validate that product exists in the system
   * @param productId - Product ID to validate
   * @throws NotFoundException if product doesn't exist
   */
  private async validateProductExists(productId: string): Promise<void> {
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      select: { id: true },
    });

    if (!product) {
      throw new NotFoundException(SUBSCRIBE_ERROR_MESSAGES.PRODUCT_NOT_FOUND);
    }
  }

  /**
   * Validate that wallet exists and belongs to the user
   * @param walletId - Wallet ID to validate
   * @param userId - User ID to check ownership
   * @throws NotFoundException if wallet doesn't exist or doesn't belong to user
   */
  private async validateWalletExists(
    walletId: string,
    userId: string,
  ): Promise<void> {
    const wallet = await this.prisma.wallet.findUnique({
      where: { id: walletId },
      select: { id: true, userId: true },
    });

    if (!wallet) {
      throw new NotFoundException(SUBSCRIBE_ERROR_MESSAGES.WALLET_NOT_FOUND);
    }

    if (wallet.userId !== userId) {
      throw new NotFoundException(SUBSCRIBE_ERROR_MESSAGES.WALLET_NOT_FOUND);
    }
  }

  /**
   * Validate that chain exists in the system
   * @param chainId - Chain ID to validate
   * @throws NotFoundException if chain doesn't exist
   */
  private async validateChainExists(chainId: string): Promise<void> {
    const chain = await this.prisma.chain.findUnique({
      where: { id: chainId },
      select: { id: true },
    });

    if (!chain) {
      throw new NotFoundException(SUBSCRIBE_ERROR_MESSAGES.CHAIN_NOT_FOUND);
    }
  }

  /**
   * Map subscription entity to response DTO
   * @param subscription - Subscription entity from database
   * @returns SubscribeResponseDto
   */
  private mapToResponseDto(subscription: any): SubscribeResponseDto {
    return {
      id: subscription.id,
      productId: subscription.productId,
      userId: subscription.userId,
      walletId: subscription.walletId,
      chainId: subscription.chainId,
      paymentProvider: subscription.paymentProvider,
      paymentStatus: subscription.paymentStatus,
      checkoutStatus: subscription.checkoutStatus,
      subscribeStatus: subscription.subscribeStatus,
      amount: subscription.amount,
      expectedPrice: subscription.expectedPrice,
      totalPrice: subscription.totalPrice,
      statusBuy: subscription.statusBuy,
      dateCutoff: subscription.dateCutoff,
      currencies: subscription.currencies,
      txHash: subscription.txHash,
      requestNumber: subscription.requestNumber,
      createdAt: subscription.createdAt,
      updatedAt: subscription.updatedAt,
    };
  }

  /**
   * Map subscription entity to status response DTO
   * @param subscription - Subscription entity from database (status fields only)
   * @returns SubscriptionStatusResponseDto
   */
  private mapToStatusResponseDto(subscription: {
    id: string;
    paymentStatus: string;
    checkoutStatus: string;
    subscribeStatus: string;
    dateCutoff: Date;
    statusBuy: $Enums.StatusBuy;
  }): SubscriptionStatusResponseDto {
    return {
      subscriptionId: subscription.id,
      paymentStatus: subscription.paymentStatus,
      checkoutStatus: subscription.checkoutStatus,
      subscriptionStatus: subscription.subscribeStatus,
      dateCutoff: subscription.dateCutoff,
      statusBuy: subscription.statusBuy,
    };
  }

  /**
   * Enhanced centralized error handling for service methods
   * @param error - The error that occurred
   * @param context - The context/method where the error occurred
   * @throws Appropriate NestJS exception based on error type
   */
  private handleServiceError(error: any, context: string): never {
    // Create detailed error context for logging
    const errorContext = {
      context,
      message: error.message,
      code: error.code,
      meta: error.meta,
      timestamp: new Date().toISOString(),
      stack: error.stack,
    };

    this.logger.error(
      `${context}: ${error.message} (${error.code || 'no_code'})`,
      JSON.stringify(errorContext),
      SubscribeService.name,
    );

    // Re-throw known exceptions as-is
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }

    // Handle validation errors from DTO transformation
    if (error.message?.includes('Amount must be greater than 0')) {
      throw new BadRequestException(SUBSCRIBE_ERROR_MESSAGES.INVALID_AMOUNT);
    }
    if (
      error.message?.includes(
        'Expected price must be greater than or equal to 0',
      )
    ) {
      throw new BadRequestException(
        SUBSCRIBE_ERROR_MESSAGES.INVALID_EXPECTED_PRICE,
      );
    }
    if (
      error.message?.includes('Total price must be greater than or equal to 0')
    ) {
      throw new BadRequestException(
        SUBSCRIBE_ERROR_MESSAGES.INVALID_TOTAL_PRICE,
      );
    }

    // Handle Prisma errors with detailed messages
    if (error.code) {
      switch (error.code) {
        case 'P2002': {
          const target = error.meta?.target || 'unknown field';
          throw new BadRequestException(
            `Duplicate subscription entry for ${target}`,
          );
        }
        case 'P2003': {
          const field = error.meta?.field_name || 'unknown field';
          throw new BadRequestException(
            `Invalid reference data for ${field}. Please check if the referenced record exists.`,
          );
        }
        case 'P2025':
          if (context === 'update' || context === 'getStatus') {
            throw new NotFoundException(
              SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_NOT_FOUND,
            );
          }
          throw new NotFoundException(
            'Record not found during subscription creation',
          );
        case 'P2000':
          throw new BadRequestException(
            'The provided value for one or more fields is too long',
          );
        case 'P2001':
          throw new NotFoundException(
            'The record searched for in the where condition does not exist',
          );
        default:
          this.logger.error(
            `Unhandled Prisma error: ${error.code}`,
            errorContext,
          );
          throw new InternalServerErrorException(
            `Database error occurred: ${error.code}`,
          );
      }
    }

    // Handle decimal conversion errors
    if (error.message?.includes('Decimal')) {
      throw new BadRequestException(
        'Invalid decimal format for amount. Please provide a valid number.',
      );
    }

    // Default to internal server error with more context
    this.logger.error('Unhandled error in subscription service', errorContext);
    let defaultErrorMessage: string;
    switch (context) {
      case 'update':
        defaultErrorMessage =
          SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_UPDATE_FAILED;
        break;
      case 'getStatus':
        defaultErrorMessage =
          SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_STATUS_RETRIEVAL_FAILED;
        break;
      default:
        defaultErrorMessage =
          SUBSCRIBE_ERROR_MESSAGES.SUBSCRIPTION_CREATE_FAILED;
    }
    throw new InternalServerErrorException(defaultErrorMessage);
  }
}
