import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ProductResponseDto, ProductSummaryDto } from './dto';
import { BaseResponseDto } from '../backoffice/nav-management/dto/base-response.dto';

@Injectable()
export class ProductsService {
  constructor(private readonly prisma: PrismaService) {}

  async findOne(id: string): Promise<BaseResponseDto<ProductResponseDto>> {
    try {
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          supportedNetworks: true,
          productTags: {
            include: {
              tag: {
                include: {
                  category: true,
                },
              },
            },
            orderBy: {
              id: 'asc',
            },
          },
        },
      });

      if (!product) {
        throw new NotFoundException('Product not found');
      }

      const productResponse = this.transformProductToResponse(product);

      return BaseResponseDto.success(
        productResponse,
        'Product retrieved successfully',
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('ProductsService Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while retrieving product',
      );
    }
  }

  async findAll(): Promise<BaseResponseDto<ProductSummaryDto[]>> {
    try {
      const products = await this.prisma.product.findMany({
        include: {
          productTags: {
            include: {
              tag: {
                include: {
                  category: true,
                },
              },
            },
            orderBy: {
              id: 'asc',
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (!products || products.length === 0) {
        throw new NotFoundException('No products found');
      }

      const productSummaries = products.map((product) =>
        this.transformProductToSummary(product),
      );

      return BaseResponseDto.success(
        productSummaries,
        'Products retrieved successfully',
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('ProductsService Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while retrieving products',
      );
    }
  }

  private transformProductToResponse(product: any): ProductResponseDto {
    // Transform tags directly from the included productTags relation
    const tags = product.productTags.map((productTag: any) => {
      const tag = productTag.tag;
      const category = tag.category;
      return {
        categoryLayer: category.layer,
        categorySlug: category.slug,
        categoryLabel: category.label,
        tagSlug: tag.slug,
        tagLabel: tag.label,
      };
    });

    const supportedNetworks =
      product.supportedNetworks?.map((network: any) => ({
        id: network.id,
        networkName: network.networkName,
        networkCode: network.networkCode,
        chainId: network.chainId,
        address: network.address,
        decimals: network.decimals,
        img: network.img,
        createdAt: network.createdAt.toISOString(),
        updatedAt: network.updatedAt.toISOString(),
      })) || [];

    return {
      id: product.id,
      displayName: product.displayName,
      tokenName: product.tokenName,
      underlyingName: product.underlyingName,
      symbol: product.symbol,
      img: product.img || 'https://orbitum.com/img/orbitum.png',
      description: product.description,
      price: Number(product.price),
      high: Number(product.high || product.price),
      low: Number(product.low || product.price),
      priceChange24h: Number(product.priceChange24h),
      priceChangePct24h: Number(product.priceChangePct24h),
      apy: product.apy ? Number(product.apy) : undefined,
      tvl: product.tvl ? Number(product.tvl) : undefined,
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt
        ? product.updatedAt.toISOString()
        : undefined,
      tags,
      supportedNetworks,
    };
  }

  private transformProductToSummary = (product: any): ProductSummaryDto => {
    // Transform tags directly from the included productTags relation
    const tags = product.productTags.map((productTag: any) => {
      const tag = productTag.tag;
      const category = tag.category;
      return {
        categoryLayer: category.layer,
        categorySlug: category.slug,
        categoryLabel: category.label,
        tagSlug: tag.slug,
        tagLabel: tag.label,
      };
    });

    return {
      id: product.id,
      displayName: product.displayName,
      tokenName: product.tokenName,
      symbol: product.symbol,
      img: product.img || 'https://orbitum.com/img/orbitum.png',
      price: Number(product.price),
      priceChange24h: Number(product.priceChange24h),
      priceChangePct24h: Number(product.priceChangePct24h),
      apy: product.apy ? Number(product.apy) : undefined,
      createdAt: product.createdAt.toISOString(),
      tags,
    } as ProductSummaryDto;
  };
}
