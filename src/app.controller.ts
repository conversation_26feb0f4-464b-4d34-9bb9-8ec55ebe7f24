import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Health')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get application status' })
  @ApiResponse({ status: 200, description: 'Application is running' })
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('db-health')
  @ApiOperation({ summary: 'Database health check endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Application and database are healthy',
  })
  @ApiResponse({
    status: 503,
    description: 'Service unavailable - database connection failed',
  })
  async getDatabaseHealth() {
    return await this.appService.getHealth();
  }
}
