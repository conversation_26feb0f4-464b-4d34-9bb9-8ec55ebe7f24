import {
  Injectable,
  OnModuleInit,
  OnM<PERSON>ule<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { PrismaClient } from '../../generated/prisma';
import {
  getDatabaseConfig,
  createPrismaClientOptions,
  DatabaseConfig,
} from '../config/database.config';

/**
 * PrismaService with connection pooling support
 * Implements best practices for database connection management in NestJS
 *
 * Features:
 * - Connection pooling with configurable pool size
 * - Automatic retry mechanism with exponential backoff
 * - Health check capabilities
 * - Graceful shutdown handling
 * - Comprehensive logging and monitoring
 */
@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(PrismaService.name);
  private readonly databaseConfig: DatabaseConfig;
  private connectionRetries = 0;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  constructor() {
    // Load and validate database configuration
    const dbConfig = getDatabaseConfig();
    const prismaOptions = createPrismaClientOptions(dbConfig);

    // Initialize PrismaClient with connection pooling configuration
    super(prismaOptions);

    // Store config for reference
    (this as any).databaseConfig = dbConfig;

    // Log connection pool configuration
    if (dbConfig.enableLogging) {
      this.logger.log(
        `🔧 Connection Pool Configuration:\n` +
          `   - Max Connections: ${dbConfig.connectionPool.max}\n` +
          `   - Min Connections: ${dbConfig.connectionPool.min}\n` +
          `   - Connection Timeout: ${dbConfig.connectionPool.timeout}ms\n` +
          `   - Idle Timeout: ${dbConfig.connectionPool.idleTimeoutMillis}ms\n` +
          `   - Query Timeout: ${dbConfig.queryTimeout}ms`,
      );
    }

    // Set up event listeners for monitoring and debugging
    this.setupEventListeners();
  }

  /**
   * Setup event listeners for database monitoring
   * Provides insights into query performance and issues
   */
  private setupEventListeners(): void {
    const dbConfig = (this as any).databaseConfig as DatabaseConfig;

    // Query event logging (only in development for performance)
    this.$on('query', (e) => {
      if (dbConfig.enableLogging) {
        const duration = e.duration;
        const logLevel = duration > 1000 ? 'warn' : 'debug';

        this.logger[logLevel](
          `📊 Query executed in ${duration}ms: ${e.query.substring(0, 100)}${e.query.length > 100 ? '...' : ''}`,
        );
      }
    });

    // Error event logging
    this.$on('error', (e) => {
      this.logger.error('❌ Database error occurred:', e);
    });

    // Warning event logging
    this.$on('warn', (e) => {
      this.logger.warn('⚠️  Database warning:', e);
    });

    // Info event logging
    this.$on('info', (e) => {
      this.logger.log('ℹ️  Database info:', e);
    });
  }

  /**
   * Initialize database connection when module starts
   * Implements retry logic for resilient connections
   */
  async onModuleInit(): Promise<void> {
    const dbConfig = (this as any).databaseConfig as DatabaseConfig;

    this.logger.log(
      `🚀 Initializing database connection with pool size: ${dbConfig.connectionPool.max}`,
    );

    await this.connectWithRetry();
  }

  /**
   * Connect to database with retry logic and exponential backoff
   * Implements resilient connection handling
   */
  private async connectWithRetry(): Promise<void> {
    const dbConfig = (this as any).databaseConfig as DatabaseConfig;
    const connectionTimeout = dbConfig.connectionPool.timeout || 20000;

    while (this.connectionRetries < this.maxRetries) {
      try {
        this.logger.log(
          `🔌 Attempting to connect to database... (Attempt ${this.connectionRetries + 1}/${this.maxRetries})`,
        );

        // Test connection with configurable timeout
        await Promise.race([
          this.$connect(),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error('Connection timeout')),
              connectionTimeout,
            ),
          ),
        ]);

        // Verify connection with a simple query
        await this.$queryRaw`SELECT 1`;

        // Get connection pool statistics
        const poolStats = await this.getConnectionPoolStats();

        this.logger.log(
          `✅ Database connected successfully\n` +
            `   - Active Connections: ${poolStats.activeConnections}\n` +
            `   - Idle Connections: ${poolStats.idleConnections}`,
        );

        this.connectionRetries = 0; // Reset retry counter on success
        return;
      } catch (error) {
        this.connectionRetries++;
        const delay = this.retryDelay * Math.pow(2, this.connectionRetries - 1); // Exponential backoff

        this.logger.error(
          `❌ Failed to connect to database (Attempt ${this.connectionRetries}/${this.maxRetries}):`,
          error.message,
        );

        if (this.connectionRetries >= this.maxRetries) {
          this.logger.error(
            '❌ Max connection retries reached. Database connection failed.',
          );
          // Don't throw error during startup to allow health checks to work
          // The connection will be retried on first use
          break;
        }

        // Wait before retrying with exponential backoff
        this.logger.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Gracefully disconnect from database when module is destroyed
   * Ensures all connections are properly closed
   */
  async onModuleDestroy(): Promise<void> {
    try {
      this.logger.log('🔌 Gracefully disconnecting from database...');

      // Get final pool statistics before disconnecting
      const poolStats = await this.getConnectionPoolStats().catch(() => ({
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0,
      }));

      this.logger.log(
        `📊 Final connection pool statistics:\n` +
          `   - Active: ${poolStats.activeConnections}\n` +
          `   - Idle: ${poolStats.idleConnections}\n` +
          `   - Total: ${poolStats.totalConnections}`,
      );

      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database:', error);
      throw error;
    }
  }

  /**
   * Health check method for monitoring
   * Returns true if database is accessible and responsive
   */
  async isHealthy(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('❌ Database health check failed:', error);
      return false;
    }
  }

  /**
   * Reconnect method for manual retry
   * Useful for recovering from connection issues
   */
  async reconnect(): Promise<void> {
    try {
      this.logger.log('🔄 Manually reconnecting to database...');
      await this.$disconnect();
      this.connectionRetries = 0;
      await this.connectWithRetry();
    } catch (error) {
      this.logger.error('❌ Failed to reconnect to database:', error);
      throw error;
    }
  }

  /**
   * Get connection pool statistics
   * Useful for monitoring and debugging
   */
  async getConnectionPoolStats(): Promise<{
    activeConnections: number;
    idleConnections: number;
    totalConnections: number;
  }> {
    try {
      const result = await this.$queryRaw<Array<{ count: bigint }>>`
        SELECT count(*) as count 
        FROM pg_stat_activity 
        WHERE datname = current_database()
        AND pid != pg_backend_pid()
      `;

      const totalConnections = Number(result[0]?.count || 0);

      // Get active connections
      const activeResult = await this.$queryRaw<Array<{ count: bigint }>>`
        SELECT count(*) as count 
        FROM pg_stat_activity 
        WHERE datname = current_database()
        AND pid != pg_backend_pid()
        AND state = 'active'
      `;

      const activeConnections = Number(activeResult[0]?.count || 0);
      const idleConnections = totalConnections - activeConnections;

      return {
        activeConnections,
        idleConnections,
        totalConnections,
      };
    } catch (error) {
      this.logger.warn('⚠️  Could not fetch connection pool stats:', error);
      return {
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0,
      };
    }
  }

  /**
   * Get database configuration
   * Returns the current database configuration for reference
   */
  getDatabaseConfig(): DatabaseConfig {
    return (this as any).databaseConfig;
  }
}
