import { ApiProperty } from '@nestjs/swagger';
import {
  UserResponseDto,
  UserType,
  BusinessType,
  Status,
} from './user-response.dto';
import { WalletResponseDto } from '../../wallet/dto/wallet-response.dto';
import { SupportedChainDto } from '../../wallet/dto/supported-chain.dto';

/**
 * Enhanced user profile response DTO that includes comprehensive wallet and blockchain chain data
 * Provides detailed wallet information with full chain details and supported blockchain networks
 * Follows clean code principles with proper composition and TypeScript typing
 */
export class EnhancedUserProfileResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  id: string;

  @ApiProperty({
    description: 'First name of the user',
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    description: 'Last name of the user',
    example: '<PERSON><PERSON>',
  })
  lastName: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Username of the user',
    example: 'johndoe',
  })
  username: string;

  @ApiProperty({
    enum: UserType,
    description: 'Type of the user',
    example: UserType.CONSUMER,
  })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    description: 'Business type for business users',
    example: BusinessType.RETAIL,
  })
  businessType?: BusinessType;

  @ApiProperty({
    enum: Status,
    description: 'Current status of the user',
    example: Status.ACTIVE,
  })
  status: Status;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Applicant ID for KYC/KYB processes',
    example: 'APP_123456789',
    required: false,
  })
  applicantId: string;

  @ApiProperty({
    description:
      'Indicates whether the user has completed the initial setup/onboarding process',
    example: false,
    default: false,
  })
  isInitiated: boolean;

  @ApiProperty({
    description: 'Comprehensive wallet information with detailed chain data',
    type: [WalletResponseDto],
    isArray: true,
    example: [
      {
        id: 'wallet-uuid',
        user_id: 'user-uuid',
        wallet_address: '******************************************',
        wallet_type: 'METAMASK',
        is_primary: true,
        status: 'ACTIVE',
        created_at: '2024-01-15T10:30:00.000Z',
        chains: [
          {
            id: 'chain-uuid',
            chain_id: '1',
            chain_name: 'Ethereum Mainnet',
            symbol: 'ETH',
            wallet_id: 'wallet-uuid',
            created_at: '2024-01-15T10:30:00.000Z',
          },
        ],
      },
    ],
  })
  wallets: WalletResponseDto[];

  @ApiProperty({
    description: 'All supported blockchain networks available in the platform',
    type: [SupportedChainDto],
    isArray: true,
    example: [
      {
        chainId: '1',
        chainName: 'Ethereum Mainnet',
        symbol: 'ETH',
        isMainnet: true,
        rpcUrls: ['https://mainnet.infura.io/v3/'],
        blockExplorerUrls: ['https://etherscan.io'],
        nativeCurrency: {
          name: 'Ether',
          symbol: 'ETH',
          decimals: 18,
        },
      },
    ],
  })
  supportedChains: SupportedChainDto[];

  /**
   * Constructor to create EnhancedUserProfileResponseDto from user data, wallets, and supported chains
   * @param userData - User data from UserResponseDto
   * @param wallets - Array of detailed user wallets with chain information
   * @param supportedChains - Array of all supported blockchain networks
   */
  constructor(
    userData: UserResponseDto,
    wallets: WalletResponseDto[] = [],
    supportedChains: SupportedChainDto[] = [],
  ) {
    this.id = userData.id;
    this.firstName = userData.firstName;
    this.lastName = userData.lastName;
    this.email = userData.email;
    this.username = userData.username;
    this.type = userData.type;
    this.businessType = userData.businessType;
    this.status = userData.status;
    this.createdAt = userData.createdAt;
    this.updatedAt = userData.updatedAt;
    this.applicantId = userData.applicantId;
    this.isInitiated = userData.isInitiated;
    this.wallets = wallets;
    this.supportedChains = supportedChains;
  }

  /**
   * Static factory method to create EnhancedUserProfileResponseDto
   * Provides a clean way to construct the response with proper typing
   */
  static create(
    userData: UserResponseDto,
    wallets: WalletResponseDto[] = [],
    supportedChains: SupportedChainDto[] = [],
  ): EnhancedUserProfileResponseDto {
    return new EnhancedUserProfileResponseDto(
      userData,
      wallets,
      supportedChains,
    );
  }
}
