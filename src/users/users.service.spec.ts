import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { UsersService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { WalletService } from '../wallet/wallet.service';
import { EmailService } from '../email/email.service';
import { UserType, BusinessType, Status } from './dto/user-response.dto';

describe('UsersService - Enhanced Profile', () => {
  let service: UsersService;
  let prismaService: PrismaService;
  let walletService: WalletService;

  const mockUser = {
    id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    username: 'johndoe',
    type: 'CONSUMER',
    businessType: 'RETAIL',
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    applicantId: 'APP_123456789',
    isInitiated: false, // Test the new field
    password: 'hashedPassword',
  };

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
    },
  };

  const mockWalletService = {
    getUserWalletsDetailed: jest.fn(),
    getSupportedChains: jest.fn(),
  };

  const mockEmailService = {
    sendActivationEmail: jest.fn(),
    sendPasswordResetEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: WalletService,
          useValue: mockWalletService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prismaService = module.get<PrismaService>(PrismaService);
    walletService = module.get<WalletService>(WalletService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getEnhancedProfile', () => {
    it('should return enhanced profile with isInitiated field', async () => {
      // Arrange
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockWalletService.getUserWalletsDetailed.mockResolvedValue([]);
      mockWalletService.getSupportedChains.mockResolvedValue([]);

      // Act
      const result = await service.getEnhancedProfile(
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '<EMAIL>',
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe('f47ac10b-58cc-4372-a567-0e02b2c3d479');
      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
      expect(result.email).toBe('<EMAIL>');
      expect(result.username).toBe('johndoe');
      expect(result.type).toBe(UserType.CONSUMER);
      expect(result.businessType).toBe(BusinessType.RETAIL);
      expect(result.status).toBe(Status.ACTIVE);
      expect(result.applicantId).toBe('APP_123456789');
      expect(result.isInitiated).toBe(false); // Verify the new field is included
      expect(result.wallets).toEqual([]);
      expect(result.supportedChains).toEqual([]);
    });

    it('should return enhanced profile with isInitiated true when user is initiated', async () => {
      // Arrange
      const initiatedUser = { ...mockUser, isInitiated: true };
      mockPrismaService.user.findUnique.mockResolvedValue(initiatedUser);
      mockWalletService.getUserWalletsDetailed.mockResolvedValue([]);
      mockWalletService.getSupportedChains.mockResolvedValue([]);

      // Act
      const result = await service.getEnhancedProfile(
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '<EMAIL>',
      );

      // Assert
      expect(result.isInitiated).toBe(true); // Verify the field reflects the database value
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getEnhancedProfile(
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
          '<EMAIL>',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException when account is not active', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, status: 'PENDING' };
      mockPrismaService.user.findUnique.mockResolvedValue(inactiveUser);

      // Act & Assert
      await expect(
        service.getEnhancedProfile(
          'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          '<EMAIL>',
        ),
      ).rejects.toThrow(ForbiddenException);
    });
  });
});
