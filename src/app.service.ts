import { Injectable, HttpStatus } from '@nestjs/common';
import { PrismaService } from './prisma/prisma.service';

@Injectable()
export class AppService {
  constructor(private prisma: PrismaService) {}

  getHello(): string {
    return 'Hello World!';
  }

  // Health check method with database connectivity test
  async getHealth() {
    const startTime = Date.now();

    try {
      // Test database connectivity
      const isDbHealthy = await this.prisma.isHealthy();
      const responseTime = Date.now() - startTime;

      if (isDbHealthy) {
        return {
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          database: {
            status: 'connected',
            responseTime: `${responseTime}ms`,
          },
          memory: {
            used:
              Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) /
              100,
            total:
              Math.round(
                (process.memoryUsage().heapTotal / 1024 / 1024) * 100,
              ) / 100,
          },
          environment: process.env.NODE_ENV || 'development',
        };
      } else {
        return {
          status: 'error',
          timestamp: new Date().toISOString(),
          database: {
            status: 'disconnected',
            responseTime: `${responseTime}ms`,
          },
          message: 'Database connection failed',
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        database: {
          status: 'error',
          responseTime: `${responseTime}ms`,
          error: error.message,
        },
        message: 'Health check failed',
      };
    }
  }

  // Example method using Prisma
  async getUsers() {
    // Example Prisma query (uncomment and modify as needed)
    // return await this.prisma.user.findMany();
    return [];
  }
}
