import {
  Controller,
  Post,
  Put,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { CurrentUser } from '../auth/decorators/user-info.decorator';
import { UserInfo } from '../auth/interfaces/user-info.interface';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';
import { RedeemService } from './redeem.service';
import { InitiateRedeemDto } from './dto/initiate-redeem.dto';
import { UpdateRedeemDto } from './dto/update-redeem.dto';
import { RedeemResponseDto } from './dto/redeem-response.dto';
import { UpdateRedeemResponseDto } from './dto/update-redeem-response.dto';

/**
 * Controller for handling redeem operations
 * Follows the same architectural pattern as the subscribe controller
 */
@ApiTags('Redeem')
@Controller('redeem')
@UseGuards(EnhancedJwtAuthGuard)
@ApiBearerAuth('JWT')
export class RedeemController {
  private readonly logger = new Logger(RedeemController.name);

  constructor(private readonly redeemService: RedeemService) {}

  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Initiate a new redeem request',
    description:
      'Initiates a new redeem request for multiple subscriptions with the specified total amount',
  })
  @ApiBody({ type: InitiateRedeemDto })
  @ApiResponse({
    status: 201,
    description: 'Redeem request successfully initiated',
    type: RedeemResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more subscriptions not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async initiate(
    @Body() initiateRedeemDto: InitiateRedeemDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<RedeemResponseDto> {
    this.logger.log(`Initiating redeem for user: ${userInfo.id}`);

    return await this.redeemService.initiate(initiateRedeemDto, userInfo.id);
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update an existing redeem',
    description:
      'Updates an existing redeem with new total amount and status information. Automatically calculates status_buy and dateCutoff based on current time and generates request number.',
  })
  @ApiBody({ type: UpdateRedeemDto })
  @ApiResponse({
    status: 200,
    description: 'Redeem successfully updated',
    type: UpdateRedeemResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Redeem not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async update(
    @Param('id') redeemId: string,
    @Body() updateRedeemDto: UpdateRedeemDto,
    @CurrentUser() userInfo: UserInfo,
  ): Promise<UpdateRedeemResponseDto> {
    this.logger.log(`Updating redeem ${redeemId} for user: ${userInfo.id}`);

    return await this.redeemService.update(
      redeemId,
      updateRedeemDto,
      userInfo.id,
    );
  }
}
