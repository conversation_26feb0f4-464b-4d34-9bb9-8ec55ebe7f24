import { ApiProperty } from '@nestjs/swagger';

export class RedeemResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 201,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Redeem request initiated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Redeem data',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440000',
    },
  })
  data: {
    id: string;
  };
}

