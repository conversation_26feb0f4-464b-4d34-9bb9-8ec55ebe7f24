import {
  IsString,
  ValidateBy,
  ValidationOptions,
  IsUUID,
  IsNotEmpty,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty } from '@nestjs/swagger';

// Custom validator for Decimal that accepts both string and number
function IsDecimalAmount(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isDecimalAmount',
      validator: {
        validate(value: any): boolean {
          if (value === null || value === undefined) {
            return false;
          }

          // Accept both string and number
          if (typeof value === 'string' || typeof value === 'number') {
            try {
              const decimal = new Decimal(value.toString());
              return decimal.gt(0) && decimal.lte(999999999.99);
            } catch {
              return false;
            }
          }

          // If it's already a Decimal instance
          if (value instanceof Decimal) {
            return value.gt(0) && value.lte(999999999.99);
          }

          return false;
        },
        defaultMessage(): string {
          return 'Amount must be a positive number up to 999999999.99 with maximum 8 decimal places';
        },
      },
    },
    validationOptions,
  );
}

/**
 * Data Transfer Object for updating redeem requests.
 * This DTO follows the same pattern as UpdateSubscribeDto, excluding status fields
 * that are managed internally by the system.
 */
export class UpdateRedeemDto {
  @ApiProperty({
    description: 'Total redeem amount (must be positive)',
    example: 3500,
    type: 'number',
    minimum: 0.01,
    maximum: 999999999.99,
  })
  @IsDecimalAmount({
    message:
      'Total amount must be a positive number up to 999999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lte(0)) {
        throw new Error('Total amount must be greater than 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid total amount format');
  })
  totalAmount: Decimal;

  @ApiProperty({
    description: 'Wallet ID for the redeem',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Wallet ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Wallet ID is required' })
  wallet_id: string;

  @ApiProperty({
    description: 'Chain ID for blockchain network',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Chain ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Chain ID is required' })
  chain_id: string;

  @ApiProperty({
    description: 'Payment provider for the redeem',
    example: 'stripe',
    enum: ['stripe', 'paypal', 'coinbase', 'metamask'],
  })
  @IsString({ message: 'Payment provider must be a string' })
  @IsNotEmpty({ message: 'Payment provider is required' })
  payment_provider: string;

  @ApiProperty({
    description: 'Currency code for the payment',
    example: 'USD',
  })
  @IsString({ message: 'Currencies must be a string' })
  @IsNotEmpty({ message: 'Currencies is required' })
  currencies: string;
}
