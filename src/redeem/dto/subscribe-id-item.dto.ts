import { IsUUID, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SubscribeIdItemDto {
  @ApiProperty({
    description: 'Unique identifier of the subscription',
    example: '550e8400-e29b-41d4-a716-************',
    format: 'uuid',
  })
  @IsUUID('all', { message: 'Subscribe ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Subscribe ID is required' })
  id: string;
}

