import {
  IsArray,
  IsNotEmpty,
  ValidateNested,
  ArrayMinSize,
  ValidateBy,
  ValidationOptions,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { Decimal } from '@prisma/client/runtime/library';
import { ApiProperty } from '@nestjs/swagger';
import { SubscribeIdItemDto } from './subscribe-id-item.dto';

// Custom validator for Decimal that accepts both string and number
function IsDecimalAmount(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isDecimalAmount',
      validator: {
        validate(value: any): boolean {
          if (value === null || value === undefined) {
            return false;
          }

          // Accept both string and number
          if (typeof value === 'string' || typeof value === 'number') {
            try {
              const decimal = new Decimal(value.toString());
              return decimal.gt(0) && decimal.lte(999999999.99);
            } catch {
              return false;
            }
          }

          // If it's already a Decimal instance
          if (value instanceof Decimal) {
            return value.gt(0) && value.lte(999999999.99);
          }

          return false;
        },
        defaultMessage(): string {
          return 'Amount must be a positive number up to 999999999.99 with maximum 8 decimal places';
        },
      },
    },
    validationOptions,
  );
}

export class InitiateRedeemDto {
  @ApiProperty({
    description: 'Array of subscription IDs to redeem',
    example: [
      { id: '550e8400-e29b-41d4-a716-446655440000' },
      { id: '550e8400-e29b-41d4-a716-446655440001' },
      { id: '550e8400-e29b-41d4-a716-446655440002' },
    ],
    type: [SubscribeIdItemDto],
  })
  @IsArray({ message: 'subscribe_id must be an array' })
  @ArrayMinSize(1, { message: 'At least one subscription ID is required' })
  @ValidateNested({ each: true })
  @Type(() => SubscribeIdItemDto)
  @IsNotEmpty({ message: 'subscribe_id is required' })
  subscribe_id: SubscribeIdItemDto[];

  @ApiProperty({
    description: 'Total redeem amount (must be positive)',
    example: 3500,
    type: 'number',
    minimum: 0.01,
    maximum: 999999999.99,
  })
  @IsDecimalAmount({
    message:
      'Amount must be a positive number up to 999999999.99 with maximum 8 decimal places',
  })
  @Transform(({ value }) => {
    if (typeof value === 'string' || typeof value === 'number') {
      const decimal = new Decimal(value.toString());
      if (decimal.lte(0)) {
        throw new Error('Amount must be greater than 0');
      }
      return decimal;
    }
    if (value instanceof Decimal) {
      return value;
    }
    throw new Error('Invalid amount format');
  })
  amount: Decimal;
}
