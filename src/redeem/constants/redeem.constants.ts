/**
 * Constants for Redeem service
 */

export const REDEEM_ERROR_MESSAGES = {
  SUBSCRIBE_NOT_FOUND: 'One or more subscriptions not found',
  SUBSCRIBE_INVALID: 'Invalid subscription ID format',
  REDEEM_CREATE_FAILED: 'Failed to create redeem request',
  REDEEM_NOT_FOUND: 'Redeem request not found',
  INVALID_AMOUNT: 'Invalid redeem amount',
  EMPTY_SUBSCRIBE_LIST: 'At least one subscription ID is required',
  DUPLICATE_SUBSCRIBE_IDS: 'Duplicate subscription IDs are not allowed',
  INVALID_PAYMENT_STATUS: 'Invalid payment status',
  INVALID_CHECKOUT_STATUS: 'Invalid checkout status',
  INVALID_REDEEM_STATUS: 'Invalid redeem status',
  REDEEM_UPDATE_FAILED: 'Failed to update redeem request',
} as const;

export const REDEEM_SUCCESS_MESSAGES = {
  REDEEM_INITIATED: 'Redeem request initiated successfully',
  REDEEM_UPDATED: 'Redeem request updated successfully',
} as const;

export const PAYMENT_STATUS = {
  PAID: 'PAID',
  UNPAID: 'UNPAID',
  PENDING: 'PENDING',
  FAILED: 'FAILED',
} as const;

export const CHECKOUT_STATUS = {
  PREPARED: 'PREPARED',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
} as const;

export const REDEEM_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  CANCELLED: 'CANCELLED',
  EXPIRED: 'EXPIRED',
} as const;

export const STATUS_BUY = {
  IN_PROCESS: 'IN_PROCESS',
  QUEUED_FOR_NEXT_DAY: 'QUEUED_FOR_NEXT_DAY',
} as const;

export const DEFAULT_VALUES = {
  PAYMENT_STATUS: PAYMENT_STATUS.UNPAID,
  CHECKOUT_STATUS: CHECKOUT_STATUS.PREPARED,
  REDEEM_STATUS: REDEEM_STATUS.ACTIVE,
  STATUS_BUY: 'IN_PROCESS' as const,
  // Default date cutoff to 30 days from now
  DATE_CUTOFF_DAYS: 30,
} as const;
