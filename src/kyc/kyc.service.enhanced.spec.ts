import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, ConflictException } from '@nestjs/common';
import { KycService } from './kyc.service';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKycDto, IdentityType, KycStatus } from './dto/create-kyc.dto';

describe('KycService - Enhanced Initiate Flow', () => {
  let service: KycService;
  let prismaService: PrismaService;

  const mockUser = {
    id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    username: 'johndo<PERSON>',
    type: 'CONSUMER',
    businessType: 'RETAIL',
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    applicantId: null,
    isInitiated: false,
    password: 'hashedPassword',
  };

  const mockKycData = {
    id: 'kyc-123',
    identityType: 'KTP',
    identityNumber: '3201234567890123',
    birthdate: new Date('1990-01-15'),
    birthplace: 'Jakarta',
    address: 'Jl. Sudirman No. 123',
    state: 'DKI Jakarta',
    country: 'Indonesia',
    zipNumber: '12345',
    phoneNumber: '+6281234567890',
    fileName: 'ktp_scan.jpg',
    provider: 'MANUAL',
    userId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    status: 'PENDING',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  const mockCreateKycDto: CreateKycDto = {
    identityType: IdentityType.KTP,
    identityNumber: '3201234567890123',
    birthdate: '1990-01-15',
    birthplace: 'Jakarta',
    address: 'Jl. Sudirman No. 123',
    state: 'DKI Jakarta',
    country: 'Indonesia',
    zipNumber: '12345',
    phoneNumber: '+6281234567890',
    fileName: 'ktp_scan.jpg',
    provider: 'MANUAL',
    status: KycStatus.PENDING,
    applicantId: 'SUMSUB_APP_12345',
  };

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
    },
    kyc: {
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KycService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<KycService>(KycService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initiateKyc - Enhanced with isInitiated', () => {
    it('should set isInitiated to true when KYC is initiated', async () => {
      // Arrange
      const updatedUser = {
        ...mockUser,
        isInitiated: true,
        applicantId: 'SUMSUB_APP_12345',
      };

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockPrismaTransaction = {
          user: {
            update: jest.fn().mockResolvedValue(updatedUser),
            findUnique: jest.fn().mockResolvedValue(updatedUser),
            findFirst: jest.fn().mockResolvedValue(null),
          },
          kyc: {
            create: jest.fn().mockResolvedValue(mockKycData),
          },
        };
        return callback(mockPrismaTransaction);
      });

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.kyc.findFirst.mockResolvedValue(null);
      mockPrismaService.kyc.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.initiateKyc(
        mockCreateKycDto,
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.isInitiated).toBe(true); // Verify the new field is included and set to true
      expect(result.applicantId).toBe('SUMSUB_APP_12345');
      expect(result.status).toBe(KycStatus.PENDING);
      expect(result.userId).toBe('f47ac10b-58cc-4372-a567-0e02b2c3d479');

      // Verify that user.update was called with isInitiated: true
      expect(mockPrismaService.$transaction).toHaveBeenCalled();
    });

    it('should set isInitiated to true even without applicantId', async () => {
      // Arrange
      const kycDtoWithoutApplicantId = { ...mockCreateKycDto };
      delete kycDtoWithoutApplicantId.applicantId;

      const updatedUser = { ...mockUser, isInitiated: true };

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockPrismaTransaction = {
          user: {
            update: jest.fn().mockResolvedValue(updatedUser),
            findUnique: jest.fn().mockResolvedValue(updatedUser),
            findFirst: jest.fn().mockResolvedValue(null),
          },
          kyc: {
            create: jest.fn().mockResolvedValue(mockKycData),
          },
        };
        return callback(mockPrismaTransaction);
      });

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.kyc.findFirst.mockResolvedValue(null);
      mockPrismaService.kyc.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.initiateKyc(
        kycDtoWithoutApplicantId,
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.isInitiated).toBe(true); // Verify isInitiated is still set to true
      expect(result.applicantId).toBeNull(); // No applicantId provided
    });

    it('should handle user already initiated scenario', async () => {
      // Arrange
      const alreadyInitiatedUser = { ...mockUser, isInitiated: true };
      const updatedUser = {
        ...alreadyInitiatedUser,
        applicantId: 'SUMSUB_APP_12345',
      };

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockPrismaTransaction = {
          user: {
            update: jest.fn().mockResolvedValue(updatedUser),
            findUnique: jest.fn().mockResolvedValue(updatedUser),
            findFirst: jest.fn().mockResolvedValue(null),
          },
          kyc: {
            create: jest.fn().mockResolvedValue(mockKycData),
          },
        };
        return callback(mockPrismaTransaction);
      });

      mockPrismaService.user.findUnique.mockResolvedValue(alreadyInitiatedUser);
      mockPrismaService.kyc.findFirst.mockResolvedValue(null);
      mockPrismaService.kyc.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.initiateKyc(
        mockCreateKycDto,
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.isInitiated).toBe(true); // Should remain true
      expect(result.applicantId).toBe('SUMSUB_APP_12345');
    });
  });
});
