import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ValidationPipe,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { KycService } from './kyc.service';
import { CreateKycDto } from './dto/create-kyc.dto';
import { KycResponseDto } from './dto/kyc-response.dto';
import { EnhancedJwtAuthGuard } from '../auth/guards/enhanced-jwt-auth.guard';
import { UserExtractionUtil } from './utils/user-extraction.util';
import { AuthenticatedRequest } from './interfaces/authenticated-user.interface';

@ApiTags('KYC (Know Your Customer)')
@Controller('kyc')
@UseGuards(EnhancedJwtAuthGuard)
@ApiBearerAuth('JWT')
export class KycController {
  private readonly logger = new Logger(KycController.name);

  constructor(private readonly kycService: KycService) {}

  @Post('initiate')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Initiate KYC process',
    description:
      'Initiates the Know Your Customer verification process for the authenticated user. This endpoint creates a new KYC record and can integrate with third-party providers like Sumsub.',
  })
  @ApiBody({ type: CreateKycDto })
  @ApiResponse({
    status: 201,
    description: 'KYC process successfully initiated',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid or incomplete data',
    schema: {
      example: {
        statusCode: 400,
        message: 'Tanggal lahir tidak boleh di masa depan',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'User already has KYC data',
    schema: {
      example: {
        statusCode: 409,
        message: 'User sudah memiliki data KYC',
        error: 'Conflict',
      },
    },
  })
  async initiateKyc(
    @Body(ValidationPipe) createKycDto: CreateKycDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<KycResponseDto> {
    try {
      // Extract user ID safely using utility
      const userId = UserExtractionUtil.extractUserId(req);

      this.logger.log(`Initiating KYC process for user: ${userId}`);

      return await this.kycService.initiateKyc(createKycDto, userId);
    } catch (error) {
      this.logger.error(
        `Failed to initiate KYC: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create KYC data (Legacy)',
    description:
      'Creates Know Your Customer data for the authenticated user. This endpoint is maintained for backward compatibility. Use /initiate for new implementations.',
    deprecated: true,
  })
  @ApiBody({ type: CreateKycDto })
  @ApiResponse({
    status: 201,
    description: 'KYC data successfully created',
    type: KycResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid or incomplete data',
    schema: {
      example: {
        statusCode: 400,
        message: 'Tanggal lahir tidak boleh di masa depan',
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid or missing authentication token',
    schema: {
      example: {
        statusCode: 401,
        message: 'Authentication failed',
        error: 'Unauthorized',
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'User already has KYC data',
    schema: {
      example: {
        statusCode: 409,
        message: 'User sudah memiliki data KYC',
        error: 'Conflict',
      },
    },
  })
  async create(
    @Body(ValidationPipe) createKycDto: CreateKycDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<KycResponseDto> {
    try {
      // Extract user ID safely using utility
      const userId = UserExtractionUtil.extractUserId(req);

      this.logger.log(
        `Creating KYC data for user: ${userId} (Legacy endpoint)`,
      );

      return await this.kycService.create(createKycDto, userId);
    } catch (error) {
      this.logger.error(
        `Failed to create KYC (legacy): ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
