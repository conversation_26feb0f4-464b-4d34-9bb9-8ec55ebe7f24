import { Request } from 'express';

/**
 * Interface for authenticated user object from JWT token
 * This represents the user object available in req.user after JWT authentication
 */
export interface AuthenticatedUser {
  /**
   * User ID (mapped from JWT payload.sub)
   */
  id: string;

  /**
   * User email address
   */
  email: string;

  /**
   * Username
   */
  username: string;

  /**
   * User type (e.g., INDIVIDUAL, BUSINESS)
   */
  type: string;

  /**
   * User status (e.g., ACTIVE, PENDING)
   */
  status: string;

  /**
   * Token version for global revocation
   */
  tokenVersion: number;
}

/**
 * Interface for Express request with authenticated user
 */
export interface AuthenticatedRequest extends Request {
  user: AuthenticatedUser;
}

/**
 * Type guard to check if user object is properly authenticated
 */
export function isAuthenticatedUser(user: any): user is AuthenticatedUser {
  return (
    user &&
    typeof user === 'object' &&
    typeof user.id === 'string' &&
    typeof user.email === 'string' &&
    typeof user.username === 'string' &&
    typeof user.type === 'string' &&
    typeof user.status === 'string' &&
    user.id.length > 0 &&
    user.email.length > 0
  );
}
