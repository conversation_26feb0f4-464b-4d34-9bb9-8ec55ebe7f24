import {
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { KycStatus } from '../dto/create-kyc.dto';
import {
  VALID_STATUS_TRANSITIONS,
  ADMIN_ONLY_TRANSITIONS,
  USER_EDITABLE_STATUSES,
  LOCKED_STATUSES,
} from '../constants/kyc.constants';

/**
 * Utility class for KYC status validation and transitions
 */
export class StatusValidatorUtil {
  private static readonly logger = new Logger('StatusValidatorUtil');

  /**
   * Validates if a status transition is allowed
   * @param currentStatus Current KYC status
   * @param newStatus Requested new status
   * @param isAdmin Whether the requester has admin privileges
   * @returns true if transition is valid
   * @throws BadRequestException or ForbiddenException if transition is invalid
   */
  public static validateStatusTransition(
    currentStatus: KycStatus,
    newStatus: KycStatus,
    isAdmin = false,
  ): boolean {
    // If status is not changing, it's valid
    if (currentStatus === newStatus) {
      return true;
    }

    // Check if the transition is allowed based on the current status
    const allowedTransitions = VALID_STATUS_TRANSITIONS[currentStatus];
    if (!allowedTransitions || !allowedTransitions.includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition: ${currentStatus} -> ${newStatus} is not allowed`,
      );
    }

    // Check if this transition requires admin privileges
    const requiresAdmin = ADMIN_ONLY_TRANSITIONS.some(
      (transition) =>
        transition.from === currentStatus && transition.to === newStatus,
    );

    if (requiresAdmin && !isAdmin) {
      throw new ForbiddenException(
        `Status transition from ${currentStatus} to ${newStatus} requires admin privileges`,
      );
    }

    this.logger.log(
      `Valid status transition: ${currentStatus} -> ${newStatus}`,
    );
    return true;
  }

  /**
   * Checks if a status is editable by regular users
   * @param status KYC status to check
   * @returns true if status is editable by users
   */
  public static isUserEditableStatus(status: KycStatus): boolean {
    return USER_EDITABLE_STATUSES.includes(status);
  }

  /**
   * Checks if a status is locked (cannot be modified by regular users)
   * @param status KYC status to check
   * @returns true if status is locked
   */
  public static isLockedStatus(status: KycStatus): boolean {
    return LOCKED_STATUSES.includes(status);
  }

  /**
   * Determines appropriate initial status for KYC creation
   * @param requestedStatus Requested status from the client
   * @param isAdmin Whether the requester has admin privileges
   * @returns Appropriate KYC status
   */
  public static determineInitialStatus(
    requestedStatus: KycStatus | undefined,
  ): KycStatus {
    // If no status is provided, default to PENDING
    if (!requestedStatus) {
      return KycStatus.PENDING;
    }

    // Validate that the requested status is a valid KYC status
    if (!Object.values(KycStatus).includes(requestedStatus)) {
      this.logger.warn(`Invalid KYC status requested: ${requestedStatus}`);
      return KycStatus.PENDING;
    }

    // Always respect the requested status if it's valid
    // This ensures the status matches what was requested
    this.logger.log(`KYC initial status set to: ${requestedStatus}`);
    return requestedStatus;
  }
}
