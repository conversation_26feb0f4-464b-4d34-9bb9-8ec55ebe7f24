import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, ConflictException } from '@nestjs/common';
import { KycService } from '../kyc.service';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateKycDto, IdentityType, KycStatus } from '../dto/create-kyc.dto';
import { UpdateKycApplicantIdDto } from '../dto/update-kyc.dto';

describe('KYC ApplicantId Functionality', () => {
  let service: KycService;
  let prismaService: PrismaService;

  const mockUser = {
    id: 'test-user-id',
    status: 'ACTIVE',
    email: '<EMAIL>',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockKyc = {
    id: 'test-kyc-id',
    identityType: IdentityType.KTP,
    identityNumber: '1234567890123456',
    birthdate: new Date('1990-01-01'),
    birthplace: 'Jakarta',
    address: 'Test Address',
    state: 'DKI Jakarta',
    country: 'Indonesia',
    zipNumber: '12345',
    phoneNumber: '+6281234567890',
    status: KycStatus.PENDING,
    provider: 'MANUAL',
    fileName: null,
    userId: 'test-user-id',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
    },
    kyc: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KycService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<KycService>(KycService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('KYC Creation with ApplicantId', () => {
    it('should create KYC with applicantId successfully', async () => {
      const createKycDto: CreateKycDto = {
        identityType: IdentityType.KTP,
        identityNumber: '1234567890123456',
        birthdate: '1990-01-01',
        birthplace: 'Jakarta',
        address: 'Test Address',
        state: 'DKI Jakarta',
        country: 'Indonesia',
        zipNumber: '12345',
        phoneNumber: '+6281234567890',
        applicantId: 'SUMSUB_APP_12345',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.kyc.findUnique.mockResolvedValue(null);
      mockPrismaService.user.findFirst.mockResolvedValue(null); // No duplicate applicantId
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockPrisma = {
          user: {
            update: jest.fn().mockResolvedValue({
              ...mockUser,
              applicantId: 'SUMSUB_APP_12345',
            }),
            findFirst: jest.fn().mockResolvedValue(null),
            findUnique: jest.fn().mockResolvedValue({
              ...mockUser,
              applicantId: 'SUMSUB_APP_12345',
            }),
          },
          kyc: {
            create: jest.fn().mockResolvedValue(mockKyc),
          },
        };
        return await callback(mockPrisma);
      });

      const result = await service.initiateKyc(createKycDto, 'test-user-id');

      expect(result).toBeDefined();
      expect(result.applicantId).toBe('SUMSUB_APP_12345');
    });

    it('should reject duplicate applicantId', async () => {
      const createKycDto: CreateKycDto = {
        identityType: IdentityType.KTP,
        identityNumber: '1234567890123456',
        birthdate: '1990-01-01',
        birthplace: 'Jakarta',
        address: 'Test Address',
        state: 'DKI Jakarta',
        country: 'Indonesia',
        zipNumber: '12345',
        phoneNumber: '+6281234567890',
        applicantId: 'DUPLICATE_APP_ID',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.kyc.findUnique.mockResolvedValue(null);
      mockPrismaService.user.findFirst.mockResolvedValue({
        id: 'other-user-id',
      }); // Duplicate found

      await expect(
        service.initiateKyc(createKycDto, 'test-user-id'),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('ApplicantId Update', () => {
    it('should update applicantId successfully', async () => {
      const updateDto: UpdateKycApplicantIdDto = {
        applicantId: 'NEW_SUMSUB_APP_12345',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.user.findFirst.mockResolvedValue(null); // No duplicate
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockPrisma = {
          user: {
            update: jest.fn().mockResolvedValue({
              ...mockUser,
              applicantId: 'NEW_SUMSUB_APP_12345',
            }),
            findFirst: jest.fn().mockResolvedValue(null),
            findUnique: jest.fn().mockResolvedValue({
              ...mockUser,
              applicantId: 'NEW_SUMSUB_APP_12345',
            }),
          },
          kyc: {
            findUnique: jest.fn().mockResolvedValue(mockKyc),
          },
        };
        return await callback(mockPrisma);
      });

      const result = await service.updateApplicantId(updateDto, 'test-user-id');

      expect(result).toBeDefined();
      expect(result.applicantId).toBe('NEW_SUMSUB_APP_12345');
    });

    it('should validate applicantId format', async () => {
      const updateDto: UpdateKycApplicantIdDto = {
        applicantId: 'ab', // Too short
      };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      await expect(
        service.updateApplicantId(updateDto, 'test-user-id'),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
