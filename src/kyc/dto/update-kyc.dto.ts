import {
  IsString,
  <PERSON>Enum,
  IsDate<PERSON>tring,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IdentityType, KycStatus } from './create-kyc.dto';

/**
 * DTO for updating applicant ID specifically
 */
export class UpdateKycApplicantIdDto {
  @ApiProperty({
    example: 'SUMSUB_APP_12345',
    description: 'Unique applicant identifier from KYC provider',
  })
  @IsString({ message: 'Applicant ID harus berupa string' })
  @MinLength(3, { message: 'Applicant ID minimal 3 karakter' })
  @MaxLength(100, { message: 'Applicant ID maksimal 100 karakter' })
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Applicant ID hanya boleh mengandung huruf, angka, underscore, dan dash',
  })
  applicantId: string;
}

/**
 * DTO for general KYC updates
 */
export class UpdateKycDto {
  @ApiProperty({
    enum: IdentityType,
    example: IdentityType.KTP,
    description: 'Jenis identitas yang digunakan (opsional)',
    required: false,
  })
  @IsOptional()
  @IsEnum(IdentityType, {
    message: 'Jenis identitas harus berupa KTP, PASSPORT atau SIM',
  })
  identityType?: IdentityType;

  @ApiProperty({
    example: '3201234567890123',
    description: 'Nomor identitas (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nomor identitas harus berupa string' })
  @MinLength(10, { message: 'Nomor identitas minimal 10 karakter' })
  @MaxLength(30, { message: 'Nomor identitas maksimal 30 karakter' })
  @Matches(/^[0-9A-Za-z]+$/, {
    message: 'Nomor identitas hanya boleh mengandung huruf dan angka',
  })
  identityNumber?: string;

  @ApiProperty({
    example: '1990-01-15',
    description: 'Tanggal lahir dalam format YYYY-MM-DD (opsional)',
    required: false,
  })
  @IsOptional()
  @IsDateString(
    {},
    { message: 'Format tanggal lahir harus YYYY-MM-DD (ISO 8601)' },
  )
  birthdate?: string;

  @ApiProperty({
    example: 'Jakarta',
    description: 'Tempat lahir (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tempat lahir harus berupa string' })
  @MinLength(2, { message: 'Tempat lahir minimal 2 karakter' })
  @MaxLength(100, { message: 'Tempat lahir maksimal 100 karakter' })
  birthplace?: string;

  @ApiProperty({
    example: 'Jl. Sudirman No. 123, Jakarta Pusat',
    description: 'Alamat lengkap (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Alamat harus berupa string' })
  @MinLength(10, { message: 'Alamat minimal 10 karakter' })
  @MaxLength(500, { message: 'Alamat maksimal 500 karakter' })
  address?: string;

  @ApiProperty({
    example: 'DKI Jakarta',
    description: 'Provinsi/negara bagian (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Provinsi harus berupa string' })
  @MinLength(2, { message: 'Provinsi minimal 2 karakter' })
  @MaxLength(100, { message: 'Provinsi maksimal 100 karakter' })
  state?: string;

  @ApiProperty({
    example: 'Indonesia',
    description: 'Negara (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Negara harus berupa string' })
  @MinLength(2, { message: 'Negara minimal 2 karakter' })
  @MaxLength(100, { message: 'Negara maksimal 100 karakter' })
  country?: string;

  @ApiProperty({
    example: '12345',
    description: 'Kode pos (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Kode pos harus berupa string' })
  @Matches(/^[0-9]{5}$/, {
    message: 'Kode pos harus berupa 5 digit angka',
  })
  zipNumber?: string;

  @ApiProperty({
    example: '+6281234567890',
    description: 'Nomor telepon dengan kode negara (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nomor telepon harus berupa string' })
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message:
      'Format nomor telepon tidak valid (gunakan format internasional +62xxx)',
  })
  phoneNumber?: string;

  @ApiProperty({
    example: 'ktp_scan_123456789.jpg',
    description: 'Nama file dokumen identitas (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nama file harus berupa string' })
  @MaxLength(255, { message: 'Nama file maksimal 255 karakter' })
  @Matches(/^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|pdf)$/i, {
    message: 'Format file harus jpg, jpeg, png, atau pdf',
  })
  fileName?: string;

  @ApiProperty({
    example: 'MANUAL or SUMSUB',
    description: 'Provider layanan KYC (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Provider harus berupa string' })
  @MaxLength(100, { message: 'Provider maksimal 100 karakter' })
  provider?: string;

  @ApiProperty({
    enum: KycStatus,
    example: KycStatus.PENDING,
    description: 'Status KYC (opsional)',
    required: false,
  })
  @IsOptional()
  @IsEnum(KycStatus, {
    message:
      'Status KYC harus berupa PENDING, APPROVED, REJECTED, DENIED, CANCELED, MANUAL_REVIEW, atau LOCKED',
  })
  status?: KycStatus;

  @ApiProperty({
    example: 'SUMSUB_APP_12345',
    description: 'Unique applicant identifier from KYC provider (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Applicant ID harus berupa string' })
  @MinLength(3, { message: 'Applicant ID minimal 3 karakter' })
  @MaxLength(100, { message: 'Applicant ID maksimal 100 karakter' })
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Applicant ID hanya boleh mengandung huruf, angka, underscore, dan dash',
  })
  applicantId?: string;
}
