import {
  IsString,
  IsEnum,
  IsDateString,
  IsNotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
  IsOptional,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum IdentityType {
  KTP = 'KTP',
  PASSPORT = 'PASSPORT',
  DRIVER_LICENSE = 'DRIVER_LICENSE',
  SIM = 'SIM',
}

export enum KycStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DENIED = 'DENIED',
  CANCELED = 'CANCELED',
  MANUAL_REVIEW = 'MANUAL_REVIEW',
  LOCKED = 'LOCKED',
}

export class CreateKycDto {
  @ApiProperty({
    enum: IdentityType,
    example: IdentityType.KTP,
    description: 'Jenis identitas yang digunakan',
  })
  @IsEnum(IdentityType, {
    message: 'Jenis identitas harus berupa KTP, PASSPORT atau SIM',
  })
  identityType: IdentityType;

  @ApiProperty({
    example: '3201234567890123',
    description: 'Nomor identitas',
  })
  @IsString({ message: 'Nomor identitas harus berupa string' })
  @IsNotEmpty({ message: 'Nomor identitas tidak boleh kosong' })
  @MinLength(10, { message: 'Nomor identitas minimal 10 karakter' })
  @MaxLength(30, { message: 'Nomor identitas maksimal 30 karakter' })
  @Matches(/^[0-9A-Za-z]+$/, {
    message: 'Nomor identitas hanya boleh mengandung huruf dan angka',
  })
  identityNumber: string;

  @ApiProperty({
    example: '1990-01-15',
    description: 'Tanggal lahir (YYYY-MM-DD)',
  })
  @IsDateString(
    {},
    { message: 'Format tanggal lahir tidak valid (gunakan YYYY-MM-DD)' },
  )
  birthdate: string;

  @ApiProperty({
    example: 'Jakarta',
    description: 'Tempat lahir',
  })
  @IsString({ message: 'Tempat lahir harus berupa string' })
  @IsNotEmpty({ message: 'Tempat lahir tidak boleh kosong' })
  @MinLength(2, { message: 'Tempat lahir minimal 2 karakter' })
  @MaxLength(100, { message: 'Tempat lahir maksimal 100 karakter' })
  @Matches(/^[a-zA-Z\s'-.,]+$/, {
    message:
      'Tempat lahir hanya boleh mengandung huruf, spasi, dan tanda baca umum',
  })
  birthplace: string;

  @ApiProperty({
    example: 'Jl. Sudirman No. 123, RT 001/RW 002, Kelurahan ABC',
    description: 'Alamat lengkap',
  })
  @IsString({ message: 'Alamat harus berupa string' })
  @IsNotEmpty({ message: 'Alamat tidak boleh kosong' })
  @MinLength(10, { message: 'Alamat minimal 10 karakter' })
  @MaxLength(500, { message: 'Alamat maksimal 500 karakter' })
  address: string;

  @ApiProperty({
    example: 'DKI Jakarta',
    description: 'Provinsi/Negara bagian',
  })
  @IsString({ message: 'Provinsi harus berupa string' })
  @IsNotEmpty({ message: 'Provinsi tidak boleh kosong' })
  @MinLength(2, { message: 'Provinsi minimal 2 karakter' })
  @MaxLength(100, { message: 'Provinsi maksimal 100 karakter' })
  @Matches(/^[a-zA-Z\s'-.,]+$/, {
    message:
      'Provinsi hanya boleh mengandung huruf, spasi, dan tanda baca umum',
  })
  state: string;

  @ApiProperty({
    example: 'IDN',
    description: 'Negara',
  })
  @IsString({ message: 'Negara harus berupa string' })
  @IsNotEmpty({ message: 'Negara tidak boleh kosong' })
  @MinLength(2, { message: 'Negara minimal 2 karakter' })
  @MaxLength(100, { message: 'Negara maksimal 100 karakter' })
  @Matches(/^[a-zA-Z\s'-.,]+$/, {
    message: 'Negara hanya boleh mengandung huruf, spasi, dan tanda baca umum',
  })
  country: string;

  @ApiProperty({
    example: '12345',
    description: 'Kode pos',
  })
  @IsString({ message: 'Kode pos harus berupa string' })
  @IsNotEmpty({ message: 'Kode pos tidak boleh kosong' })
  @Matches(/^[0-9]{5}$/, {
    message: 'Kode pos harus berupa 5 digit angka',
  })
  zipNumber: string;

  @ApiProperty({
    example: '+6281234567890',
    description: 'Nomor telepon dengan kode negara',
  })
  @IsString({ message: 'Nomor telepon harus berupa string' })
  @IsNotEmpty({ message: 'Nomor telepon tidak boleh kosong' })
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message:
      'Format nomor telepon tidak valid (gunakan format internasional +62xxx)',
  })
  phoneNumber: string;

  @ApiProperty({
    example: 'ktp_scan_123456789.jpg',
    description: 'Nama file dokumen identitas (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nama file harus berupa string' })
  @MaxLength(255, { message: 'Nama file maksimal 255 karakter' })
  @Matches(/^[a-zA-Z0-9._-]+\.(jpg|jpeg|png|pdf)$/i, {
    message: 'Format file harus jpg, jpeg, png, atau pdf',
  })
  fileName?: string;

  @ApiProperty({
    example: 'MANUAL or SUMSUB',
    description: 'Provider layanan KYC (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Provider harus berupa string' })
  @MaxLength(100, { message: 'Provider maksimal 100 karakter' })
  provider?: string;

  @ApiProperty({
    enum: KycStatus,
    example: KycStatus.PENDING,
    description: 'Status KYC (opsional, default: PENDING)',
    required: false,
  })
  @IsOptional()
  @IsEnum(KycStatus, {
    message:
      'Status KYC harus berupa PENDING, APPROVED, REJECTED, DENIED, CANCELED, MANUAL_REVIEW, atau LOCKED',
  })
  status?: KycStatus;

  @ApiProperty({
    example: 'SUMSUB_APP_12345',
    description: 'Unique applicant identifier from KYC provider (opsional)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Applicant ID harus berupa string' })
  @MinLength(3, { message: 'Applicant ID minimal 3 karakter' })
  @MaxLength(100, { message: 'Applicant ID maksimal 100 karakter' })
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message:
      'Applicant ID hanya boleh mengandung huruf, angka, underscore, dan dash',
  })
  applicantId?: string;
}
