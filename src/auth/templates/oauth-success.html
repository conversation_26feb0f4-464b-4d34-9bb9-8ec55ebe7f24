<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline';">
    <title>Authentication Successful</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .container { 
            background: white; 
            border-radius: 12px; 
            padding: 2rem; 
            max-width: 400px; 
            width: 90%; 
            text-align: center; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
        }
        .success-icon { 
            font-size: 3rem; 
            margin-bottom: 1rem; 
            color: #10b981; 
        }
        h1 { 
            color: #1f2937; 
            margin-bottom: 1rem; 
            font-size: 1.5rem; 
        }
        p { 
            color: #6b7280; 
            margin-bottom: 1rem; 
            line-height: 1.5; 
        }
        .welcome { 
            color: #10b981; 
            font-weight: 600; 
        }
        .loading-spinner { 
            width: 24px; 
            height: 24px; 
            border: 2px solid #e5e7eb; 
            border-top: 2px solid #3b82f6; 
            border-radius: 50%; 
            animation: spin 1s linear infinite; 
            margin: 1rem auto; 
        }
        @keyframes spin { 
            0% { transform: rotate(0deg); } 
            100% { transform: rotate(360deg); } 
        }
        .actions { 
            margin-top: 1.5rem; 
        }
        .btn { 
            padding: 0.75rem 1.5rem; 
            border: none; 
            border-radius: 6px; 
            font-weight: 600; 
            cursor: pointer; 
            margin: 0 0.5rem; 
            transition: all 0.2s; 
        }
        .btn-primary { 
            background: #3b82f6; 
            color: white; 
        }
        .btn-primary:hover { 
            background: #2563eb; 
        }
        .countdown {
            color: #6b7280; 
            font-size: 0.875rem; 
            margin-top: 1rem;
        }
        @media (max-width: 480px) { 
            .container { padding: 1.5rem; } 
            h1 { font-size: 1.25rem; } 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="success-icon">✓</div>
            <h1>Authentication Successful!</h1>
            <p>You have been successfully authenticated.</p>
            <div class="loading-spinner"></div>
        </div>
        <div class="actions">
            <button onclick="redirectToApp()" class="btn btn-primary">Continue to App</button>
        </div>
    </div>
    
    <script>
        // Enhanced redirect function with proper URL construction
        function redirectToApp() {
            try {
                const frontendUrl = '{{FRONTEND_URL}}' || 'http://localhost:3000';
                const targetUrl = frontendUrl + '/auth/google/success';

                console.log('Redirecting to:', targetUrl);
                window.location.href = targetUrl;
            } catch (error) {
                console.error('Redirect error:', error);
                // Fallback to home page
                const fallbackUrl = '{{FRONTEND_URL}}' || 'http://localhost:3000';
                window.location.href = fallbackUrl;
            }
        }

        // Enhanced auto-redirect with error handling
        function initializeRedirect() {
            try {
                let countdown = 3;
                const countdownElement = document.createElement('p');
                countdownElement.className = 'countdown';

                const contentElement = document.querySelector('.content');
                if (contentElement) {
                    contentElement.appendChild(countdownElement);
                }

                const updateCountdown = () => {
                    try {
                        if (countdownElement) {
                            countdownElement.textContent = `Redirecting in ${countdown} seconds...`;
                        }

                        if (countdown <= 0) {
                            redirectToApp();
                        } else {
                            countdown--;
                            setTimeout(updateCountdown, 1000);
                        }
                    } catch (error) {
                        console.error('Countdown error:', error);
                        // Fallback redirect
                        redirectToApp();
                    }
                };

                setTimeout(updateCountdown, 100);

                // Prevent back navigation
                history.pushState(null, null, window.location.href);
                window.addEventListener('popstate', function(event) {
                    history.pushState(null, null, window.location.href);
                });

            } catch (error) {
                console.error('Initialization error:', error);
                // Emergency fallback
                setTimeout(redirectToApp, 5000);
            }
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeRedirect);
        } else {
            initializeRedirect();
        }
    </script>
</body>
</html>
