<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline';">
    <title>Authentication Error</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .container { 
            background: white; 
            border-radius: 12px; 
            padding: 2rem; 
            max-width: 400px; 
            width: 90%; 
            text-align: center; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
        }
        .error-icon { 
            font-size: 3rem; 
            margin-bottom: 1rem; 
            color: #ef4444; 
        }
        h1 { 
            color: #1f2937; 
            margin-bottom: 1rem; 
            font-size: 1.5rem; 
        }
        p { 
            color: #6b7280; 
            margin-bottom: 1rem; 
            line-height: 1.5; 
        }
        .error-details { 
            font-size: 0.875rem; 
            color: #9ca3af; 
        }
        .actions { 
            margin-top: 1.5rem; 
        }
        .btn { 
            padding: 0.75rem 1.5rem; 
            border: none; 
            border-radius: 6px; 
            font-weight: 600; 
            cursor: pointer; 
            margin: 0 0.5rem; 
            transition: all 0.2s; 
        }
        .btn-primary { 
            background: #3b82f6; 
            color: white; 
        }
        .btn-primary:hover { 
            background: #2563eb; 
        }
        .btn-secondary { 
            background: #e5e7eb; 
            color: #374151; 
        }
        .btn-secondary:hover { 
            background: #d1d5db; 
        }
        @media (max-width: 480px) { 
            .container { padding: 1.5rem; } 
            h1 { font-size: 1.25rem; } 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="error-icon">✗</div>
            <h1>Authentication Error</h1>
            <p>We encountered an error during authentication. Please try again.</p>
            <p class="error-details">If the problem persists, please contact support.</p>
        </div>
        <div class="actions">
            <button onclick="tryAgain()" class="btn btn-primary">Try Again</button>
            <button onclick="goHome()" class="btn btn-secondary">Go Home</button>
        </div>
    </div>
    
    <script>
        function tryAgain() {
            const baseUrl = window.location.origin;
            window.location.href = baseUrl + '/api/v1/auth/google';
        }
        
        function goHome() {
            const frontendUrl = '{{FRONTEND_URL}}' || 'http://localhost:3000';
            window.location.href = frontendUrl;
        }
        
        // Prevent back navigation
        history.pushState(null, null, window.location.href);
        window.addEventListener('popstate', function(event) {
            history.pushState(null, null, window.location.href);
        });
    </script>
</body>
</html>
