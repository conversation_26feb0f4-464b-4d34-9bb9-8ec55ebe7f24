<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
    />
    <title>Authentication Successful - Orbitum</title>
    <style>
      :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --success-color: #10b981;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --background: #f9fafb;
        --card-background: #ffffff;
        --border-radius: 12px;
        --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', sans-serif;
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1.6;
        color: var(--text-primary);
        overflow-x: hidden;
      }

      .container {
        background: var(--card-background);
        border-radius: var(--border-radius);
        padding: 2rem 1.5rem;
        max-width: 480px;
        width: 90%;
        text-align: center;
        box-shadow: var(--shadow);
        position: relative;
        transform: translateY(0);
        animation: slideUp 0.6s ease-out;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .success-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--success-color), #059669);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        animation: pulse 2s infinite;
      }

      .success-icon svg {
        width: 40px;
        height: 40px;
        color: white;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
          box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
        }
        50% {
          transform: scale(1.05);
          box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
        }
      }

      h1 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.75rem;
        font-weight: 700;
        letter-spacing: -0.025em;
      }

      .subtitle {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
        font-size: 1rem;
        font-weight: 400;
      }

      .user-info {
        background: var(--background);
        border-radius: 8px;
        padding: 1rem;
        margin: 1.5rem 0;
        border: 1px solid #e5e7eb;
      }

      .user-avatar {
        width: 48px;
        height: 48px;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
        color: white;
        font-weight: 600;
        font-size: 1.25rem;
      }

      .user-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
      }

      .user-email {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: #ecfdf5;
        color: var(--success-color);
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 1rem 0;
        border: 1px solid #d1fae5;
      }

      .new-user-badge {
        background: #fef3c7;
        color: #92400e;
        border-color: #fde68a;
      }

      .countdown {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin: 1rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
      }

      .countdown-timer {
        font-weight: 600;
        color: var(--primary-color);
      }

      .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #e5e7eb;
        border-top: 2px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .actions {
        margin-top: 2rem;
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .btn {
        padding: 0.875rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: var(--transition);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        position: relative;
        overflow: hidden;
      }

      .btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn:hover:before {
        left: 100%;
      }

      .btn-primary {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--secondary-color)
        );
        color: white;
        border: 2px solid transparent;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
      }

      .btn-secondary {
        background: var(--background);
        color: var(--text-primary);
        border: 2px solid #e5e7eb;
      }

      .btn-secondary:hover {
        background: #f3f4f6;
        border-color: #d1d5db;
        transform: translateY(-1px);
      }

      .security-info {
        margin-top: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        font-size: 0.75rem;
        color: var(--text-secondary);
        text-align: left;
      }

      .security-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      .security-item:last-child {
        margin-bottom: 0;
      }

      .security-icon {
        width: 16px;
        height: 16px;
        color: var(--success-color);
      }

      .footer {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
        color: var(--text-secondary);
        font-size: 0.75rem;
      }

      @media (max-width: 480px) {
        .container {
          margin: 1rem;
          padding: 1.5rem 1rem;
        }
        h1 {
          font-size: 1.5rem;
        }
        .btn {
          padding: 0.75rem 1rem;
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #f9fafb;
          --text-secondary: #d1d5db;
          --background: #1f2937;
          --card-background: #374151;
        }
      }

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="success-icon">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          ></path>
        </svg>
      </div>

      <h1>Authentication Successful!</h1>
      <p class="subtitle">
        You have been successfully authenticated with {{PROVIDER_NAME}}
      </p>

      <div class="user-info">
        <div class="user-avatar">{{USER_INITIALS}}</div>
        <div class="user-name">{{USER_NAME}}</div>
        <div class="user-email">{{USER_EMAIL}}</div>
      </div>

      <div class="status-badge {{#if IS_NEW_USER}}new-user-badge{{/if}}">
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        {{#if IS_NEW_USER}}Welcome to Orbitum!{{else}}Welcome back!{{/if}}
      </div>

      <div class="countdown">
        <div class="loading-spinner"></div>
        <span
          >Redirecting in
          <span class="countdown-timer" id="countdown">3</span> seconds...</span
        >
      </div>

      <div class="actions">
        <button
          onclick="redirectToApp()"
          class="btn btn-primary"
          id="continueBtn"
        >
          <svg
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13 7l5 5m0 0l-5 5m5-5H6"
            />
          </svg>
          Continue to Application
        </button>
        <button onclick="openInNewTab()" class="btn btn-secondary">
          <svg
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>
          Open in New Tab
        </button>
      </div>

      <div class="security-info">
        <div class="security-item">
          <svg
            class="security-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
            />
          </svg>
          Secure authentication via {{PROVIDER_NAME}}
        </div>
        <div class="security-item">
          <svg
            class="security-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
          Your session is encrypted and secure
        </div>
        <div class="security-item">
          <svg
            class="security-icon"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Token expires in {{TOKEN_EXPIRES_IN}}
        </div>
      </div>

      <div class="footer">
        <p>Powered by Orbitum Authentication • Secured with OAuth 2.0</p>
      </div>
    </div>

    <script>
      // Configuration
      const config = {
          frontendUrl: '{{FRONTEND_URL}}' || 'http://localhost:3000',
          redirectUrl: '{{REDIRECT_URL}}',
          isPopup: {{IS_POPUP}} || false,
          autoRedirect: {{AUTO_REDIRECT}} || true,
          redirectDelay: {{REDIRECT_DELAY}} || 3000,
          accessToken: '{{ACCESS_TOKEN}}',
          refreshToken: '{{REFRESH_TOKEN}}',
          userData: {
              id: '{{USER_ID}}',
              email: '{{USER_EMAIL}}',
              name: '{{USER_NAME}}',
              isNewUser: {{IS_NEW_USER}} || false
          }
      };

      // Enhanced token accessibility - Make tokens globally accessible
      window.oauthTokens = {
          accessToken: config.accessToken || null,
          refreshToken: config.refreshToken || null,
          accessTokenExpiresIn: {{ACCESS_TOKEN_EXPIRES_IN}} || null,
          refreshTokenExpiresIn: {{REFRESH_TOKEN_EXPIRES_IN}} || null,
          tokenType: '{{TOKEN_TYPE}}' || 'Bearer',
          user: config.userData,
          isNewUser: config.userData.isNewUser || false,
          provider: '{{PROVIDER}}' || 'google',
          expiresAt: {{ACCESS_TOKEN_EXPIRES_IN}} ?
              new Date(Date.now() + ({{ACCESS_TOKEN_EXPIRES_IN}} * 1000)).toISOString() : null
      };

      // Store tokens in localStorage for persistence (optional)
      if (window.oauthTokens.accessToken && typeof(Storage) !== "undefined") {
          try {
              localStorage.setItem('oauth_access_token', window.oauthTokens.accessToken);
              localStorage.setItem('oauth_refresh_token', window.oauthTokens.refreshToken || '');
              localStorage.setItem('oauth_user', JSON.stringify(window.oauthTokens.user || {}));
              localStorage.setItem('oauth_expires_at', window.oauthTokens.expiresAt || '');
              localStorage.setItem('oauth_provider', window.oauthTokens.provider || 'google');
          } catch (storageError) {
              console.warn('Could not store tokens in localStorage:', storageError);
          }
      }

      // Utility functions for easy token access
      window.getOAuthTokens = function() {
          return window.oauthTokens;
      };

      window.getAccessToken = function() {
          return window.oauthTokens ? window.oauthTokens.accessToken : null;
      };

      window.getRefreshToken = function() {
          return window.oauthTokens ? window.oauthTokens.refreshToken : null;
      };

      window.getOAuthUser = function() {
          return window.oauthTokens ? window.oauthTokens.user : null;
      };

      window.isTokenExpired = function() {
          if (!window.oauthTokens || !window.oauthTokens.expiresAt) {
              return true;
          }
          return new Date() >= new Date(window.oauthTokens.expiresAt);
      };

      // Dispatch custom event for token availability
      try {
          const tokenEvent = new CustomEvent('oauthTokensReady', {
              detail: window.oauthTokens
          });
          window.dispatchEvent(tokenEvent);
      } catch (eventError) {
          console.warn('Could not dispatch token event:', eventError);
      }

      // State management
      let countdownTimer = null;
      let redirectInProgress = false;

      // Enhanced redirect function with error handling and retry mechanism
      function redirectToApp(forceRedirect = false) {
          if (redirectInProgress && !forceRedirect) return;
          redirectInProgress = true;

          try {
              const targetUrl = config.redirectUrl || config.frontendUrl;

              if (!targetUrl) {
                  throw new Error('No redirect URL configured');
              }

              console.log('Redirecting to:', targetUrl);

              // Store authentication data for the frontend
              if (typeof(Storage) !== "undefined") {
                  const authData = {
                      accessToken: config.accessToken,
                      refreshToken: config.refreshToken,
                      user: config.userData,
                      timestamp: new Date().toISOString(),
                      provider: '{{PROVIDER_NAME}}'
                  };

                  try {
                      localStorage.setItem('orbitum_auth_success', JSON.stringify(authData));
                      sessionStorage.setItem('orbitum_redirect_success', 'true');
                  } catch (storageError) {
                      console.warn('Could not store auth data:', storageError);
                  }
              }

              // Handle popup vs redirect flow
              if (config.isPopup && window.opener) {
                  handlePopupRedirect(targetUrl);
              } else {
                  handleStandardRedirect(targetUrl);
              }

          } catch (error) {
              console.error('Redirect error:', error);
              handleRedirectError(error);
          }
      }

      function handlePopupRedirect(targetUrl) {
          try {
              // Send message to parent window
              const messageData = {
                  type: 'OAUTH_SUCCESS',
                  success: true,
                  data: {
                      accessToken: config.accessToken,
                      refreshToken: config.refreshToken,
                      user: config.userData,
                      redirectUrl: targetUrl
                  }
              };

              window.opener.postMessage(messageData, config.frontendUrl);

              // Small delay before closing to ensure message is sent
              setTimeout(() => {
                  window.close();
              }, 500);

          } catch (postMessageError) {
              console.error('PostMessage failed:', postMessageError);
              // Fallback to standard redirect
              handleStandardRedirect(targetUrl);
          }
      }

      function handleStandardRedirect(targetUrl) {
          // Add auth parameters to URL if needed
          const url = new URL(targetUrl);
          url.searchParams.set('auth_success', 'true');
          url.searchParams.set('timestamp', Date.now().toString());

          window.location.href = url.toString();
      }

      function handleRedirectError(error) {
          clearCountdown();

          const countdownElement = document.getElementById('countdown');
          const continueBtn = document.getElementById('continueBtn');

          if (countdownElement) {
              countdownElement.parentElement.innerHTML = `
                  <span style="color: #ef4444;">
                      <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24" style="vertical-align: middle; margin-right: 4px;">
                          <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/>
                      </svg>
                      Redirect failed. Please use the button below.
                  </span>
              `;
          }

          if (continueBtn) {
              continueBtn.textContent = 'Try Again';
              continueBtn.onclick = () => redirectToApp(true);
          }

          // Show error details in console for debugging
          console.error('Redirect Error Details:', {
              error: error.message,
              config: config,
              timestamp: new Date().toISOString()
          });
      }

      function openInNewTab() {
          try {
              const targetUrl = config.redirectUrl || config.frontendUrl;
              window.open(targetUrl, '_blank', 'noopener,noreferrer');
          } catch (error) {
              console.error('Failed to open new tab:', error);
              redirectToApp();
          }
      }

      function initializeCountdown() {
          if (!config.autoRedirect) return;

          let countdown = Math.floor(config.redirectDelay / 1000);
          const countdownElement = document.getElementById('countdown');

          const updateCountdown = () => {
              if (countdownElement) {
                  countdownElement.textContent = countdown;
              }

              if (countdown <= 0) {
                  redirectToApp();
              } else {
                  countdown--;
                  countdownTimer = setTimeout(updateCountdown, 1000);
              }
          };

          // Start countdown after a brief delay
          setTimeout(updateCountdown, 100);
      }

      function clearCountdown() {
          if (countdownTimer) {
              clearTimeout(countdownTimer);
              countdownTimer = null;
          }
      }

      function initializePage() {
          try {
              // Prevent back navigation
              history.pushState(null, null, window.location.href);
              window.addEventListener('popstate', function(event) {
                  history.pushState(null, null, window.location.href);
              });

              // Initialize countdown if auto-redirect is enabled
              initializeCountdown();

              // Add keyboard shortcuts
              document.addEventListener('keydown', function(event) {
                  if (event.key === 'Enter' || event.key === ' ') {
                      event.preventDefault();
                      redirectToApp();
                  } else if (event.key === 'Escape') {
                      clearCountdown();
                  }
              });

              console.log('OAuth success page initialized successfully');

          } catch (error) {
              console.error('Failed to initialize page:', error);
              // Fallback to simple redirect after 5 seconds
              setTimeout(() => {
                  window.location.href = config.frontendUrl;
              }, 5000);
          }
      }

      // Initialize when DOM is ready
      if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initializePage);
      } else {
          initializePage();
      }

      // Cleanup on page unload
      window.addEventListener('beforeunload', function() {
          clearCountdown();
      });
    </script>
  </body>
</html>
