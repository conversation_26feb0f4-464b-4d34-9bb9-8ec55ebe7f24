import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { TokenSecurityService } from '../services/token-security.service';

@Injectable()
export class EnhancedJwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private readonly reflector: Reflector,
    private readonly tokenSecurityService: TokenSecurityService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    // Rate limiting check
    const identifier = this.getRequestIdentifier(request);
    if (!this.tokenSecurityService.validateTokenRequestRate(identifier)) {
      throw new UnauthorizedException('Too many token validation requests');
    }

    // Standard JWT validation
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }

    // Additional security checks
    const user = request.user;
    // Check if route requires specific permissions
    const requiredRoles = this.reflector.get<string[]>(
      'roles',
      context.getHandler(),
    );
    if (requiredRoles && !this.validateUserRoles(user, requiredRoles)) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return true;
  }

  private getRequestIdentifier(request: any): string {
    return request.ip || request.connection.remoteAddress || 'unknown';
  }

  private validateUserRoles(user: any, requiredRoles: string[]): boolean {
    const userRoles = user.roles;
    if (!userRoles) {
      return false;
    }
    return requiredRoles.every((role) => userRoles.includes(role));
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      this.logSecurityEvent(context, err, info);
      throw err || new UnauthorizedException('Authentication failed');
    }
    return user;
  }

  private logSecurityEvent(
    context: ExecutionContext,
    error: Error,
    info: any,
  ): void {
    const request = context.switchToHttp().getRequest();
    const logData = {
      timestamp: new Date().toISOString(),
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      path: request.path,
      method: request.method,
      error: error?.message,
      info: info?.message,
    };
    console.log('JWT Auth Security Event:', logData);
  }
}
