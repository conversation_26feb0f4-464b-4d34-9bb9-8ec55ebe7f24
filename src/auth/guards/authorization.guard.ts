import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserType } from '../../../generated/prisma';
import { UserInfo } from '../interfaces/user-info.interface';

export interface AuthenticatedRequest {
  user?: UserInfo;
}

export const ROLES_KEY = 'roles';
export const REQUIRE_ACTIVE_ACCOUNT = 'requireActive';

@Injectable()
export class AuthorizationGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();

    if (!request.user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Check if account must be active
    const requireActive = this.reflector.get<boolean>(
      REQUIRE_ACTIVE_ACCOUNT,
      context.getHandler(),
    );

    if (requireActive && request.user.status !== 'ACTIVE') {
      throw new ForbiddenException(
        'Account must be active to access this resource',
      );
    }

    // Check for required user types
    const requiredRoles = this.reflector.get<UserType[]>(
      ROLES_KEY,
      context.getHandler(),
    );

    if (requiredRoles && requiredRoles.length > 0) {
      if (!requiredRoles.includes(request.user.type)) {
        throw new ForbiddenException('Insufficient permissions');
      }
    }

    return true;
  }
}
