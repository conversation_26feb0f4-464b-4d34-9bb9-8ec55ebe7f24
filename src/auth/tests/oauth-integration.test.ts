import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AuthModule } from '../auth.module';
import { PrismaModule } from '../../prisma/prisma.module';
import { UsersModule } from '../../users/users.module';
import { CorsHandlerService } from '../services/cors-handler.service';
import { RateLimiterService } from '../services/rate-limiter.service';
import { AuditLoggingService } from '../services/audit-logging.service';
import { InputValidationService } from '../services/input-validation.service';
import { ErrorMonitoringService } from '../services/error-monitoring.service';

describe('OAuth Integration Tests', () => {
  let app: INestApplication;
  let corsHandler: CorsHandlerService;
  let rateLimiter: RateLimiterService;
  let auditLogging: AuditLoggingService;
  let inputValidation: InputValidationService;
  let errorMonitoring: ErrorMonitoringService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        PassportModule,
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '24h' },
        }),
        PrismaModule,
        UsersModule,
        AuthModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();

    corsHandler = moduleFixture.get<CorsHandlerService>(CorsHandlerService);
    rateLimiter = moduleFixture.get<RateLimiterService>(RateLimiterService);
    auditLogging = moduleFixture.get<AuditLoggingService>(AuditLoggingService);
    inputValidation = moduleFixture.get<InputValidationService>(
      InputValidationService,
    );
    errorMonitoring = moduleFixture.get<ErrorMonitoringService>(
      ErrorMonitoringService,
    );

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('CORS Handling', () => {
    it('should allow valid origins', async () => {
      const response = await request(app.getHttpServer())
        .options('/api/v1/auth/google')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'GET')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });

    it('should block invalid origins', async () => {
      await request(app.getHttpServer())
        .options('/api/v1/auth/google')
        .set('Origin', 'http://malicious-site.com')
        .set('Access-Control-Request-Method', 'GET')
        .expect(403);
    });

    it('should set proper CORS headers', async () => {
      const mockReq = {
        headers: { origin: 'http://localhost:3000' },
        method: 'GET',
      };
      const mockRes = {
        header: jest.fn(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const result = corsHandler.applyCorsHeaders(
        mockReq as any,
        mockRes as any,
      );
      expect(result).toBe(true);
      expect(mockRes.header).toHaveBeenCalledWith(
        'Access-Control-Allow-Origin',
        expect.any(String),
      );
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limits correctly', async () => {
      const mockReq = {
        ip: '***********',
        path: '/api/v1/auth/login',
        method: 'POST',
        headers: {},
        user: null,
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        header: jest.fn(),
      };

      // First few requests should pass
      for (let i = 0; i < 3; i++) {
        const result = await rateLimiter.applyRateLimit(
          mockReq as any,
          mockRes as any,
        );
        expect(result).toBe(true);
      }
    });

    it('should block requests after rate limit exceeded', async () => {
      const mockReq = {
        ip: '***********',
        path: '/api/v1/auth/login',
        method: 'POST',
        headers: {},
        user: null,
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        header: jest.fn(),
      };

      // Exceed rate limit
      for (let i = 0; i < 10; i++) {
        await rateLimiter.applyRateLimit(mockReq as any, mockRes as any);
      }

      // Next request should be blocked
      const result = await rateLimiter.applyRateLimit(
        mockReq as any,
        mockRes as any,
      );
      expect(result).toBe(false);
      expect(mockRes.status).toHaveBeenCalledWith(429);
    });

    it('should get rate limiter statistics', () => {
      const stats = rateLimiter.getStatistics();
      expect(stats).toHaveProperty('totalRules');
      expect(stats).toHaveProperty('totalKeys');
      expect(stats).toHaveProperty('rules');
      expect(Array.isArray(stats.rules)).toBe(true);
    });
  });

  describe('Input Validation', () => {
    it('should validate authentication input correctly', async () => {
      const validInput = {
        emailOrUsername: '<EMAIL>',
        password: 'securePassword123',
        rememberMe: true,
      };

      const result = await inputValidation.validateAuthInput(validInput);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid authentication input', async () => {
      const invalidInput = {
        emailOrUsername: 'ab', // Too short
        password: '123', // Too short
      };

      const result = await inputValidation.validateAuthInput(invalidInput);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should validate OAuth data correctly', async () => {
      const validOAuthData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        googleId: '123456789',
        provider: 'google',
        accessToken: 'valid-access-token-12345',
      };

      const result = await inputValidation.validateOAuthData(validOAuthData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect XSS attempts', async () => {
      const maliciousInput = {
        emailOrUsername: '<script>alert("xss")</script>',
        password: 'password123',
      };

      const result = await inputValidation.validateAuthInput(maliciousInput);
      // The input should be validated and potentially flagged for security issues
      expect(result).toBeDefined();
    });

    it('should validate URL parameters', () => {
      const validParams = {
        redirect_uri: 'http://localhost:3000/auth/callback',
        state: 'valid-state-parameter',
      };

      const result = inputValidation.validateUrlParameters(validParams);
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid redirect URLs', () => {
      const invalidParams = {
        redirect_uri: 'http://malicious-site.com/steal-tokens',
        state: 'state-parameter',
      };

      const result = inputValidation.validateUrlParameters(invalidParams);
      expect(result.isValid).toBe(false);
      expect(result.errors.some((e) => e.code === 'DOMAIN_NOT_ALLOWED')).toBe(
        true,
      );
    });
  });

  describe('Error Monitoring', () => {
    it('should report errors correctly', async () => {
      const testError = new Error('Test error for monitoring');
      const context = {
        userId: 'test-user-123',
        ipAddress: '***********',
        endpoint: '/api/v1/auth/test',
        method: 'POST',
      };

      const errorId = await errorMonitoring.reportError(
        testError,
        context,
        { testData: 'test-value' },
        'MEDIUM',
      );

      expect(errorId).toBeDefined();
      expect(errorId.startsWith('err_')).toBe(true);

      // Verify error was stored
      const storedError = errorMonitoring.getError(errorId);
      expect(storedError).toBeDefined();
      expect(storedError?.message).toBe('Test error for monitoring');
      expect(storedError?.level).toBe('MEDIUM');
    });

    it('should track authentication errors', async () => {
      const authError = new Error('Invalid credentials');
      const context = {
        ipAddress: '***********',
        endpoint: '/api/v1/auth/login',
      };
      const authContext = {
        attemptedEmail: '<EMAIL>',
        failureReason: 'invalid_password',
      };

      const errorId = await errorMonitoring.reportAuthError(
        authError,
        context,
        authContext,
      );

      expect(errorId).toBeDefined();

      const storedError = errorMonitoring.getError(errorId);
      expect(storedError?.type).toBe('AUTHENTICATION_ERROR');
    });

    it('should get error metrics', () => {
      const metrics = errorMonitoring.getMetrics();
      expect(metrics).toHaveProperty('totalErrors');
      expect(metrics).toHaveProperty('errorsByLevel');
      expect(metrics).toHaveProperty('errorsByType');
      expect(metrics).toHaveProperty('recentErrors');
    });

    it('should search errors', () => {
      const errors = errorMonitoring.searchErrors('test', {
        level: 'MEDIUM',
      });

      expect(Array.isArray(errors)).toBe(true);
    });
  });

  describe('Audit Logging', () => {
    it('should log authentication events', async () => {
      const mockReq = {
        ip: '***********',
        headers: { 'user-agent': 'Test Browser' },
        url: '/api/v1/auth/login',
      };

      await auditLogging.logAuthEvent(
        'LOGIN_ATTEMPT',
        mockReq as any,
        true,
        'test-user-123',
        { provider: 'local' },
      );

      // Verify logging doesn't throw errors
      expect(true).toBe(true);
    });

    it('should log OAuth events', async () => {
      const mockReq = {
        ip: '***********',
        headers: { 'user-agent': 'Test Browser' },
        url: '/api/v1/auth/google/callback',
        query: { redirect_uri: 'http://localhost:3000' },
      };

      await auditLogging.logOAuthEvent(
        'google',
        mockReq as any,
        true,
        'test-user-123',
      );

      // Verify logging doesn't throw errors
      expect(true).toBe(true);
    });

    it('should log security events', async () => {
      const securityEvent = {
        type: 'RATE_LIMIT_EXCEEDED' as const,
        userId: 'test-user-123',
        ipAddress: '***********',
        userAgent: 'Test Browser',
        details: {
          rule: 'login-attempts',
          limit: 5,
          current: 6,
        },
        risk: 'MEDIUM' as const,
        timestamp: new Date(),
      };

      await auditLogging.logSecurityEvent(securityEvent);

      // Verify logging doesn't throw errors
      expect(true).toBe(true);
    });
  });

  describe('Token Validation API', () => {
    it('should handle token validation requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/tokens/validate')
        .send({
          token: 'invalid-token',
          tokenType: 'access',
        });

      // Should not crash and return proper error structure
      expect(response.status).toBeDefined();
      expect([400, 401].includes(response.status)).toBe(true);
    });

    it('should handle token introspection requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/tokens/introspect')
        .set('Authorization', 'Basic dGVzdDp0ZXN0') // test:test in base64
        .send('token=invalid-token&token_type_hint=access_token')
        .set('Content-Type', 'application/x-www-form-urlencoded');

      expect(response.status).toBeDefined();
      expect(response.body).toHaveProperty('active');
      expect(response.body.active).toBe(false);
    });

    it('should handle token revocation requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/auth/tokens/revoke')
        .send({
          token: 'test-token-to-revoke',
          token_type_hint: 'access_token',
        });

      // Should handle the request gracefully
      expect([200, 400].includes(response.status)).toBe(true);
    });
  });

  describe('OAuth Flow Integration', () => {
    it('should redirect to Google OAuth', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/auth/google')
        .expect(302);

      expect(response.headers.location).toContain('accounts.google.com');
    });

    it('should handle OAuth callback errors gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/auth/google/callback')
        .query({ error: 'access_denied' });

      // Should handle the error and return appropriate response
      expect(response.status).toBeDefined();
      expect([400, 401, 500].includes(response.status)).toBe(true);
    });

    it('should handle missing OAuth data', async () => {
      const response = await request(app.getHttpServer()).get(
        '/api/v1/auth/google/callback',
      );

      // Should handle missing data gracefully
      expect(response.status).toBeDefined();
      expect([400, 401, 500].includes(response.status)).toBe(true);
    });
  });

  describe('Security Headers', () => {
    it('should include security headers in responses', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/auth/google/callback')
        .query({ error: 'test' });

      // Check for common security headers
      expect(response.headers).toBeDefined();
    });
  });

  describe('Service Integration', () => {
    it('should have all required services available', () => {
      expect(corsHandler).toBeDefined();
      expect(rateLimiter).toBeDefined();
      expect(auditLogging).toBeDefined();
      expect(inputValidation).toBeDefined();
      expect(errorMonitoring).toBeDefined();
    });

    it('should handle service interactions correctly', async () => {
      // Test that services work together without conflicts
      const mockReq = {
        ip: '***********00',
        path: '/api/v1/auth/test',
        method: 'POST',
        headers: { origin: 'http://localhost:3000' },
        user: null,
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        header: jest.fn(),
      };

      // Test CORS + Rate Limiting interaction
      const corsResult = corsHandler.applyCorsHeaders(
        mockReq as any,
        mockRes as any,
      );
      const rateLimitResult = await rateLimiter.applyRateLimit(
        mockReq as any,
        mockRes as any,
      );

      expect(corsResult).toBe(true);
      expect(rateLimitResult).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Test that the system doesn't crash when services encounter errors
      const invalidInput = null;

      try {
        await inputValidation.validateAuthInput(invalidInput as any);
      } catch (error) {
        expect(error).toBeDefined();
        // Error should be handled gracefully
      }
    });

    it('should maintain service availability during errors', async () => {
      // Verify that one service error doesn't affect others
      const testError = new Error('Test service error');

      try {
        await errorMonitoring.reportError(testError, {}, {}, 'LOW');
      } catch (error) {
        // Error monitoring should not throw
        fail('Error monitoring should handle errors gracefully');
      }

      // Other services should still work
      const corsResult = corsHandler.isOriginAllowed('http://localhost:3000');
      expect(typeof corsResult).toBe('boolean');
    });
  });
});

describe('Performance Tests', () => {
  let app: INestApplication;
  let rateLimiter: RateLimiterService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule.forRoot({ isGlobal: true }), AuthModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    rateLimiter = moduleFixture.get<RateLimiterService>(RateLimiterService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should handle high load rate limiting', async () => {
    const startTime = Date.now();
    const promises = [];

    const mockReq = {
      ip: '*************',
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {},
      user: null,
    };
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      header: jest.fn(),
    };

    // Test 100 concurrent rate limit checks
    for (let i = 0; i < 100; i++) {
      promises.push(
        rateLimiter.applyRateLimit(
          { ...mockReq, ip: `192.168.1.${200 + i}` } as any,
          mockRes as any,
        ),
      );
    }

    const results = await Promise.all(promises);
    const endTime = Date.now();

    expect(results).toHaveLength(100);
    expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
  });
});
