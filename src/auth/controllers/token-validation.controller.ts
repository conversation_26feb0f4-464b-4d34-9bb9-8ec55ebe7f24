import {
  Controller,
  Post,
  Get,
  Body,
  Headers,
  HttpC<PERSON>,
  HttpStatus,
  UseGuards,
  Request,
  Logger,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiHeader,
} from '@nestjs/swagger';
import { TokenManagementService } from '../services/token-management.service';
import { TokenSecurityService } from '../services/token-security.service';
import { AuditLoggingService } from '../services/audit-logging.service';
import { RateLimiterService } from '../services/rate-limiter.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

export class TokenValidationRequestDto {
  token: string;
  tokenType?: 'access' | 'refresh' = 'access';
}

export class TokenValidationResponseDto {
  isValid: boolean;
  isExpired: boolean;
  isBlacklisted?: boolean;
  expiresAt?: Date;
  issuedAt?: Date;
  userId?: string;
  tokenVersion?: number;
  scopes?: string[];
  deviceId?: string;
  sessionId?: string;
  remainingTime?: number;
  securityChecks: {
    signatureValid: boolean;
    notExpired: boolean;
    notBlacklisted: boolean;
    userActive: boolean;
    deviceTrusted: boolean;
  };
}

export class TokenIntrospectionDto {
  active: boolean;
  scope?: string;
  client_id?: string;
  username?: string;
  token_type?: string;
  exp?: number;
  iat?: number;
  nbf?: number;
  sub?: string;
  aud?: string;
  iss?: string;
  jti?: string;
}

export class TokenRevocationRequestDto {
  token: string;
  token_type_hint?: 'access_token' | 'refresh_token';
}

@ApiTags('Token Validation')
@Controller('auth/tokens')
export class TokenValidationController {
  private readonly logger = new Logger(TokenValidationController.name);

  constructor(
    private readonly tokenManagement: TokenManagementService,
    private readonly tokenSecurity: TokenSecurityService,
    private readonly auditLogging: AuditLoggingService,
    private readonly rateLimiter: RateLimiterService,
  ) {}

  @Post('validate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Validate access or refresh token',
    description: 'Comprehensive token validation with security checks',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token validation result',
    type: TokenValidationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request format',
  })
  @ApiResponse({
    status: HttpStatus.TOO_MANY_REQUESTS,
    description: 'Rate limit exceeded',
  })
  async validateToken(
    @Body() request: TokenValidationRequestDto,
    @Request() req: any,
  ): Promise<TokenValidationResponseDto> {
    // Apply rate limiting
    const rateLimitPassed = await this.rateLimiter.applyRateLimit(req, req.res);
    if (!rateLimitPassed) {
      throw new UnauthorizedException('Rate limit exceeded');
    }

    if (!request.token) {
      throw new BadRequestException('Token is required');
    }

    try {
      const validation = await this.performTokenValidation(
        request.token,
        request.tokenType,
        req,
      );

      // Log validation attempt
      await this.auditLogging.logTokenEvent(
        'VALIDATE',
        req,
        validation.isValid,
        validation.userId,
        request.tokenType?.toUpperCase() as any,
        {
          isExpired: validation.isExpired,
          isBlacklisted: validation.isBlacklisted,
        },
      );

      return validation;
    } catch (error) {
      this.logger.error('Token validation error:', error);

      await this.auditLogging.logTokenEvent(
        'VALIDATE',
        req,
        false,
        undefined,
        request.tokenType?.toUpperCase() as any,
        { error: error.message },
      );

      throw new BadRequestException('Token validation failed');
    }
  }

  @Post('introspect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'OAuth 2.0 token introspection (RFC 7662)',
    description: 'Standard OAuth 2.0 introspection endpoint',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Basic authentication for client credentials',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token introspection result',
    type: TokenIntrospectionDto,
  })
  async introspectToken(
    @Body('token') token: string,
    @Body('token_type_hint') tokenTypeHint?: string,
    @Headers('authorization') authorization?: string,
    @Request() req?: any,
  ): Promise<TokenIntrospectionDto> {
    // Validate client credentials
    if (!this.validateClientCredentials(authorization)) {
      throw new UnauthorizedException('Invalid client credentials');
    }

    if (!token) {
      throw new BadRequestException('Token parameter is required');
    }

    try {
      const validation = await this.performTokenValidation(
        token,
        tokenTypeHint as any,
        req,
      );

      const introspection: TokenIntrospectionDto = {
        active: validation.isValid && !validation.isExpired,
      };

      if (validation.isValid && !validation.isExpired) {
        introspection.scope = 'read write';
        introspection.client_id = 'orbitum-client';
        introspection.username = validation.userId;
        introspection.token_type = 'Bearer';
        introspection.exp = validation.expiresAt
          ? Math.floor(validation.expiresAt.getTime() / 1000)
          : undefined;
        introspection.iat = validation.issuedAt
          ? Math.floor(validation.issuedAt.getTime() / 1000)
          : undefined;
        introspection.sub = validation.userId;
        introspection.aud = 'orbitum-api';
        introspection.iss = 'orbitum-auth';
      }

      await this.auditLogging.logTokenEvent(
        'VALIDATE',
        req,
        introspection.active,
        validation.userId,
        'ACCESS',
        { introspection: true },
      );

      return introspection;
    } catch (error) {
      this.logger.error('Token introspection error:', error);
      return { active: false };
    }
  }

  @Post('revoke')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Revoke access or refresh token',
    description: 'OAuth 2.0 token revocation (RFC 7009)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token revoked successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request',
  })
  async revokeToken(
    @Body() request: TokenRevocationRequestDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    if (!request.token) {
      throw new BadRequestException('Token is required');
    }

    try {
      // Determine token type
      const isRefreshToken = request.token_type_hint === 'refresh_token';

      if (isRefreshToken) {
        await this.tokenManagement.revokeRefreshToken(request.token);
      } else {
        // For access tokens, blacklist them
        const validation = await this.performTokenValidation(
          request.token,
          'access',
          req,
        );

        if (validation.userId) {
          await this.tokenManagement.blacklistAccessToken(
            request.token,
            validation.userId,
            'Manual revocation',
          );
        }
      }

      await this.auditLogging.logTokenEvent(
        'REVOKE',
        req,
        true,
        undefined,
        isRefreshToken ? 'REFRESH' : 'ACCESS',
        { manual: true },
      );

      return { message: 'Token revoked successfully' };
    } catch (error) {
      this.logger.error('Token revocation error:', error);

      await this.auditLogging.logTokenEvent(
        'REVOKE',
        req,
        false,
        undefined,
        'ACCESS',
        { error: error.message },
      );

      throw new BadRequestException('Token revocation failed');
    }
  }

  @Get('info')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get current token information',
    description: 'Get information about the currently authenticated token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token information',
    type: TokenValidationResponseDto,
  })
  async getTokenInfo(@Request() req: any): Promise<TokenValidationResponseDto> {
    const token = this.extractTokenFromRequest(req);

    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    const validation = await this.performTokenValidation(token, 'access', req);

    await this.auditLogging.logTokenEvent(
      'VALIDATE',
      req,
      validation.isValid,
      validation.userId,
      'ACCESS',
      { endpoint: 'info' },
    );

    return validation;
  }

  @Post('refresh-info')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get refresh token information',
    description: 'Validate and get information about a refresh token',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Refresh token information',
  })
  async getRefreshTokenInfo(
    @Body('refreshToken') refreshToken: string,
    @Request() req: any,
  ): Promise<{
    isValid: boolean;
    expiresAt?: Date;
    userId?: string;
    deviceId?: string;
    lastUsed?: Date;
  }> {
    if (!refreshToken) {
      throw new BadRequestException('Refresh token is required');
    }

    try {
      const tokenData =
        await this.tokenManagement.validateRefreshToken(refreshToken);

      const isValid = !!tokenData && tokenData.expiresAt > new Date();

      await this.auditLogging.logTokenEvent(
        'VALIDATE',
        req,
        isValid,
        tokenData?.userId,
        'REFRESH',
        { endpoint: 'refresh-info' },
      );

      if (!isValid) {
        return { isValid: false };
      }

      return {
        isValid: true,
        expiresAt: tokenData.expiresAt,
        userId: tokenData.userId,
        deviceId: tokenData.deviceId,
        lastUsed: tokenData.lastUsedAt,
      };
    } catch (error) {
      this.logger.error('Refresh token validation error:', error);
      return { isValid: false };
    }
  }

  /**
   * Perform comprehensive token validation
   */
  private async performTokenValidation(
    token: string,
    tokenType: 'access' | 'refresh' = 'access',
    req: any,
  ): Promise<TokenValidationResponseDto> {
    const result: TokenValidationResponseDto = {
      isValid: false,
      isExpired: false,
      securityChecks: {
        signatureValid: false,
        notExpired: false,
        notBlacklisted: false,
        userActive: false,
        deviceTrusted: false,
      },
    };

    try {
      if (tokenType === 'refresh') {
        return await this.validateRefreshToken(token, result);
      } else {
        return await this.validateAccessToken(token, result, req);
      }
    } catch (error) {
      this.logger.error('Token validation error:', error);
      return result;
    }
  }

  /**
   * Validate access token
   */
  private async validateAccessToken(
    token: string,
    result: TokenValidationResponseDto,
    req: any,
  ): Promise<TokenValidationResponseDto> {
    // Check if token is blacklisted
    const isBlacklisted =
      await this.tokenManagement.isAccessTokenBlacklisted(token);
    result.isBlacklisted = isBlacklisted;
    result.securityChecks.notBlacklisted = !isBlacklisted;

    if (isBlacklisted) {
      return result;
    }

    // Validate token signature and extract payload
    const tokenValidation = await this.tokenSecurity.validateAccessToken(token);
    result.securityChecks.signatureValid = tokenValidation.isValid;

    if (!tokenValidation.isValid) {
      return result;
    }

    const payload = tokenValidation.decoded?.payload;
    if (payload) {
      result.userId = payload.sub;
      result.tokenVersion = payload.tokenVersion;
      result.issuedAt = new Date(payload.iat * 1000);
      result.expiresAt = new Date(payload.exp * 1000);

      // Check expiration
      const now = Date.now();
      result.isExpired = payload.exp * 1000 < now;
      result.securityChecks.notExpired = !result.isExpired;
      result.remainingTime = Math.max(0, payload.exp * 1000 - now);
    }

    if (result.isExpired) {
      return result;
    }

    // Additional security checks
    result.securityChecks.userActive = true; // TODO: Check user status
    result.securityChecks.deviceTrusted = true; // TODO: Check device trust

    result.isValid =
      result.securityChecks.signatureValid &&
      result.securityChecks.notExpired &&
      result.securityChecks.notBlacklisted &&
      result.securityChecks.userActive;

    return result;
  }

  /**
   * Validate refresh token
   */
  private async validateRefreshToken(
    token: string,
    result: TokenValidationResponseDto,
  ): Promise<TokenValidationResponseDto> {
    const tokenData = await this.tokenManagement.validateRefreshToken(token);

    if (!tokenData) {
      return result;
    }

    result.userId = tokenData.userId;
    result.expiresAt = tokenData.expiresAt;
    result.issuedAt = tokenData.createdAt;
    result.deviceId = tokenData.deviceId;

    // Check expiration
    const now = new Date();
    result.isExpired = tokenData.expiresAt < now;
    result.securityChecks.notExpired = !result.isExpired;
    result.remainingTime = Math.max(
      0,
      tokenData.expiresAt.getTime() - now.getTime(),
    );

    // Check if revoked
    result.isBlacklisted = tokenData.isRevoked;
    result.securityChecks.notBlacklisted = !tokenData.isRevoked;

    result.securityChecks.signatureValid = true; // Refresh tokens are stored, not JWTs
    result.securityChecks.userActive = true; // TODO: Check user status
    result.securityChecks.deviceTrusted = true; // TODO: Check device trust

    result.isValid =
      result.securityChecks.notExpired &&
      result.securityChecks.notBlacklisted &&
      result.securityChecks.userActive;

    return result;
  }

  /**
   * Extract token from Authorization header
   */
  private extractTokenFromRequest(req: any): string | null {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * Validate client credentials for introspection endpoint
   */
  private validateClientCredentials(authorization: string): boolean {
    // TODO: Implement proper client credential validation
    // For now, just check if authorization header is present
    return !!authorization && authorization.startsWith('Basic ');
  }
}
