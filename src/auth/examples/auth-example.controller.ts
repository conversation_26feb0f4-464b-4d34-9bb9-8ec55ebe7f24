import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { AuthorizationGuard, ROLES_KEY } from '../guards/authorization.guard';
import { Roles, RequireActiveAccount } from '../decorators/roles.decorator';
import { CurrentUser } from '../decorators/user-info.decorator';
import { UserInfo } from '../interfaces/user-info.interface';
import { UserType } from '../../../generated/prisma';

@Controller('api/v1/example')
export class AuthExampleController {
  // Example 1: Basic middleware usage
  @Get('profile')
  @UseGuards(AuthorizationGuard)
  async getProfile(@CurrentUser() user: UserInfo) {
    return {
      message: 'Profile accessed successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        type: user.type,
        businessType: user.businessType,
        applicantId: user.applicantId,
        status: user.status,
      },
    };
  }

  // Example 2: Get specific user property
  @Get('email')
  @UseGuards(AuthorizationGuard)
  async getEmail(@CurrentUser('email') email: string) {
    return {
      message: 'Email retrieved successfully',
      email,
    };
  }

  // Example 3: Role-based authorization
  @Get('business-only')
  @UseGuards(AuthorizationGuard)
  @Roles(UserType.BUSINESS)
  async businessOnly(@CurrentUser() user: UserInfo) {
    return {
      message: 'Business resource accessed',
      user: {
        id: user.id,
        businessType: user.businessType,
      },
    };
  }

  // Example 4: Active account required
  @Get('active-only')
  @UseGuards(AuthorizationGuard)
  @RequireActiveAccount()
  async activeOnly(@CurrentUser() user: UserInfo) {
    return {
      message: 'Active account resource accessed',
      user: {
        id: user.id,
        status: user.status,
      },
    };
  }

  // Example 5: Multiple role requirements
  @Get('admin-or-business')
  @UseGuards(AuthorizationGuard)
  @Roles(UserType.BUSINESS)
  async adminOrBusiness(@CurrentUser() user: UserInfo) {
    return {
      message: 'Business resource accessed',
      user: {
        id: user.id,
        type: user.type,
      },
    };
  }

  // Example 6: Combined decorators
  @Post('create-business-resource')
  @UseGuards(AuthorizationGuard)
  @Roles(UserType.BUSINESS)
  @RequireActiveAccount()
  async createBusinessResource(
    @CurrentUser() user: UserInfo,
    @Body() createResourceDto: any,
  ) {
    return {
      message: 'Business resource created',
      createdBy: {
        id: user.id,
        username: user.username,
        businessType: user.businessType,
      },
      resource: createResourceDto,
    };
  }
}
