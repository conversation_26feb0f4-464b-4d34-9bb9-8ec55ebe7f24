import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TokenManagementService } from '../services/token-management.service';
import { UsersService } from '../../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    configService: ConfigService,
    private readonly tokenManagementService: TokenManagementService,
    private readonly usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'your-secret-key',
      passReqToCallback: true, // Enable req parameter
    });
  }

  async validate(req: any, payload: any) {
    try {
      // Extract token from header
      const token = this.extractTokenFromRequest(req);
      if (!token) {
        throw new UnauthorizedException('No token provided');
      }

      // Check if token is blacklisted
      const isBlacklisted =
        await this.tokenManagementService.isAccessTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException('Token has been revoked');
      }

      // Validate user exists and is active
      const user = await this.usersService.findByEmail(payload.email);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      if (user.status !== 'ACTIVE') {
        throw new UnauthorizedException('User account is not active');
      }

      // Check token version for global revocation
      if (payload.tokenVersion !== user.tokenVersion) {
        throw new UnauthorizedException('Token version mismatch');
      }

      return {
        id: payload.sub,
        email: payload.email,
        username: payload.username,
        type: payload.type,
        status: payload.status,
        tokenVersion: payload.tokenVersion,
      };
    } catch (error) {
      this.logger.error('JWT validation failed:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromRequest(req: any): string | null {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }
}
