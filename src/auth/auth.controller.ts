import {
  Controller,
  Post,
  Get,
  Body,
  Request,
  HttpCode,
  HttpStatus,
  UseGuards,
  UseInterceptors,
  Req,
  Re<PERSON>,
  Logger,
} from '@nestjs/common';

import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ClassSerializerInterceptor } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { TokenPairDto } from './dto/token-pair.dto';
import { DeviceInfoDto } from './dto/device-info.dto';
import { UserResponseDto } from '../users/dto/user-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { OAuthHtmlTemplateService } from './services/oauth-html-template.service';
import { OAuthSecurityService } from './services/oauth-security.service';
import { OAuthResponseHandlerService } from './services/oauth-response-handler.service';

import { OAuthApiResponseDto } from './dto/oauth-redirect.dto';

@ApiTags('Authentication')
@Controller('auth')
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly oauthHtmlTemplateService: OAuthHtmlTemplateService,
    private readonly oauthSecurityService: OAuthSecurityService,
    private readonly oauthResponseHandler: OAuthResponseHandlerService,
  ) {
    // Validate environment on startup
    this.oauthSecurityService.logEnvironmentValidation();
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description: 'Authenticate user with email/username and password',
  })
  @ApiOperation({
    summary: 'User login with token pair generation',
    description:
      'Authenticate user with email/username and password, and generate token pair',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: LoginResponseDto,
    description: 'Login successful',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: any,
  ): Promise<LoginResponseDto> {
    const deviceInfo = this.extractDeviceInfo(req);
    return this.authService.login(loginDto, deviceInfo);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get user profile',
    description: 'Get current authenticated user profile',
  })
  async getProfile(@Request() req: any): Promise<UserResponseDto> {
    const user = await this.usersService.findByEmail(req.user.email);
    return new UserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as any,
      businessType: user.businessType as any,
      status: user.status as any,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      password: user.password,
    });
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({
    summary: 'Initiate Google OAuth login',
    description: 'Redirects user to Google OAuth consent screen',
  })
  @ApiResponse({
    status: HttpStatus.FOUND,
    description: 'Redirects to Google OAuth',
  })
  async googleAuth() {
    // This method initiates the OAuth flow
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({
    summary: 'Google OAuth callback with dual response support',
    description:
      'Handles the callback from Google OAuth. Returns either JSON API response or HTML redirect based on request headers.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OAuth login successful - returns JSON API response',
    type: OAuthApiResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OAuth login successful - returns HTML redirect page',
    content: {
      'text/html': {
        schema: {
          type: 'string',
          description: 'HTML redirect page with embedded tokens',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'OAuth authentication failed',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error during OAuth process',
  })
  async googleAuthRedirect(@Req() req: any, @Res() res: any) {
    try {
      const { user } = req;
      const deviceInfo = this.extractDeviceInfo(req);

      // Analyze request to determine response type
      const requestContext = this.oauthResponseHandler.analyzeRequest(req);

      if (!user) {
        return this.oauthResponseHandler.handleError(
          res,
          'AUTHENTICATION_FAILED',
          'Authentication failed',
          'No user data received',
          requestContext,
        );
      }

      // Get OAuth validation result with token pair
      const { user: validatedUser, tokenPair } =
        await this.authService.validateOAuthLogin(user, deviceInfo);

      const userResponse = this.authService.createUserResponse(validatedUser);

      // Prepare response data
      const responseData = {
        user: userResponse,
        tokenPair,
        isNewUser: this.isNewUser(validatedUser),
        provider: 'google',
      };

      // Determine redirect type
      const isAccountLinking = this.isAccountLinking();

      if (isAccountLinking) {
        return this.oauthResponseHandler.handleAccountLinking(
          res,
          responseData,
          requestContext,
        );
      } else {
        return this.oauthResponseHandler.handleSuccess(
          res,
          responseData,
          requestContext,
        );
      }
    } catch (error) {
      this.logger.error('OAuth callback error:', error);
      const requestContext = this.oauthResponseHandler.analyzeRequest(req);
      return this.oauthResponseHandler.handleError(
        res,
        'INTERNAL_SERVER_ERROR',
        'Authentication failed',
        error.message,
        requestContext,
      );
    }
  }

  // Utility methods
  private extractDeviceInfo(req: any): DeviceInfoDto {
    return {
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || req.connection.remoteAddress,
      deviceId: req.headers['x-device-id'], // Optional custom header
    };
  }

  private isNewUser(user: any): boolean {
    // Check if user was created recently (within last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return user.createdAt > fiveMinutesAgo;
  }

  private isAccountLinking(): boolean {
    // Account linking is not currently implemented
    // TODO: Implement account linking logic when needed
    return false;
  }

  private extractTokenFromHeader(req: any): string {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: HttpStatus.OK, type: TokenPairDto })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() req: any,
  ): Promise<TokenPairDto> {
    const deviceInfo = this.extractDeviceInfo(req);
    return this.authService.refreshToken(refreshTokenDto, deviceInfo);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Logout user and revoke tokens' })
  async logout(
    @Request() req: any,
    @Body() body: { refreshToken: string },
  ): Promise<{ message: string }> {
    const accessToken = this.extractTokenFromHeader(req);
    return this.authService.logout(accessToken, body.refreshToken, req.user.id);
  }

  @Post('logout-all')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Logout from all devices' })
  async logoutFromAllDevices(
    @Request() req: any,
  ): Promise<{ message: string }> {
    return this.authService.logoutFromAllDevices(req.user.id);
  }
}
