import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { UserInfo } from '../interfaces/user-info.interface';

export const CurrentUser = createParamDecorator(
  (data: keyof UserInfo | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as UserInfo;

    if (data) {
      return user?.[data];
    }

    return user;
  },
);
