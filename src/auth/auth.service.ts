import {
  Injectable,
  UnauthorizedException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';

import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { TokenPairDto } from './dto/token-pair.dto';
import { UserResponseDto } from '../users/dto/user-response.dto';

import { TokenManagementService } from './services/token-management.service';
import { TokenRotationService } from './services/token-rotation.service';
import { DeviceInfoDto } from './dto/device-info.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly tokenManagementService: TokenManagementService,
    private readonly tokenRotationService: TokenRotationService,
  ) {}

  async validateUser(emailOrUsername: string, password: string): Promise<any> {
    try {
      const user =
        await this.usersService.findUserByEmailOrUsername(emailOrUsername);

      if (!user) {
        return null;
      }

      if (user.status !== 'ACTIVE') {
        return null;
      }
      const isPasswordValid = await bcrypt.compare(password, user.password);

      return isPasswordValid ? user : null;
    } catch (error) {
      console.error('Error in validateUser:', error);
      return null;
    }
  }

  async login(
    loginDto: LoginDto,
    deviceInfo: DeviceInfoDto,
  ): Promise<LoginResponseDto> {
    const { emailOrUsername, password } = loginDto;

    if (!emailOrUsername || !password) {
      throw new UnauthorizedException(
        'Email/username and password are required',
      );
    }

    const user = await this.validateUser(emailOrUsername, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const userResponse = this.createUserResponse(user);

    // Update last login timestamp (if method exists)
    if (
      'updateLastLogin' in this.usersService &&
      typeof this.usersService.updateLastLogin === 'function'
    ) {
      await (this.usersService as any).updateLastLogin(user.id);
    }

    // Generate token pair
    const tokenPair = await this.tokenManagementService.generateTokenPair(
      user,
      deviceInfo,
    );

    return new LoginResponseDto(tokenPair, userResponse);
  }

  async refreshToken(
    refreshTokenDto: RefreshTokenDto,
    deviceInfo: DeviceInfoDto,
  ): Promise<TokenPairDto> {
    const { refreshToken } = refreshTokenDto;

    // Validate device fingerprint for additional security
    const isValidDevice =
      await this.tokenRotationService.validateDeviceFingerprint(
        refreshToken,
        deviceInfo,
      );

    if (!isValidDevice) {
      await this.tokenManagementService.revokeRefreshToken(refreshToken);
      throw new UnauthorizedException('Invalid device signature');
    }

    // Rotate token (generate new refresh token)
    return this.tokenRotationService.rotateRefreshToken(
      refreshToken,
      deviceInfo,
    );
  }

  /**
   * Enhanced logout with token revocation
   */
  async logout(
    accessToken: string,
    refreshToken: string,
    userId: string,
  ): Promise<{ message: string }> {
    try {
      // Blacklist current access token
      await this.tokenManagementService.blacklistAccessToken(
        accessToken,
        userId,
        'User logout',
      );

      // Revoke refresh token
      await this.tokenManagementService.revokeRefreshToken(refreshToken);

      this.logger.log(`User ${userId} logged out successfully`);

      return { message: 'Logged out successfully' };
    } catch (error) {
      this.logger.error('Logout error:', error);
      throw new InternalServerErrorException('Logout failed');
    }
  }

  async logoutFromAllDevices(userId: string): Promise<{ message: string }> {
    await this.tokenManagementService.revokeAllUserTokens(userId);

    this.logger.log(`User ${userId} logged out from all devices`);

    return { message: 'Logged out from all devices successfully' };
  }

  async validateOAuthLogin(
    oauthData: any,
    deviceInfo: DeviceInfoDto,
  ): Promise<{ user: any; tokenPair: TokenPairDto }> {
    const user = await this.processOAuthUser(oauthData);

    // Generate token pair
    const tokenPair = await this.tokenManagementService.generateTokenPair(
      user,
      deviceInfo,
    );

    return { user, tokenPair };
  }

  async processOAuthUser(oauthData: any): Promise<any> {
    const { email, firstName, lastName, googleId, provider, accessToken } =
      oauthData;

    // Validate required OAuth data
    if (!email || !googleId || !provider || !accessToken) {
      throw new UnauthorizedException('Missing required OAuth data');
    }

    // First, try to find user by Google ID
    let user = await this.usersService.findByGoogleId(googleId);

    if (!user) {
      // If not found by Google ID, try to find by email
      user = await this.usersService.findByEmail(email);

      if (user && this.usersService.linkGoogleAccount) {
        // User exists with this email, link the Google account
        user = await this.usersService.linkGoogleAccount(
          user.id,
          googleId,
          provider,
          accessToken,
        );
      } else if (this.usersService.createOAuthUser) {
        // Create new user
        const username = this.generateUniqueUsername(
          email,
          firstName,
          lastName,
        );
        user = await this.usersService.createOAuthUser({
          email,
          firstName,
          lastName,
          username,
          googleId,
          oauthProvider: provider,
          oauthAccessToken: accessToken,
          type: 'CONSUMER' as any,
          businessType: undefined, // OAuth users default to CONSUMER type
        });
      } else {
        throw new UnauthorizedException('OAuth user creation not supported');
      }
    } else {
      // User exists by Google ID, update the access token
      if (this.usersService.updateOAuthToken) {
        await this.usersService.updateOAuthToken(user.id, accessToken);
      }
    }

    return user;
  }

  // JWT token generation is now handled by TokenManagementService for consistency

  createUserResponse(user: any): UserResponseDto {
    return new UserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as any,
      businessType: user.businessType as any,
      status: user.status as any,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      password: user.password,
    });
  }

  private generateUniqueUsername(
    email: string,
    firstName: string,
    lastName: string,
  ): string {
    const baseUsername = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`;
    const emailPrefix = email.split('@')[0];
    const timestamp = Date.now().toString().slice(-4);

    // Try different username variations
    const candidates = [
      baseUsername,
      `${baseUsername}${timestamp}`,
      emailPrefix,
      `${emailPrefix}${timestamp}`,
    ];

    // Return the first candidate (in a real implementation, you'd check for uniqueness)
    return candidates[0].replace(/[^a-zA-Z0-9_]/g, '_');
  }
}
