import { ApiProperty } from '@nestjs/swagger';

export class TokenPairDto {
  @ApiProperty({ description: 'JWT access token' })
  accessToken: string;

  @ApiProperty({ description: 'Refresh token' })
  refreshToken: string;

  @ApiProperty({ description: 'Access token expiry in seconds' })
  accessTokenExpiresIn: number;

  @ApiProperty({ description: 'Refresh token expiry in seconds' })
  refreshTokenExpiresIn: number;

  @ApiProperty({ description: 'Token type', default: 'Bearer' })
  tokenType: string = 'Bearer';

  constructor(data?: Partial<TokenPairDto>) {
    if (data) {
      this.accessToken = data.accessToken || '';
      this.refreshToken = data.refreshToken || '';
      this.accessTokenExpiresIn = data.accessTokenExpiresIn || 0;
      this.refreshTokenExpiresIn = data.refreshTokenExpiresIn || 0;
      this.tokenType = data.tokenType || 'Bearer';
    }
  }
}
