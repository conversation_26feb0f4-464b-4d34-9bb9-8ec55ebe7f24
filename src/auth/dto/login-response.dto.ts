import { ApiProperty } from '@nestjs/swagger';
import { UserResponseDto } from '../../users/dto/user-response.dto';
import { TokenPairDto } from './token-pair.dto';

export class LoginResponseDto extends TokenPairDto {
  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  user: UserResponseDto;

  constructor(tokenPair: TokenPairDto, user: UserResponseDto) {
    super(tokenPair);
    this.user = user;
  }
}
