import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface SecurityHeaders {
  'Content-Type': string;
  'Cache-Control': string;
  Pragma: string;
  Expires: string;
  'X-Content-Type-Options': string;
  'X-Frame-Options': string;
  'X-XSS-Protection': string;
  'Referrer-Policy': string;
  'Content-Security-Policy': string;
}

export interface EnvironmentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable()
export class OAuthSecurityService {
  private readonly logger = new Logger(OAuthSecurityService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Get security headers for OAuth redirect responses
   */
  getSecurityHeaders(): SecurityHeaders {
    const frontendUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
    const frontendOrigin = new URL(frontendUrl).origin;

    return {
      'Content-Type': 'text/html; charset=utf-8',
      'Cache-Control': 'no-cache, no-store, must-revalidate, private',
      Pragma: 'no-cache',
      Expires: '0',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': this.generateCSP(frontendOrigin),
    };
  }

  /**
   * Generate Content Security Policy header
   */
  private generateCSP(frontendOrigin: string): string {
    const isProduction =
      this.configService.get<string>('NODE_ENV') === 'production';

    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'", // Allow inline scripts for redirect functionality
      "style-src 'self' 'unsafe-inline'", // Allow inline styles for embedded CSS
      "img-src 'self' data:",
      "font-src 'self'",
      `connect-src 'self' ${frontendOrigin}`,
      "object-src 'none'",
      "media-src 'none'",
      "frame-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];

    if (isProduction) {
      cspDirectives.push('upgrade-insecure-requests');
    }

    return cspDirectives.join('; ');
  }

  /**
   * Validate environment variables required for OAuth
   */
  validateEnvironment(): EnvironmentValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required environment variables
    const requiredVars = [
      'FRONTEND_URL',
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'GOOGLE_CALLBACK_URL',
      'JWT_SECRET',
    ];

    for (const varName of requiredVars) {
      const value = this.configService.get<string>(varName);
      if (!value) {
        errors.push(`Missing required environment variable: ${varName}`);
      } else if (
        varName === 'JWT_SECRET' &&
        value === 'your-super-secret-jwt-key'
      ) {
        warnings.push(
          'JWT_SECRET is using default value - change this in production',
        );
      }
    }

    // Validate FRONTEND_URL format
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    if (frontendUrl) {
      try {
        const url = new URL(frontendUrl);
        if (!['http:', 'https:'].includes(url.protocol)) {
          errors.push('FRONTEND_URL must use HTTP or HTTPS protocol');
        }
        if (url.pathname !== '/') {
          warnings.push('FRONTEND_URL should not include a path');
        }
      } catch (error) {
        errors.push(`Invalid FRONTEND_URL format: ${error.message}`);
      }
    }

    // Validate Google OAuth configuration
    const googleCallbackUrl = this.configService.get<string>(
      'GOOGLE_CALLBACK_URL',
    );
    if (googleCallbackUrl) {
      try {
        const url = new URL(googleCallbackUrl);
        if (!url.pathname.includes('/auth/google/callback')) {
          warnings.push(
            'GOOGLE_CALLBACK_URL should point to /auth/google/callback endpoint',
          );
        }
      } catch (error) {
        errors.push(`Invalid GOOGLE_CALLBACK_URL format: ${error.message}`);
      }
    }

    // Production-specific validations
    const nodeEnv = this.configService.get<string>('NODE_ENV');
    if (nodeEnv === 'production') {
      if (frontendUrl && frontendUrl.startsWith('http://')) {
        warnings.push('FRONTEND_URL should use HTTPS in production');
      }
      if (googleCallbackUrl && googleCallbackUrl.startsWith('http://')) {
        warnings.push('GOOGLE_CALLBACK_URL should use HTTPS in production');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate redirect URL for security
   */
  validateRedirectUrl(url: string): { isValid: boolean; error?: string } {
    try {
      const parsedUrl = new URL(url);
      const allowedOrigins = this.getAllowedOrigins();

      // Check if the origin is in the allowed list
      const isAllowed = allowedOrigins.some((origin) => {
        try {
          const allowedUrl = new URL(origin);
          return allowedUrl.origin === parsedUrl.origin;
        } catch {
          return false;
        }
      });

      if (!isAllowed) {
        return {
          isValid: false,
          error: `Redirect URL origin ${parsedUrl.origin} is not in the allowed origins list`,
        };
      }

      // Additional security checks
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return {
          isValid: false,
          error: 'Only HTTP and HTTPS protocols are allowed',
        };
      }

      // Prevent javascript: and data: URLs
      if (['javascript:', 'data:', 'vbscript:'].includes(parsedUrl.protocol)) {
        return {
          isValid: false,
          error: 'Dangerous protocol detected in redirect URL',
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Invalid URL format: ${error.message}`,
      };
    }
  }

  /**
   * Get allowed origins for redirect validation
   */
  private getAllowedOrigins(): string[] {
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    const additionalOrigins = this.configService.get<string>(
      'ALLOWED_ORIGINS',
      '',
    );

    const origins = [frontendUrl];

    if (additionalOrigins) {
      origins.push(
        ...additionalOrigins
          .split(',')
          .map((origin) => origin.trim())
          .filter(Boolean),
      );
    }

    return origins.filter(Boolean);
  }

  /**
   * Apply security headers to response
   */
  applySecurityHeaders(res: any): void {
    const headers = this.getSecurityHeaders();

    Object.entries(headers).forEach(([key, value]) => {
      res.setHeader(key, value);
    });
  }

  /**
   * Log security validation results
   */
  logEnvironmentValidation(): void {
    const validation = this.validateEnvironment();

    if (!validation.isValid) {
      this.logger.error('Environment validation failed:', validation.errors);
    }

    if (validation.warnings.length > 0) {
      this.logger.warn('Environment validation warnings:', validation.warnings);
    }

    if (validation.isValid && validation.warnings.length === 0) {
      this.logger.log('Environment validation passed');
    }
  }

  /**
   * Sanitize data for safe embedding in HTML
   */
  sanitizeForHtml(data: any): any {
    if (typeof data === 'string') {
      return data
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.sanitizeForHtml(item));
    }

    if (data && typeof data === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeForHtml(value);
      }
      return sanitized;
    }

    return data;
  }

  /**
   * Generate secure random state for OAuth flows
   */
  generateSecureState(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate OAuth state parameter
   */
  validateOAuthState(state: string, expectedState: string): boolean {
    if (!state || !expectedState) {
      return false;
    }

    // Use constant-time comparison to prevent timing attacks
    const crypto = require('crypto');
    const stateBuffer = Buffer.from(state, 'utf8');
    const expectedBuffer = Buffer.from(expectedState, 'utf8');

    if (stateBuffer.length !== expectedBuffer.length) {
      return false;
    }

    return crypto.timingSafeEqual(stateBuffer, expectedBuffer);
  }
}
