import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator: (req: Request) => string;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  message: string;
  standardHeaders: boolean;
  legacyHeaders: boolean;
}

export interface RateLimitInfo {
  totalHits: number;
  totalResets: number;
  resetTime: Date;
  remainingPoints: number;
}

export interface RateLimitResult {
  isAllowed: boolean;
  remainingPoints: number;
  resetTime: Date;
  totalHits: number;
  retryAfter?: number;
}

export interface RateLimitRule {
  name: string;
  windowMs: number;
  maxRequests: number;
  keyGenerator: (req: Request) => string;
  condition?: (req: Request) => boolean;
  skipSuccessful?: boolean;
  skipFailed?: boolean;
  message?: string;
}

@Injectable()
export class RateLimiterService {
  private readonly logger = new Logger(RateLimiterService.name);
  private readonly storage = new Map<string, Map<string, RateLimitInfo>>();
  private readonly cleanupInterval: NodeJS.Timeout;

  private readonly defaultRules: RateLimitRule[] = [
    {
      name: 'global',
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 1000,
      keyGenerator: (req) => req.ip || 'unknown',
    },
    {
      name: 'auth-login',
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      keyGenerator: (req) => `login:${req.ip}`,
      condition: (req) => req.path.includes('/auth/login'),
      message: 'Too many login attempts, please try again later',
    },
    {
      name: 'auth-oauth',
      windowMs: 5 * 60 * 1000, // 5 minutes
      maxRequests: 10,
      keyGenerator: (req) => `oauth:${req.ip}`,
      condition: (req) => req.path.includes('/auth/google'),
      message: 'Too many OAuth attempts, please try again later',
    },
    {
      name: 'auth-refresh',
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 20,
      keyGenerator: (req) => `refresh:${req.ip}:${this.getUserId(req)}`,
      condition: (req) => req.path.includes('/auth/refresh'),
      message: 'Too many refresh token requests, please try again later',
    },
    {
      name: 'auth-password-reset',
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3,
      keyGenerator: (req) => `password-reset:${req.body?.email || req.ip}`,
      condition: (req) => req.path.includes('/auth/forgot-password'),
      message: 'Too many password reset attempts, please try again later',
    },
    {
      name: 'api-per-user',
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100,
      keyGenerator: (req) => `user:${this.getUserId(req) || req.ip}`,
      condition: (req) => this.isAuthenticatedRequest(req),
    },
  ];

  constructor(private readonly configService: ConfigService) {
    // Start cleanup interval
    this.cleanupInterval = setInterval(
      () => {
        this.cleanupExpiredEntries();
      },
      5 * 60 * 1000,
    ); // Cleanup every 5 minutes

    this.logger.log('Rate limiter service initialized');
  }

  /**
   * Check rate limit for a request
   */
  async checkRateLimit(req: Request): Promise<RateLimitResult[]> {
    const results: RateLimitResult[] = [];
    const applicableRules = this.getApplicableRules(req);

    for (const rule of applicableRules) {
      const result = await this.checkSingleRule(req, rule);
      results.push(result);

      // If any rule blocks the request, we can stop here
      if (!result.isAllowed) {
        break;
      }
    }

    return results;
  }

  /**
   * Apply rate limiting middleware
   */
  async applyRateLimit(req: Request, res: Response): Promise<boolean> {
    const results = await this.checkRateLimit(req);
    const blockedResult = results.find((result) => !result.isAllowed);

    if (blockedResult) {
      // Set rate limit headers
      this.setRateLimitHeaders(res, blockedResult);

      // Log rate limit violation
      this.logger.warn('Rate limit exceeded', {
        ip: req.ip,
        path: req.path,
        userAgent: req.headers['user-agent'],
        remainingPoints: blockedResult.remainingPoints,
        resetTime: blockedResult.resetTime,
      });

      // Send rate limit response
      res.status(429).json({
        statusCode: 429,
        error: 'Too Many Requests',
        message: this.getRateLimitMessage(req),
        retryAfter: blockedResult.retryAfter,
        resetTime: blockedResult.resetTime,
      });

      return false;
    }

    // Set success rate limit headers
    const primaryResult = results[0];
    if (primaryResult) {
      this.setRateLimitHeaders(res, primaryResult);
    }

    return true;
  }

  /**
   * Check a single rate limit rule
   */
  private async checkSingleRule(
    req: Request,
    rule: RateLimitRule,
  ): Promise<RateLimitResult> {
    const key = rule.keyGenerator(req);
    const ruleStorage = this.getOrCreateRuleStorage(rule.name);
    const now = new Date();
    let rateLimitInfo = ruleStorage.get(key);

    if (!rateLimitInfo || rateLimitInfo.resetTime <= now) {
      // Create new rate limit info
      rateLimitInfo = {
        totalHits: 0,
        totalResets: 0,
        resetTime: new Date(now.getTime() + rule.windowMs),
        remainingPoints: rule.maxRequests,
      };
    }

    // Increment hit count
    rateLimitInfo.totalHits++;
    rateLimitInfo.remainingPoints = Math.max(
      0,
      rule.maxRequests - rateLimitInfo.totalHits,
    );

    // Save updated info
    ruleStorage.set(key, rateLimitInfo);

    const isAllowed = rateLimitInfo.totalHits <= rule.maxRequests;
    const retryAfter = isAllowed
      ? undefined
      : Math.ceil((rateLimitInfo.resetTime.getTime() - now.getTime()) / 1000);

    return {
      isAllowed,
      remainingPoints: rateLimitInfo.remainingPoints,
      resetTime: rateLimitInfo.resetTime,
      totalHits: rateLimitInfo.totalHits,
      retryAfter,
    };
  }

  /**
   * Get applicable rules for a request
   */
  private getApplicableRules(req: Request): RateLimitRule[] {
    return this.defaultRules.filter((rule) => {
      if (rule.condition) {
        return rule.condition(req);
      }
      return true;
    });
  }

  /**
   * Get or create storage for a specific rule
   */
  private getOrCreateRuleStorage(ruleName: string): Map<string, RateLimitInfo> {
    let ruleStorage = this.storage.get(ruleName);
    if (!ruleStorage) {
      ruleStorage = new Map<string, RateLimitInfo>();
      this.storage.set(ruleName, ruleStorage);
    }
    return ruleStorage;
  }

  /**
   * Set rate limit headers on response
   */
  private setRateLimitHeaders(res: Response, result: RateLimitResult): void {
    // Standard headers (RFC 6585)
    res.header('X-RateLimit-Limit', '100');
    res.header('X-RateLimit-Remaining', result.remainingPoints.toString());
    res.header(
      'X-RateLimit-Reset',
      Math.ceil(result.resetTime.getTime() / 1000).toString(),
    );

    // Legacy headers for compatibility
    res.header('X-Rate-Limit-Remaining', result.remainingPoints.toString());
    res.header(
      'X-Rate-Limit-Reset',
      Math.ceil(result.resetTime.getTime() / 1000).toString(),
    );

    if (result.retryAfter) {
      res.header('Retry-After', result.retryAfter.toString());
    }
  }

  /**
   * Get rate limit message for request
   */
  private getRateLimitMessage(req: Request): string {
    const applicableRules = this.getApplicableRules(req);
    for (const rule of applicableRules) {
      if (rule.message) {
        return rule.message;
      }
    }
    return 'Too many requests, please try again later';
  }

  /**
   * Extract user ID from request
   */
  private getUserId(req: Request): string | null {
    if (req.user && (req.user as any).id) {
      return (req.user as any).id;
    }

    // Try to extract from JWT token
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const payload = JSON.parse(
          Buffer.from(token.split('.')[1], 'base64').toString(),
        );
        return payload.sub || payload.userId || payload.id;
      } catch {
        // Invalid token, return null
      }
    }

    return null;
  }

  /**
   * Check if request is authenticated
   */
  private isAuthenticatedRequest(req: Request): boolean {
    return !!(
      req.user ||
      (req.headers.authorization &&
        req.headers.authorization.startsWith('Bearer '))
    );
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [ruleName, ruleStorage] of this.storage.entries()) {
      for (const [key, info] of ruleStorage.entries()) {
        if (info.resetTime <= now) {
          ruleStorage.delete(key);
          cleanedCount++;
        }
      }

      // Remove empty rule storage
      if (ruleStorage.size === 0) {
        this.storage.delete(ruleName);
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(
        `Cleaned up ${cleanedCount} expired rate limit entries`,
      );
    }
  }

  /**
   * Get current rate limit status for a key
   */
  async getRateLimitStatus(
    ruleName: string,
    key: string,
  ): Promise<RateLimitInfo | null> {
    const ruleStorage = this.storage.get(ruleName);
    if (!ruleStorage) {
      return null;
    }

    const info = ruleStorage.get(key);
    if (!info || info.resetTime <= new Date()) {
      return null;
    }

    return info;
  }

  /**
   * Reset rate limit for a specific key
   */
  async resetRateLimit(ruleName: string, key: string): Promise<void> {
    const ruleStorage = this.storage.get(ruleName);
    if (ruleStorage) {
      ruleStorage.delete(key);
      this.logger.log(`Reset rate limit for ${ruleName}:${key}`);
    }
  }

  /**
   * Add custom rate limit rule
   */
  addCustomRule(rule: RateLimitRule): void {
    this.defaultRules.push(rule);
    this.logger.log(`Added custom rate limit rule: ${rule.name}`);
  }

  /**
   * Remove rate limit rule
   */
  removeRule(ruleName: string): void {
    const index = this.defaultRules.findIndex((rule) => rule.name === ruleName);
    if (index > -1) {
      this.defaultRules.splice(index, 1);
      this.storage.delete(ruleName);
      this.logger.log(`Removed rate limit rule: ${ruleName}`);
    }
  }

  /**
   * Get statistics about rate limiting
   */
  getStatistics(): {
    totalRules: number;
    totalKeys: number;
    rules: Array<{ name: string; keyCount: number }>;
  } {
    const rules = [];
    let totalKeys = 0;

    for (const [ruleName, ruleStorage] of this.storage.entries()) {
      const keyCount = ruleStorage.size;
      rules.push({ name: ruleName, keyCount });
      totalKeys += keyCount;
    }

    return {
      totalRules: this.defaultRules.length,
      totalKeys,
      rules,
    };
  }

  /**
   * Create Express middleware for rate limiting
   */
  createMiddleware() {
    return async (req: Request, res: Response, next: any) => {
      try {
        const isAllowed = await this.applyRateLimit(req, res);
        if (isAllowed) {
          next();
        }
        // If not allowed, response is already sent
      } catch (error) {
        this.logger.error('Rate limiting error:', error);
        // Continue on error to avoid breaking the application
        next();
      }
    };
  }

  /**
   * Cleanup on service destruction
   */
  onModuleDestroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.storage.clear();
    this.logger.log('Rate limiter service destroyed');
  }
}
