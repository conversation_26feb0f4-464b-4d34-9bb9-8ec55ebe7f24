import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuditLoggingService } from './audit-logging.service';

export interface ErrorReport {
  id: string;
  timestamp: Date;
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: ErrorType;
  message: string;
  stack?: string;
  context: ErrorContext;
  metadata: Record<string, any>;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  tags: string[];
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  headers?: Record<string, string>;
  body?: any;
  query?: Record<string, string>;
  params?: Record<string, string>;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByLevel: Record<string, number>;
  errorsByType: Record<string, number>;
  errorRate: number;
  avgResponseTime: number;
  recentErrors: ErrorReport[];
  topErrors: Array<{ error: string; count: number }>;
}

export enum ErrorType {
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  TOKEN_ERROR = 'TOKEN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  OAUTH_ERROR = 'OAUTH_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  SECURITY_ERROR = 'SECURITY_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

@Injectable()
export class ErrorMonitoringService {
  private readonly logger = new Logger(ErrorMonitoringService.name);
  private readonly errorStore = new Map<string, ErrorReport>();
  private readonly errorMetrics: ErrorMetrics;
  private readonly maxStoredErrors = 10000;
  private readonly cleanupInterval: NodeJS.Timeout;

  constructor(
    private readonly configService: ConfigService,
    private readonly auditLogging: AuditLoggingService,
  ) {
    this.errorMetrics = {
      totalErrors: 0,
      errorsByLevel: {},
      errorsByType: {},
      errorRate: 0,
      avgResponseTime: 0,
      recentErrors: [],
      topErrors: [],
    };

    // Start cleanup and metrics calculation interval
    this.cleanupInterval = setInterval(
      () => {
        this.cleanupOldErrors();
        this.calculateMetrics();
      },
      5 * 60 * 1000,
    ); // Every 5 minutes

    this.logger.log('Error monitoring service initialized');
  }

  /**
   * Report an error with comprehensive context
   */
  async reportError(
    error: Error,
    context: Partial<ErrorContext> = {},
    metadata: Record<string, any> = {},
    level: ErrorReport['level'] = 'MEDIUM',
  ): Promise<string> {
    const errorId = this.generateErrorId();
    const errorType = this.classifyError(error);
    const tags = this.generateTags(error, context, errorType);

    const errorReport: ErrorReport = {
      id: errorId,
      timestamp: new Date(),
      level,
      type: errorType,
      message: error.message || 'Unknown error',
      stack: error.stack,
      context: this.sanitizeContext(context),
      metadata: this.sanitizeMetadata(metadata),
      resolved: false,
      tags,
    };

    // Store error
    this.storeError(errorReport);

    // Update metrics
    this.updateMetrics(errorReport);

    // Log error based on level
    await this.logError(errorReport);

    // Send alerts for critical errors
    if (level === 'CRITICAL' || errorType === ErrorType.SECURITY_ERROR) {
      await this.sendCriticalAlert(errorReport);
    }

    // Track error patterns
    this.trackErrorPatterns(errorReport);

    this.logger.debug(`Error reported: ${errorId}`);
    return errorId;
  }

  /**
   * Report authentication specific errors
   */
  async reportAuthError(
    error: Error,
    context: Partial<ErrorContext> = {},
    authContext: {
      attemptedEmail?: string;
      provider?: string;
      tokenType?: string;
      failureReason?: string;
    } = {},
  ): Promise<string> {
    const enhancedContext = {
      ...context,
      ...authContext,
    };

    const level = this.determineAuthErrorLevel(error, authContext);

    return this.reportError(error, enhancedContext, authContext, level);
  }

  /**
   * Report OAuth specific errors
   */
  async reportOAuthError(
    error: Error,
    context: Partial<ErrorContext> = {},
    oauthContext: {
      provider?: string;
      step?: string;
      redirectUri?: string;
      state?: string;
    } = {},
  ): Promise<string> {
    const enhancedMetadata = {
      ...oauthContext,
      oauthFlow: true,
      timestamp: new Date().toISOString(),
    };

    return this.reportError(
      error,
      context,
      enhancedMetadata,
      'HIGH', // OAuth errors are generally high priority
    );
  }

  /**
   * Report rate limiting violations
   */
  async reportRateLimitError(
    context: Partial<ErrorContext> = {},
    rateLimitContext: {
      rule?: string;
      limit?: number;
      current?: number;
      resetTime?: Date;
    } = {},
  ): Promise<string> {
    const error = new Error(`Rate limit exceeded: ${rateLimitContext.rule}`);

    return this.reportError(error, context, rateLimitContext, 'MEDIUM');
  }

  /**
   * Get error by ID
   */
  getError(errorId: string): ErrorReport | undefined {
    return this.errorStore.get(errorId);
  }

  /**
   * Get error metrics
   */
  getMetrics(): ErrorMetrics {
    return { ...this.errorMetrics };
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 50): ErrorReport[] {
    return Array.from(this.errorStore.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get errors by type
   */
  getErrorsByType(type: ErrorType, limit: number = 50): ErrorReport[] {
    return Array.from(this.errorStore.values())
      .filter((error) => error.type === type)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get errors by level
   */
  getErrorsByLevel(
    level: ErrorReport['level'],
    limit: number = 50,
  ): ErrorReport[] {
    return Array.from(this.errorStore.values())
      .filter((error) => error.level === level)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Search errors
   */
  searchErrors(
    query: string,
    filters: {
      level?: ErrorReport['level'];
      type?: ErrorType;
      userId?: string;
      startDate?: Date;
      endDate?: Date;
    } = {},
    limit: number = 50,
  ): ErrorReport[] {
    let errors = Array.from(this.errorStore.values());

    // Apply filters
    if (filters.level) {
      errors = errors.filter((error) => error.level === filters.level);
    }
    if (filters.type) {
      errors = errors.filter((error) => error.type === filters.type);
    }
    if (filters.userId) {
      errors = errors.filter(
        (error) => error.context.userId === filters.userId,
      );
    }
    if (filters.startDate) {
      errors = errors.filter((error) => error.timestamp >= filters.startDate);
    }
    if (filters.endDate) {
      errors = errors.filter((error) => error.timestamp <= filters.endDate);
    }

    // Apply text search
    if (query) {
      const searchLower = query.toLowerCase();
      errors = errors.filter(
        (error) =>
          error.message.toLowerCase().includes(searchLower) ||
          error.stack?.toLowerCase().includes(searchLower) ||
          error.tags.some((tag) => tag.toLowerCase().includes(searchLower)),
      );
    }

    return errors
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string, resolvedBy: string): boolean {
    const error = this.errorStore.get(errorId);
    if (error) {
      error.resolved = true;
      error.resolvedAt = new Date();
      error.resolvedBy = resolvedBy;
      this.logger.log(`Error ${errorId} marked as resolved by ${resolvedBy}`);
      return true;
    }
    return false;
  }

  /**
   * Get error statistics for a time period
   */
  getErrorStatistics(
    startDate: Date,
    endDate: Date,
  ): {
    totalErrors: number;
    errorsByHour: Array<{ hour: string; count: number }>;
    errorsByLevel: Record<string, number>;
    errorsByType: Record<string, number>;
    topEndpoints: Array<{ endpoint: string; count: number }>;
    topUsers: Array<{ userId: string; count: number }>;
  } {
    const errors = Array.from(this.errorStore.values()).filter(
      (error) => error.timestamp >= startDate && error.timestamp <= endDate,
    );

    const errorsByHour = this.groupErrorsByHour(errors, startDate, endDate);
    const errorsByLevel = this.groupBy(errors, 'level');
    const errorsByType = this.groupBy(errors, 'type');
    const topEndpoints = this.getTopEndpoints(errors, 10);
    const topUsers = this.getTopUsers(errors, 10);

    return {
      totalErrors: errors.length,
      errorsByHour,
      errorsByLevel,
      errorsByType,
      topEndpoints,
      topUsers,
    };
  }

  /**
   * Store error in memory
   */
  private storeError(error: ErrorReport): void {
    // Remove oldest errors if we exceed the limit
    if (this.errorStore.size >= this.maxStoredErrors) {
      const oldestError = Array.from(this.errorStore.values()).sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
      )[0];

      if (oldestError) {
        this.errorStore.delete(oldestError.id);
      }
    }

    this.errorStore.set(error.id, error);
  }

  /**
   * Update metrics
   */
  private updateMetrics(error: ErrorReport): void {
    this.errorMetrics.totalErrors++;

    // Update by level
    this.errorMetrics.errorsByLevel[error.level] =
      (this.errorMetrics.errorsByLevel[error.level] || 0) + 1;

    // Update by type
    this.errorMetrics.errorsByType[error.type] =
      (this.errorMetrics.errorsByType[error.type] || 0) + 1;

    // Update recent errors (keep last 20)
    this.errorMetrics.recentErrors.unshift(error);
    if (this.errorMetrics.recentErrors.length > 20) {
      this.errorMetrics.recentErrors.pop();
    }
  }

  /**
   * Log error based on level
   */
  private async logError(error: ErrorReport): Promise<void> {
    const logContext = {
      errorId: error.id,
      type: error.type,
      level: error.level,
      context: error.context,
      metadata: error.metadata,
      tags: error.tags,
    };

    switch (error.level) {
      case 'CRITICAL':
        this.logger.error(`CRITICAL ERROR: ${error.message}`, logContext);
        break;
      case 'HIGH':
        this.logger.error(`HIGH PRIORITY ERROR: ${error.message}`, logContext);
        break;
      case 'MEDIUM':
        this.logger.warn(`ERROR: ${error.message}`, logContext);
        break;
      case 'LOW':
        this.logger.log(`Low priority error: ${error.message}`, logContext);
        break;
    }

    // Log to audit system for security-related errors
    if (error.type === ErrorType.SECURITY_ERROR || error.level === 'CRITICAL') {
      await this.auditLogging.logSecurityEvent({
        type: 'SUSPICIOUS_ACTIVITY',
        userId: error.context.userId,
        ipAddress: error.context.ipAddress || 'unknown',
        userAgent: error.context.userAgent,
        details: {
          errorId: error.id,
          errorType: error.type,
          message: error.message,
          endpoint: error.context.endpoint,
        },
        risk: error.level === 'CRITICAL' ? 'CRITICAL' : 'HIGH',
        timestamp: error.timestamp,
      });
    }
  }

  /**
   * Send critical alert
   */
  private async sendCriticalAlert(error: ErrorReport): Promise<void> {
    // TODO: Implement actual alerting (email, Slack, SMS, etc.)
    this.logger.error('CRITICAL ALERT', {
      errorId: error.id,
      message: error.message,
      context: error.context,
      timestamp: error.timestamp,
    });

    // Log critical alert
    console.error('🚨 CRITICAL ERROR ALERT 🚨', {
      id: error.id,
      type: error.type,
      message: error.message,
      userId: error.context.userId,
      endpoint: error.context.endpoint,
      timestamp: error.timestamp.toISOString(),
    });
  }

  /**
   * Track error patterns for anomaly detection
   */
  private trackErrorPatterns(error: ErrorReport): void {
    // TODO: Implement pattern detection and anomaly analysis
    // This could include:
    // - Detecting error spikes
    // - Identifying recurring error patterns
    // - Correlating errors with user behavior
    // - Machine learning for anomaly detection

    // For now, just log if we see too many errors from the same source
    const recentSimilarErrors = this.errorMetrics.recentErrors.filter(
      (recentError) =>
        recentError.type === error.type &&
        recentError.context.userId === error.context.userId &&
        Date.now() - recentError.timestamp.getTime() < 5 * 60 * 1000, // Last 5 minutes
    );

    if (recentSimilarErrors.length > 5) {
      this.logger.warn('Potential error pattern detected', {
        errorType: error.type,
        userId: error.context.userId,
        count: recentSimilarErrors.length,
        timeWindow: '5 minutes',
      });
    }
  }

  /**
   * Classify error type based on error content
   */
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (
      message.includes('unauthorized') ||
      message.includes('authentication')
    ) {
      return ErrorType.AUTHENTICATION_ERROR;
    }
    if (message.includes('forbidden') || message.includes('authorization')) {
      return ErrorType.AUTHORIZATION_ERROR;
    }
    if (message.includes('token') || message.includes('jwt')) {
      return ErrorType.TOKEN_ERROR;
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.VALIDATION_ERROR;
    }
    if (message.includes('oauth') || message.includes('google')) {
      return ErrorType.OAUTH_ERROR;
    }
    if (
      message.includes('rate limit') ||
      message.includes('too many requests')
    ) {
      return ErrorType.RATE_LIMIT_ERROR;
    }
    if (message.includes('database') || stack.includes('prisma')) {
      return ErrorType.DATABASE_ERROR;
    }
    if (message.includes('network') || message.includes('timeout')) {
      return ErrorType.NETWORK_ERROR;
    }
    if (message.includes('config') || message.includes('environment')) {
      return ErrorType.CONFIGURATION_ERROR;
    }
    if (message.includes('security') || message.includes('attack')) {
      return ErrorType.SECURITY_ERROR;
    }

    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * Generate tags for error categorization
   */
  private generateTags(
    error: Error,
    context: Partial<ErrorContext>,
    errorType: ErrorType,
  ): string[] {
    const tags: string[] = [];

    // Add error type tag
    tags.push(errorType);

    // Add endpoint tag
    if (context.endpoint) {
      tags.push(`endpoint:${context.endpoint}`);
    }

    // Add method tag
    if (context.method) {
      tags.push(`method:${context.method}`);
    }

    // Add status code tag
    if (context.statusCode) {
      tags.push(`status:${context.statusCode}`);
    }

    // Add user tag
    if (context.userId) {
      tags.push(`user:${context.userId}`);
    }

    // Add environment tag
    const environment = this.configService.get<string>(
      'NODE_ENV',
      'development',
    );
    tags.push(`env:${environment}`);

    return tags;
  }

  /**
   * Determine auth error level
   */
  private determineAuthErrorLevel(
    error: Error,
    authContext: any,
  ): ErrorReport['level'] {
    if (authContext.failureReason === 'account_locked') {
      return 'HIGH';
    }
    if (authContext.failureReason === 'suspicious_activity') {
      return 'CRITICAL';
    }
    if (
      error.message.includes('brute force') ||
      error.message.includes('attack')
    ) {
      return 'CRITICAL';
    }
    return 'MEDIUM';
  }

  /**
   * Sanitize context to remove sensitive information
   */
  private sanitizeContext(context: Partial<ErrorContext>): ErrorContext {
    const sanitized = { ...context };

    // Remove sensitive headers
    if (sanitized.headers) {
      const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
      for (const header of sensitiveHeaders) {
        if (sanitized.headers[header]) {
          sanitized.headers[header] = '***REDACTED***';
        }
      }
    }

    // Remove sensitive body fields
    if (sanitized.body && typeof sanitized.body === 'object') {
      const sensitiveFields = ['password', 'token', 'secret', 'key'];
      for (const field of sensitiveFields) {
        if (sanitized.body[field]) {
          sanitized.body[field] = '***REDACTED***';
        }
      }
    }

    return sanitized as ErrorContext;
  }

  /**
   * Sanitize metadata
   */
  private sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
    const sanitized = { ...metadata };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credentials'];

    for (const key of Object.keys(sanitized)) {
      if (
        sensitiveKeys.some((sensitiveKey) =>
          key.toLowerCase().includes(sensitiveKey),
        )
      ) {
        sanitized[key] = '***REDACTED***';
      }
    }

    return sanitized;
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Group errors by a field
   */
  private groupBy(
    errors: ErrorReport[],
    field: string,
  ): Record<string, number> {
    return errors.reduce(
      (acc, error) => {
        const key = this.getNestedValue(error, field) || 'unknown';
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );
  }

  /**
   * Get nested object value by path
   */
  private getNestedValue(obj: any, path: string): any {
    return path
      .split('.')
      .reduce((current, key) => current && current[key], obj);
  }

  /**
   * Get top endpoints by frequency
   */
  private getTopEndpoints(
    errors: ErrorReport[],
    limit: number,
  ): Array<{ endpoint: string; count: number }> {
    const grouped = this.groupBy(errors, 'context.endpoint');
    return Object.entries(grouped)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([endpoint, count]) => ({
        endpoint: endpoint || 'unknown',
        count,
      }));
  }

  /**
   * Get top users by frequency
   */
  private getTopUsers(
    errors: ErrorReport[],
    limit: number,
  ): Array<{ userId: string; count: number }> {
    const grouped = this.groupBy(errors, 'context.userId');
    return Object.entries(grouped)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([userId, count]) => ({
        userId: userId || 'anonymous',
        count,
      }));
  }

  /**
   * Group errors by hour
   */
  private groupErrorsByHour(
    errors: ErrorReport[],
    startDate: Date,
    endDate: Date,
  ): Array<{ hour: string; count: number }> {
    const hours: Record<string, number> = {};

    // Initialize all hours in the range
    const current = new Date(startDate);
    while (current <= endDate) {
      const hourKey = current.toISOString().substr(0, 13) + ':00:00';
      hours[hourKey] = 0;
      current.setHours(current.getHours() + 1);
    }

    // Count errors by hour
    for (const error of errors) {
      const hourKey = error.timestamp.toISOString().substr(0, 13) + ':00:00';
      if (hours.hasOwnProperty(hourKey)) {
        hours[hourKey]++;
      }
    }

    return Object.entries(hours).map(([hour, count]) => ({ hour, count }));
  }

  /**
   * Calculate comprehensive metrics
   */
  private calculateMetrics(): void {
    const recentErrors = this.getRecentErrors(100);
    const last24Hours = Date.now() - 24 * 60 * 60 * 1000;
    const recent24HourErrors = recentErrors.filter(
      (error) => error.timestamp.getTime() > last24Hours,
    );

    // Calculate error rate (errors per hour)
    this.errorMetrics.errorRate = recent24HourErrors.length / 24;

    // Calculate top errors
    const errorMessages = recent24HourErrors.reduce(
      (acc, error) => {
        acc[error.message] = (acc[error.message] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    this.errorMetrics.topErrors = Object.entries(errorMessages)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([error, count]) => ({ error, count }));
  }

  /**
   * Clean up old errors
   */
  private cleanupOldErrors(): void {
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    const cutoffTime = Date.now() - maxAge;
    let cleanedCount = 0;

    for (const [id, error] of this.errorStore.entries()) {
      if (error.timestamp.getTime() < cutoffTime) {
        this.errorStore.delete(id);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} old errors`);
    }
  }

  /**
   * Cleanup on service destruction
   */
  onModuleDestroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.logger.log('Error monitoring service destroyed');
  }
}
