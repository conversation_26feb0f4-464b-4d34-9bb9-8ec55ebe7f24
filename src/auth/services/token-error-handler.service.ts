import { Injectable, Logger } from '@nestjs/common';
import { TokenManagementService } from './token-management.service';

@Injectable()
export class TokenErrorHandlerService {
  private readonly logger = new Logger(TokenErrorHandlerService.name);

  constructor(
    private readonly tokenManagementService: TokenManagementService,
  ) {}

  /**
   * Handle expired token errors
   */
  async handleExpiredToken(
    _error: any,
    request: any,
    response: any,
  ): Promise<void> {
    const refreshToken = request.headers['x-refresh-token'];

    if (refreshToken) {
      try {
        // Attempt automatic token refresh
        const newTokenPair =
          await this.tokenManagementService.refreshAccessToken(refreshToken);

        // Return new tokens in response headers
        response.setHeader('X-New-Access-Token', newTokenPair.accessToken);
        response.setHeader('X-New-Refresh-Token', newTokenPair.refreshToken);
        response.setHeader('X-Token-Refreshed', 'true');

        return;
      } catch (refreshError) {
        this.logger.error(
          'Token refresh failed during error handling:',
          refreshError,
        );
      }
    }

    // Standard error response
    response.status(401).json({
      statusCode: 401,
      message: 'Token expired',
      error: 'Unauthorized',
      code: 'TOKEN_EXPIRED',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Handle invalid token errors
   */
  handleInvalidToken(_error: any, response: any): void {
    response.status(401).json({
      statusCode: 401,
      message: 'Invalid token',
      error: 'Unauthorized',
      code: 'TOKEN_INVALID',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Handle blacklisted token errors
   */
  handleBlacklistedToken(response: any): void {
    response.status(401).json({
      statusCode: 401,
      message: 'Token has been revoked',
      error: 'Unauthorized',
      code: 'TOKEN_REVOKED',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Handle refresh token errors with detailed error codes
   */
  handleRefreshTokenError(error: any, response: any): void {
    let code = 'REFRESH_TOKEN_ERROR';
    let message = 'Refresh token error';

    if (error.message.includes('expired')) {
      code = 'REFRESH_TOKEN_EXPIRED';
      message = 'Refresh token has expired';
    } else if (error.message.includes('revoked')) {
      code = 'REFRESH_TOKEN_REVOKED';
      message = 'Refresh token has been revoked';
    } else if (error.message.includes('invalid')) {
      code = 'REFRESH_TOKEN_INVALID';
      message = 'Invalid refresh token';
    }

    response.status(401).json({
      statusCode: 401,
      message,
      error: 'Unauthorized',
      code,
      timestamp: new Date().toISOString(),
      action: 'REDIRECT_TO_LOGIN',
    });
  }
}
