import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import {
  OAuthSuccessData,
  OAuthErrorData,
  OAuthAccountLinkingData,
  OAuthRedirectOptions,
  HTMLTemplateData,
  SecurityValidationResult,
  OAuthRedirectType,
} from '../dto/oauth-redirect.dto';
import { OAuthRedirectManagerService } from './oauth-redirect-manager.service';

@Injectable()
export class OAuthHtmlTemplateService {
  private readonly defaultOptions: OAuthRedirectOptions = {
    autoRedirect: true,
    redirectDelay: 3000,
    showManualRedirect: true,
    isPopup: false,
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly redirectManager: OAuthRedirectManagerService,
  ) {}

  /**
   * Generate HTML page for successful OAuth authentication
   */
  generateSuccessPage(
    successData: OAuthSuccessData,
    options?: OAuthRedirectOptions,
  ): string {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const frontendUrl = this.getFrontendUrl();
    const templateData: HTMLTemplateData = {
      title: 'Authentication Successful',
      data: successData,
      options: mergedOptions,
      frontendUrl,
    };

    return this.generateHtmlTemplate(OAuthRedirectType.SUCCESS, templateData);
  }

  /**
   * Generate HTML page for OAuth authentication errors
   */
  generateErrorPage(
    errorData: OAuthErrorData,
    options?: OAuthRedirectOptions,
  ): string {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const frontendUrl = this.getFrontendUrl();
    const templateData: HTMLTemplateData = {
      title: 'Authentication Error',
      data: errorData,
      options: mergedOptions,
      frontendUrl,
    };

    return this.generateHtmlTemplate(OAuthRedirectType.ERROR, templateData);
  }

  /**
   * Generate HTML page for account linking scenarios
   */
  generateAccountLinkingPage(
    linkingData: OAuthAccountLinkingData,
    options?: OAuthRedirectOptions,
  ): string {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const frontendUrl = this.getFrontendUrl();
    const templateData: HTMLTemplateData = {
      title: 'Account Linked Successfully',
      data: linkingData,
      options: mergedOptions,
      frontendUrl,
    };

    return this.generateHtmlTemplate(
      OAuthRedirectType.ACCOUNT_LINKING,
      templateData,
    );
  }

  /**
   * Validate frontend URL for security
   */
  validateFrontendUrl(url: string): SecurityValidationResult {
    try {
      const parsedUrl = new URL(url);
      const allowedOrigins = this.getAllowedOrigins();

      // Check if the origin is in the allowed list
      const isAllowed = allowedOrigins.some((origin) => {
        const allowedUrl = new URL(origin);
        return allowedUrl.origin === parsedUrl.origin;
      });

      if (!isAllowed) {
        return {
          isValid: false,
          error: `URL origin ${parsedUrl.origin} is not in the allowed origins list`,
        };
      }

      // Additional security checks
      if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
        return {
          isValid: false,
          error: 'Only HTTP and HTTPS protocols are allowed',
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Invalid URL format: ${error.message}`,
      };
    }
  }

  /**
   * Get frontend URL from environment variables
   */
  private getFrontendUrl(): string {
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    if (!frontendUrl) {
      throw new Error('FRONTEND_URL environment variable is not configured');
    }

    const validation = this.validateFrontendUrl(frontendUrl);
    if (!validation.isValid) {
      throw new Error(`Invalid FRONTEND_URL: ${validation.error}`);
    }

    return frontendUrl;
  }

  /**
   * Get allowed origins for security validation
   */
  private getAllowedOrigins(): string[] {
    const frontendUrl = this.configService.get<string>('FRONTEND_URL');
    const additionalOrigins = this.configService.get<string>(
      'ALLOWED_ORIGINS',
      '',
    );

    const origins = [frontendUrl];

    if (additionalOrigins) {
      origins.push(
        ...additionalOrigins.split(',').map((origin) => origin.trim()),
      );
    }

    return origins.filter(Boolean);
  }

  /**
   * Generate HTML template based on type and data
   */
  private generateHtmlTemplate(
    type: OAuthRedirectType,
    templateData: HTMLTemplateData,
  ): string {
    const { title, data, options, frontendUrl } = templateData;

    // For success type, use the enhanced template with token accessibility
    if (type === OAuthRedirectType.SUCCESS) {
      return this.generateEnhancedSuccessTemplate(
        data as OAuthSuccessData,
        options,
        frontendUrl,
      );
    }

    // For other types, use the original inline template
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline';">
    <title>${this.escapeHtml(title)}</title>
    <style>
        ${this.getEmbeddedCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            ${this.generateContentHtml(type, data)}
        </div>
        <div class="actions">
            ${this.generateActionsHtml(type, data, options)}
        </div>
    </div>

    <script>
        ${this.redirectManager.generateRedirectScript(type, data, options, frontendUrl)}
    </script>
</body>
</html>`;
  }

  /**
   * Generate content HTML based on OAuth type
   */
  private generateContentHtml(
    type: OAuthRedirectType,
    data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData,
  ): string {
    switch (type) {
      case OAuthRedirectType.SUCCESS:
        const successData = data as OAuthSuccessData;
        return `
          <div class="success-icon">✓</div>
          <h1>Authentication Successful!</h1>
          <p>${this.escapeHtml(successData.message)}</p>
          ${successData.isNewUser ? '<p class="welcome">Welcome to Orbitum!</p>' : ''}
          <div class="loading-spinner"></div>
        `;
      case OAuthRedirectType.ERROR:
        const errorData = data as OAuthErrorData;
        return `
          <div class="error-icon">✗</div>
          <h1>Authentication Error</h1>
          <p>${this.escapeHtml(errorData.message)}</p>
          ${errorData.details ? `<p class="error-details">${this.escapeHtml(errorData.details)}</p>` : ''}
        `;
      case OAuthRedirectType.ACCOUNT_LINKING:
        const linkingData = data as OAuthAccountLinkingData;
        return `
          <div class="link-icon">🔗</div>
          <h1>Account Linked Successfully!</h1>
          <p>${this.escapeHtml(linkingData.message)}</p>
          <div class="loading-spinner"></div>
        `;
      default:
        return '<h1>Processing...</h1>';
    }
  }

  /**
   * Generate actions HTML based on OAuth type
   */
  private generateActionsHtml(
    type: OAuthRedirectType,
    data: OAuthSuccessData | OAuthErrorData | OAuthAccountLinkingData,
    options: OAuthRedirectOptions,
  ): string {
    if (type === OAuthRedirectType.ERROR) {
      const errorData = data as OAuthErrorData;
      return `
        ${
          errorData.canRetry && errorData.retryUrl
            ? `<button onclick="window.location.href='${this.escapeHtml(errorData.retryUrl)}'" class="btn btn-primary">Try Again</button>`
            : ''
        }
        <button onclick="redirectToFrontend('${this.escapeHtml(errorData.redirectUrl)}')" class="btn btn-secondary">Continue</button>
      `;
    }

    if (options.showManualRedirect) {
      const redirectUrl =
        type === OAuthRedirectType.SUCCESS
          ? (data as OAuthSuccessData).redirectUrl
          : (data as OAuthAccountLinkingData).redirectUrl;

      return `<button onclick="redirectToFrontend('${this.escapeHtml(redirectUrl)}')" class="btn btn-primary">Continue to App</button>`;
    }

    return '';
  }

  /**
   * Generate enhanced success template with token accessibility
   */
  private generateEnhancedSuccessTemplate(
    data: OAuthSuccessData,
    options: OAuthRedirectOptions,
    frontendUrl: string,
  ): string {
    try {
      // Read the enhanced template file
      const templatePath = path.join(
        __dirname,
        '../templates/enhanced-oauth-success.html',
      );
      let template = fs.readFileSync(templatePath, 'utf8');

      // Replace placeholders with actual data
      template = template
        .replace(/\{\{FRONTEND_URL\}\}/g, this.escapeHtml(frontendUrl))
        .replace(/\{\{REDIRECT_URL\}\}/g, this.escapeHtml(data.redirectUrl))
        .replace(/\{\{IS_POPUP\}\}/g, options.isPopup ? 'true' : 'false')
        .replace(
          /\{\{AUTO_REDIRECT\}\}/g,
          options.autoRedirect ? 'true' : 'false',
        )
        .replace(
          /\{\{REDIRECT_DELAY\}\}/g,
          (options.redirectDelay || 3000).toString(),
        )
        .replace(
          /\{\{ACCESS_TOKEN\}\}/g,
          this.escapeHtml(data.accessToken || ''),
        )
        .replace(
          /\{\{REFRESH_TOKEN\}\}/g,
          this.escapeHtml(data.refreshToken || ''),
        )
        .replace(
          /\{\{ACCESS_TOKEN_EXPIRES_IN\}\}/g,
          (data.accessTokenExpiresIn || 0).toString(),
        )
        .replace(
          /\{\{REFRESH_TOKEN_EXPIRES_IN\}\}/g,
          (data.refreshTokenExpiresIn || 0).toString(),
        )
        .replace(
          /\{\{TOKEN_TYPE\}\}/g,
          this.escapeHtml(data.tokenType || 'Bearer'),
        )
        .replace(/\{\{USER_ID\}\}/g, this.escapeHtml(data.user?.id || ''))
        .replace(/\{\{USER_EMAIL\}\}/g, this.escapeHtml(data.user?.email || ''))
        .replace(
          /\{\{USER_NAME\}\}/g,
          this.escapeHtml(
            `${data.user?.firstName || ''} ${data.user?.lastName || ''}`.trim(),
          ),
        )
        .replace(/\{\{IS_NEW_USER\}\}/g, data.isNewUser ? 'true' : 'false')
        .replace(/\{\{PROVIDER\}\}/g, this.escapeHtml('google'));

      return template;
    } catch (error) {
      console.error('Error loading enhanced template:', error);
      // Fallback to inline template
      return this.generateFallbackSuccessTemplate(data, options, frontendUrl);
    }
  }

  /**
   * Fallback success template if enhanced template fails to load
   */
  private generateFallbackSuccessTemplate(
    data: OAuthSuccessData,
    options: OAuthRedirectOptions,
    frontendUrl: string,
  ): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Successful</title>
    <style>${this.getEmbeddedCSS()}</style>
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="success-icon">✓</div>
            <h1>Authentication Successful!</h1>
            <p>${this.escapeHtml(data.message)}</p>
            ${data.isNewUser ? '<p class="welcome">Welcome to Orbitum!</p>' : ''}
            <div class="loading-spinner"></div>
        </div>
        <div class="actions">
            <button onclick="redirectToApp()" class="btn btn-primary">Continue to App</button>
        </div>
    </div>
    <script>
        // Enhanced token accessibility
        window.oauthTokens = {
            accessToken: '${this.escapeForJs(data.accessToken)}',
            refreshToken: '${this.escapeForJs(data.refreshToken)}',
            user: ${JSON.stringify(data.user)},
            expiresAt: '${new Date(Date.now() + data.accessTokenExpiresIn * 1000).toISOString()}'
        };

        function redirectToApp() {
            window.location.href = '${this.escapeForJs(data.redirectUrl)}';
        }

        ${options.autoRedirect ? `setTimeout(redirectToApp, ${options.redirectDelay || 3000});` : ''}
    </script>
</body>
</html>`;
  }

  /**
   * Get embedded CSS styles
   */
  private getEmbeddedCSS(): string {
    return `
      * { margin: 0; padding: 0; box-sizing: border-box; }
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
      .container { background: white; border-radius: 12px; padding: 2rem; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
      .success-icon, .error-icon, .link-icon { font-size: 3rem; margin-bottom: 1rem; }
      .success-icon { color: #10b981; }
      .error-icon { color: #ef4444; }
      .link-icon { color: #3b82f6; }
      h1 { color: #1f2937; margin-bottom: 1rem; font-size: 1.5rem; }
      p { color: #6b7280; margin-bottom: 1rem; line-height: 1.5; }
      .welcome { color: #10b981; font-weight: 600; }
      .error-details { font-size: 0.875rem; color: #9ca3af; }
      .loading-spinner { width: 24px; height: 24px; border: 2px solid #e5e7eb; border-top: 2px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 1rem auto; }
      @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
      .actions { margin-top: 1.5rem; }
      .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; margin: 0 0.5rem; transition: all 0.2s; }
      .btn-primary { background: #3b82f6; color: white; }
      .btn-primary:hover { background: #2563eb; }
      .btn-secondary { background: #e5e7eb; color: #374151; }
      .btn-secondary:hover { background: #d1d5db; }
      @media (max-width: 480px) { .container { padding: 1.5rem; } h1 { font-size: 1.25rem; } }
    `;
  }

  /**
   * Escape HTML to prevent XSS attacks
   */
  private escapeHtml(text: string): string {
    if (!text) return '';
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Escape JavaScript special characters
   */
  private escapeForJs(unsafe: string): string {
    if (!unsafe) return '';
    return unsafe
      .replace(/\\/g, '\\\\')
      .replace(/'/g, "\\'")
      .replace(/"/g, '\\"')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t');
  }

  /**
   * Escape data for safe embedding in HTML/JavaScript
   */
  private escapeHtmlData(data: any): any {
    if (typeof data === 'string') {
      return this.escapeHtml(data);
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.escapeHtmlData(item));
    }

    if (data && typeof data === 'object') {
      const escaped: any = {};
      for (const [key, value] of Object.entries(data)) {
        escaped[key] = this.escapeHtmlData(value);
      }
      return escaped;
    }

    return data;
  }
}
