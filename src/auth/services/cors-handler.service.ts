import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response, Request } from 'express';

export interface CorsOptions {
  origins: string[];
  methods: string[];
  allowedHeaders: string[];
  exposedHeaders: string[];
  credentials: boolean;
  maxAge: number;
  preflightContinue: boolean;
  optionsSuccessStatus: number;
}

export interface CorsValidationResult {
  isAllowed: boolean;
  origin?: string;
  error?: string;
}

@Injectable()
export class CorsHandlerService {
  private readonly logger = new Logger(CorsHandlerService.name);
  private readonly corsOptions: CorsOptions;

  constructor(private readonly configService: ConfigService) {
    this.corsOptions = this.initializeCorsOptions();
  }

  /**
   * Initialize CORS options from environment variables
   */
  private initializeCorsOptions(): CorsOptions {
    const frontendUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
    const allowedOrigins = this.configService.get<string>(
      'ALLOWED_ORIGINS',
      '',
    );
    const additionalOrigins = allowedOrigins
      ? allowedOrigins.split(',').map((o) => o.trim())
      : [];

    const origins = [frontendUrl, ...additionalOrigins].filter(Boolean);

    // Add common development origins if in development
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    if (nodeEnv === 'development') {
      origins.push(
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:8080',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:8080',
      );
    }

    this.logger.log(`CORS origins configured: ${origins.join(', ')}`);

    return {
      origins: [...new Set(origins)], // Remove duplicates
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Access-Token',
        'X-Refresh-Token',
        'X-Device-Id',
        'X-OAuth-API-Request',
        'X-OAuth-Popup',
        'Cache-Control',
        'Pragma',
      ],
      exposedHeaders: [
        'X-Total-Count',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset',
        'X-Request-ID',
      ],
      credentials: true,
      maxAge: 86400, // 24 hours
      preflightContinue: false,
      optionsSuccessStatus: 204,
    };
  }

  /**
   * Validate if origin is allowed
   */
  validateOrigin(origin: string): CorsValidationResult {
    if (!origin) {
      return { isAllowed: false, error: 'No origin header provided' };
    }

    try {
      const originUrl = new URL(origin);

      // Check exact match
      if (this.corsOptions.origins.includes(origin)) {
        return { isAllowed: true, origin };
      }

      // Check origin without trailing slash
      const normalizedOrigin = origin.replace(/\/$/, '');
      if (this.corsOptions.origins.includes(normalizedOrigin)) {
        return { isAllowed: true, origin: normalizedOrigin };
      }

      // Check if any allowed origin matches the protocol and host
      for (const allowedOrigin of this.corsOptions.origins) {
        try {
          const allowedUrl = new URL(allowedOrigin);
          if (
            originUrl.protocol === allowedUrl.protocol &&
            originUrl.hostname === allowedUrl.hostname &&
            originUrl.port === allowedUrl.port
          ) {
            return { isAllowed: true, origin: allowedOrigin };
          }
        } catch {
          // Skip invalid allowed origin
          continue;
        }
      }

      return {
        isAllowed: false,
        error: `Origin ${origin} is not allowed. Allowed origins: ${this.corsOptions.origins.join(', ')}`,
      };
    } catch (error) {
      return {
        isAllowed: false,
        error: `Invalid origin format: ${error.message}`,
      };
    }
  }

  /**
   * Apply CORS headers to response
   */
  applyCorsHeaders(req: Request, res: Response): boolean {
    const method = req.method.toUpperCase();

    // Handle preflight requests
    if (method === 'OPTIONS') {
      return this.handlePreflightRequest(req, res);
    }

    // Handle simple requests
    return this.handleSimpleRequest(req, res);
  }

  /**
   * Handle preflight CORS requests
   */
  private handlePreflightRequest(req: Request, res: Response): boolean {
    const origin = req.headers.origin;
    const requestMethod = req.headers['access-control-request-method'];

    if (!origin) {
      this.logger.warn('Preflight request without origin header');
      res.status(403).json({ error: 'Origin header is required' });
      return false;
    }

    const originValidation = this.validateOrigin(origin);
    if (!originValidation.isAllowed) {
      this.logger.warn(`Preflight request rejected: ${originValidation.error}`);
      res.status(403).json({ error: 'Origin not allowed' });
      return false;
    }

    // Check if requested method is allowed
    if (
      requestMethod &&
      !this.corsOptions.methods.includes(requestMethod.toUpperCase())
    ) {
      this.logger.warn(
        `Preflight request rejected: Method ${requestMethod} not allowed`,
      );
      res.status(403).json({ error: 'Method not allowed' });
      return false;
    }

    // Set CORS headers for preflight
    res.header('Access-Control-Allow-Origin', originValidation.origin);
    res.header(
      'Access-Control-Allow-Methods',
      this.corsOptions.methods.join(', '),
    );
    res.header(
      'Access-Control-Allow-Headers',
      this.corsOptions.allowedHeaders.join(', '),
    );
    res.header('Access-Control-Max-Age', this.corsOptions.maxAge.toString());

    if (this.corsOptions.credentials) {
      res.header('Access-Control-Allow-Credentials', 'true');
    }

    res.status(this.corsOptions.optionsSuccessStatus).end();
    return true;
  }

  /**
   * Handle simple CORS requests
   */
  private handleSimpleRequest(req: Request, res: Response): boolean {
    const origin = req.headers.origin;

    if (!origin) {
      // Allow requests without origin (same-origin, Postman, curl, etc.)
      return true;
    }

    const originValidation = this.validateOrigin(origin);
    if (!originValidation.isAllowed) {
      this.logger.warn(`CORS request rejected: ${originValidation.error}`);
      res.status(403).json({ error: 'Origin not allowed' });
      return false;
    }

    // Set CORS headers for simple requests
    res.header('Access-Control-Allow-Origin', originValidation.origin);
    res.header(
      'Access-Control-Expose-Headers',
      this.corsOptions.exposedHeaders.join(', '),
    );

    if (this.corsOptions.credentials) {
      res.header('Access-Control-Allow-Credentials', 'true');
    }

    return true;
  }

  /**
   * Get CORS options for manual configuration
   */
  getCorsOptions(): CorsOptions {
    return { ...this.corsOptions };
  }

  /**
   * Check if origin is allowed (utility method)
   */
  isOriginAllowed(origin: string): boolean {
    return this.validateOrigin(origin).isAllowed;
  }

  /**
   * Add runtime origin to allowed list (for dynamic configurations)
   */
  addAllowedOrigin(origin: string): void {
    if (!this.corsOptions.origins.includes(origin)) {
      this.corsOptions.origins.push(origin);
      this.logger.log(`Added new allowed origin: ${origin}`);
    }
  }

  /**
   * Remove origin from allowed list
   */
  removeAllowedOrigin(origin: string): void {
    const index = this.corsOptions.origins.indexOf(origin);
    if (index > -1) {
      this.corsOptions.origins.splice(index, 1);
      this.logger.log(`Removed allowed origin: ${origin}`);
    }
  }

  /**
   * Generate Express CORS middleware configuration
   */
  getExpressCorsConfig(): any {
    return {
      origin: (
        origin: string,
        callback: (err: Error | null, allow?: boolean) => void,
      ) => {
        // Allow requests with no origin (mobile apps, postman, etc.)
        if (!origin) return callback(null, true);

        const validation = this.validateOrigin(origin);
        if (validation.isAllowed) {
          callback(null, true);
        } else {
          this.logger.warn(`CORS blocked origin: ${origin}`);
          callback(new Error('Not allowed by CORS'), false);
        }
      },
      methods: this.corsOptions.methods,
      allowedHeaders: this.corsOptions.allowedHeaders,
      exposedHeaders: this.corsOptions.exposedHeaders,
      credentials: this.corsOptions.credentials,
      maxAge: this.corsOptions.maxAge,
      preflightContinue: this.corsOptions.preflightContinue,
      optionsSuccessStatus: this.corsOptions.optionsSuccessStatus,
    };
  }

  /**
   * Log CORS configuration for debugging
   */
  logConfiguration(): void {
    this.logger.log('CORS Configuration:');
    this.logger.log(`- Origins: ${this.corsOptions.origins.join(', ')}`);
    this.logger.log(`- Methods: ${this.corsOptions.methods.join(', ')}`);
    this.logger.log(`- Credentials: ${this.corsOptions.credentials}`);
    this.logger.log(`- Max Age: ${this.corsOptions.maxAge}s`);
  }
}
