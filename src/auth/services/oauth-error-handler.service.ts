import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { OAuthErrorType, OAuthErrorData } from '../dto/oauth-redirect.dto';
import { OAuthHtmlTemplateService } from './oauth-html-template.service';
import { OAuthSecurityService } from './oauth-security.service';
import { FrontendValidationService } from './frontend-validation.service';

export interface ErrorContext {
  userId?: string;
  userEmail?: string;
  oauthProvider?: string;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
  timestamp: string;
}

export interface FallbackRedirectOptions {
  enableFallback: boolean;
  fallbackUrl?: string;
  maxRetries: number;
  retryDelay: number;
}

@Injectable()
export class OAuthErrorHandlerService {
  private readonly logger = new Logger(OAuthErrorHandlerService.name);
  private readonly errorCounts = new Map<string, number>();
  private readonly maxErrorsPerMinute = 10;

  constructor(
    private readonly configService: ConfigService,
    private readonly oauthHtmlTemplateService: OAuthHtmlTemplateService,
    private readonly oauthSecurityService: OAuthSecurityService,
    private readonly frontendValidationService: FrontendValidationService,
  ) {}

  /**
   * Handle OAuth errors with comprehensive error boundaries
   */
  async handleOAuthError(
    res: Response,
    errorType: OAuthErrorType,
    message: string,
    details?: string,
    context?: ErrorContext,
  ): Promise<void> {
    try {
      // Log error with context
      this.logErrorWithContext(errorType, message, details, context);

      // Check for error rate limiting
      if (this.isErrorRateLimited(context?.ipAddress)) {
        return this.handleRateLimitedError(res);
      }

      // Generate error response
      const errorData = await this.createErrorData(
        errorType,
        message,
        details,
        context,
      );

      // Validate frontend URL before redirecting
      const frontendValidation =
        await this.frontendValidationService.validateFrontendPage(
          errorData.redirectUrl,
          {
            timeout: 3000,
            retries: 1,
            checkAccessibility: true,
            useFallback: true,
          },
        );
      if (!frontendValidation.isValid || !frontendValidation.accessible) {
        return this.handleFallbackError(
          res,
          errorType,
          message,
          frontendValidation.error,
        );
      }

      // Generate HTML response
      const htmlResponse =
        this.oauthHtmlTemplateService.generateErrorPage(errorData);

      // Apply security headers
      this.oauthSecurityService.applySecurityHeaders(res);

      // Set appropriate status code
      const statusCode = this.getStatusCodeForErrorType(errorType);

      res.status(statusCode).send(htmlResponse);
    } catch (error) {
      this.logger.error('Critical error in OAuth error handler:', error);
      this.handleCriticalError(res, error);
    }
  }

  /**
   * Handle critical errors that occur within the error handler itself
   */
  private handleCriticalError(res: Response, error: any): void {
    try {
      const fallbackHtml = this.generateFallbackErrorPage();
      res.status(500).send(fallbackHtml);
    } catch (fallbackError) {
      this.logger.error(
        'Failed to generate fallback error page:',
        fallbackError,
      );
      // Last resort - send plain text response
      res
        .status(500)
        .send(
          'Authentication error occurred. Please try again or contact support.',
        );
    }
  }

  /**
   * Generate a minimal fallback error page
   */
  private generateFallbackErrorPage(): string {
    const frontendUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Authentication Error</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 2rem; background: #f5f5f5; }
          .container { max-width: 400px; margin: 0 auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .error-icon { font-size: 3rem; color: #ef4444; margin-bottom: 1rem; }
          h1 { color: #1f2937; margin-bottom: 1rem; }
          p { color: #6b7280; margin-bottom: 1.5rem; }
          .btn { background: #3b82f6; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 6px; text-decoration: none; display: inline-block; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="error-icon">⚠️</div>
          <h1>Authentication Error</h1>
          <p>We encountered an error during authentication. Please try again.</p>
          <a href="${frontendUrl}" class="btn">Return to Home</a>
        </div>
        <script>
          // Auto-redirect after 5 seconds
          setTimeout(() => {
            window.location.href = '${frontendUrl}';
          }, 5000);
        </script>
      </body>
      </html>
    `;
  }

  /**
   * Create error data with proper validation and fallbacks
   */
  private async createErrorData(
    errorType: OAuthErrorType,
    message: string,
    details?: string,
    context?: ErrorContext,
  ): Promise<OAuthErrorData> {
    const frontendUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
    const baseUrl = this.getBaseUrl();

    return new OAuthErrorData({
      errorType,
      message,
      details,
      redirectUrl: `${frontendUrl}/auth/error`,
      canRetry: this.canRetryError(errorType),
      retryUrl: this.canRetryError(errorType)
        ? `${baseUrl}/api/v1/auth/google`
        : undefined,
      timestamp: context?.timestamp || new Date().toISOString(),
    });
  }

  /**
   * Handle fallback error when primary error handling fails
   */
  private handleFallbackError(
    res: Response,
    errorType: OAuthErrorType,
    message: string,
    validationError?: string,
  ): void {
    this.logger.error(
      'Frontend URL validation failed, using fallback:',
      validationError,
    );

    const fallbackUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
    const fallbackHtml = this.generateFallbackErrorPage();

    res.status(500).send(fallbackHtml);
  }

  /**
   * Check if error rate is limited for IP address
   */
  private isErrorRateLimited(ipAddress?: string): boolean {
    if (!ipAddress) return false;

    const currentCount = this.errorCounts.get(ipAddress) || 0;
    if (currentCount >= this.maxErrorsPerMinute) {
      return true;
    }

    this.errorCounts.set(ipAddress, currentCount + 1);

    // Reset counter after 1 minute
    setTimeout(() => {
      this.errorCounts.delete(ipAddress);
    }, 60000);

    return false;
  }

  /**
   * Handle rate limited errors
   */
  private handleRateLimitedError(res: Response): void {
    const rateLimitHtml = `
      <!DOCTYPE html>
      <html>
      <head><title>Rate Limited</title></head>
      <body>
        <h1>Too Many Requests</h1>
        <p>Please wait a moment before trying again.</p>
      </body>
      </html>
    `;

    res.status(429).send(rateLimitHtml);
  }

  /**
   * Log error with comprehensive context
   */
  private logErrorWithContext(
    errorType: OAuthErrorType,
    message: string,
    details?: string,
    context?: ErrorContext,
  ): void {
    const logData = {
      errorType,
      message,
      details,
      context,
      timestamp: new Date().toISOString(),
    };

    this.logger.error('OAuth Error:', logData);
  }

  /**
   * Determine if error type allows retry
   */
  private canRetryError(errorType: OAuthErrorType): boolean {
    const nonRetryableErrors = [
      OAuthErrorType.ACCOUNT_INACTIVE,
      OAuthErrorType.ACCOUNT_LINKING_CONFLICT,
    ];

    return !nonRetryableErrors.includes(errorType);
  }

  /**
   * Get appropriate HTTP status code for error type
   */
  private getStatusCodeForErrorType(errorType: OAuthErrorType): number {
    switch (errorType) {
      case OAuthErrorType.AUTHENTICATION_FAILED:
      case OAuthErrorType.INVALID_USER_DATA:
      case OAuthErrorType.ACCOUNT_INACTIVE:
        return 401;
      case OAuthErrorType.ACCOUNT_LINKING_CONFLICT:
        return 409;
      case OAuthErrorType.INVALID_OAUTH_DATA:
        return 400;
      case OAuthErrorType.INTERNAL_SERVER_ERROR:
      default:
        return 500;
    }
  }

  /**
   * Get base URL for the application
   */
  private getBaseUrl(): string {
    return this.configService.get<string>('NODE_ENV') === 'production'
      ? 'https://your-domain.com'
      : 'http://localhost:3001';
  }
}
