import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { Request } from 'express';

export interface AuditLogEntry {
  id?: string;
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  requestId?: string;
  metadata?: Record<string, any>;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  success: boolean;
  errorMessage?: string;
  timestamp: Date;
  sessionId?: string;
  deviceId?: string;
}

export interface SecurityEvent {
  type:
    | 'LOGIN_ATTEMPT'
    | 'LOGIN_SUCCESS'
    | 'LOGIN_FAILURE'
    | 'OAUTH_LOGIN'
    | 'TOKEN_REFRESH'
    | 'LOGOUT'
    | 'PASSWORD_RESET'
    | 'ACCOUNT_LOCKED'
    | 'SUSPICIOUS_ACTIVITY'
    | 'RATE_LIMIT_EXCEEDED'
    | 'UNAUTHORIZED_ACCESS'
    | 'TOKEN_VALIDATION_FAILED'
    | 'CORS_VIOLATION'
    | 'DATA_BREACH_ATTEMPT';
  userId?: string;
  ipAddress: string;
  userAgent?: string;
  details: Record<string, any>;
  risk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: Date;
}

export interface LoggingConfig {
  enableConsoleLogging: boolean;
  enableDatabaseLogging: boolean;
  enableFileLogging: boolean;
  logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  retentionDays: number;
  sensitiveDataFields: string[];
  maxLogSize: number;
  rotateLogsDaily: boolean;
}

@Injectable()
export class AuditLoggingService {
  private readonly logger = new Logger(AuditLoggingService.name);
  private readonly config: LoggingConfig;
  private readonly sensitiveFields = [
    'password',
    'token',
    'accessToken',
    'refreshToken',
    'secret',
    'key',
    'authorization',
    'cookie',
    'session',
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.config = this.initializeConfig();
    this.logger.log('Audit logging service initialized');
  }

  /**
   * Initialize logging configuration
   */
  private initializeConfig(): LoggingConfig {
    return {
      enableConsoleLogging: this.configService.get<boolean>(
        'ENABLE_CONSOLE_LOGGING',
        true,
      ),
      enableDatabaseLogging: this.configService.get<boolean>(
        'ENABLE_DATABASE_LOGGING',
        true,
      ),
      enableFileLogging: this.configService.get<boolean>(
        'ENABLE_FILE_LOGGING',
        false,
      ),
      logLevel: this.configService.get<'DEBUG' | 'INFO' | 'WARN' | 'ERROR'>(
        'LOG_LEVEL',
        'INFO',
      ),
      retentionDays: this.configService.get<number>('LOG_RETENTION_DAYS', 90),
      sensitiveDataFields: this.configService
        .get<string>('SENSITIVE_DATA_FIELDS', '')
        .split(',')
        .filter(Boolean),
      maxLogSize: this.configService.get<number>(
        'MAX_LOG_SIZE',
        10 * 1024 * 1024,
      ), // 10MB
      rotateLogsDaily: this.configService.get<boolean>(
        'ROTATE_LOGS_DAILY',
        true,
      ),
    };
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    action: string,
    req: Request,
    success: boolean,
    userId?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'authentication',
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'],
      requestId: this.getRequestId(req),
      metadata: this.sanitizeMetadata(metadata),
      severity: success ? 'LOW' : 'MEDIUM',
      success,
      timestamp: new Date(),
      sessionId: this.getSessionId(req),
      deviceId: this.getDeviceId(req),
    };

    await this.writeLog(entry);
  }

  /**
   * Log OAuth events
   */
  async logOAuthEvent(
    provider: string,
    req: Request,
    success: boolean,
    userId?: string,
    errorMessage?: string,
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action: `OAUTH_${provider.toUpperCase()}`,
      resource: 'oauth',
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'],
      requestId: this.getRequestId(req),
      metadata: {
        provider,
        callbackUrl: req.url,
        redirectUrl: req.query.redirect_uri,
      },
      severity: success ? 'LOW' : 'MEDIUM',
      success,
      errorMessage,
      timestamp: new Date(),
    };

    await this.writeLog(entry);
  }

  /**
   * Log security events
   */
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    const entry: AuditLogEntry = {
      userId: event.userId,
      action: event.type,
      resource: 'security',
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      metadata: this.sanitizeMetadata(event.details),
      severity: this.mapRiskToSeverity(event.risk),
      success: !['FAILURE', 'VIOLATION', 'BREACH'].some((keyword) =>
        event.type.includes(keyword),
      ),
      timestamp: event.timestamp,
    };

    await this.writeLog(entry);

    // Send alerts for critical events
    if (event.risk === 'CRITICAL') {
      await this.sendSecurityAlert(event);
    }
  }

  /**
   * Log token events
   */
  async logTokenEvent(
    action: 'GENERATE' | 'REFRESH' | 'REVOKE' | 'VALIDATE' | 'BLACKLIST',
    req: Request,
    success: boolean,
    userId?: string,
    tokenType: 'ACCESS' | 'REFRESH' = 'ACCESS',
    metadata?: Record<string, any>,
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action: `TOKEN_${action}`,
      resource: 'token',
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'],
      requestId: this.getRequestId(req),
      metadata: {
        tokenType,
        ...this.sanitizeMetadata(metadata),
      },
      severity: action === 'BLACKLIST' ? 'HIGH' : 'LOW',
      success,
      timestamp: new Date(),
    };

    await this.writeLog(entry);
  }

  /**
   * Log API access events
   */
  async logApiAccess(
    req: Request,
    statusCode: number,
    responseTime: number,
    userId?: string,
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action: 'API_ACCESS',
      resource: req.path,
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'],
      requestId: this.getRequestId(req),
      metadata: {
        method: req.method,
        statusCode,
        responseTime,
        contentLength: req.headers['content-length'],
      },
      severity: statusCode >= 400 ? 'MEDIUM' : 'LOW',
      success: statusCode < 400,
      timestamp: new Date(),
    };

    // Only log if enabled and meets criteria
    if (this.shouldLogApiAccess(statusCode, req.path)) {
      await this.writeLog(entry);
    }
  }

  /**
   * Log rate limit violations
   */
  async logRateLimitViolation(
    req: Request,
    rule: string,
    remainingPoints: number,
  ): Promise<void> {
    const securityEvent: SecurityEvent = {
      type: 'RATE_LIMIT_EXCEEDED',
      userId: this.extractUserId(req),
      ipAddress: this.getClientIP(req),
      userAgent: req.headers['user-agent'],
      details: {
        rule,
        remainingPoints,
        path: req.path,
        method: req.method,
      },
      risk: 'MEDIUM',
      timestamp: new Date(),
    };

    await this.logSecurityEvent(securityEvent);
  }

  /**
   * Write log entry to configured destinations
   */
  private async writeLog(entry: AuditLogEntry): Promise<void> {
    try {
      // Console logging
      if (this.config.enableConsoleLogging) {
        this.writeToConsole(entry);
      }

      // Database logging
      if (this.config.enableDatabaseLogging) {
        await this.writeToDatabase(entry);
      }

      // File logging (if enabled)
      if (this.config.enableFileLogging) {
        await this.writeToFile(entry);
      }
    } catch (error) {
      this.logger.error('Failed to write log entry:', error);
    }
  }

  /**
   * Write to console
   */
  private writeToConsole(entry: AuditLogEntry): void {
    const logMessage = `[${entry.action}] ${entry.resource} - User: ${entry.userId || 'anonymous'} - IP: ${entry.ipAddress} - Success: ${entry.success}`;

    switch (entry.severity) {
      case 'CRITICAL':
      case 'HIGH':
        this.logger.error(logMessage, entry);
        break;
      case 'MEDIUM':
        this.logger.warn(logMessage, entry);
        break;
      case 'LOW':
      default:
        this.logger.log(logMessage, entry);
        break;
    }
  }

  /**
   * Write to database
   */
  private async writeToDatabase(entry: AuditLogEntry): Promise<void> {
    try {
      // Check if audit log table exists, if not create it
      // TODO: Create auditLog table in Prisma schema
      // For now, log to console as fallback
      this.logger.warn(
        'Database audit logging not implemented, using console fallback',
      );
      this.writeToConsole(entry);
    } catch (error) {
      // If audit table doesn't exist, log to console as fallback
      this.logger.error(
        'Database logging failed, falling back to console:',
        error,
      );
      this.writeToConsole(entry);
    }
  }

  /**
   * Write to file (placeholder for file logging implementation)
   */
  private async writeToFile(entry: AuditLogEntry): Promise<void> {
    // This would require additional file system operations
    // For now, we'll just log to console as a placeholder
    this.logger.debug('File logging not implemented, using console fallback');
    this.writeToConsole(entry);
  }

  /**
   * Sanitize metadata to remove sensitive information
   */
  private sanitizeMetadata(
    metadata: Record<string, any> = {},
  ): Record<string, any> {
    const sanitized = { ...metadata };
    const allSensitiveFields = [
      ...this.sensitiveFields,
      ...this.config.sensitiveDataFields,
    ];

    for (const key of Object.keys(sanitized)) {
      if (
        allSensitiveFields.some((field) =>
          key.toLowerCase().includes(field.toLowerCase()),
        )
      ) {
        sanitized[key] = '***REDACTED***';
      }
    }

    return sanitized;
  }

  /**
   * Extract client IP address
   */
  private getClientIP(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string) ||
      (req.headers['x-real-ip'] as string) ||
      req.connection.remoteAddress ||
      req.ip ||
      'unknown'
    )
      .split(',')[0]
      .trim();
  }

  /**
   * Get request ID
   */
  private getRequestId(req: Request): string {
    return (
      (req.headers['x-request-id'] as string) ||
      (req.headers['x-correlation-id'] as string) ||
      `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    );
  }

  /**
   * Get session ID
   */
  private getSessionId(req: Request): string | undefined {
    return (req.headers['x-session-id'] as string) || req.cookies?.sessionId;
  }

  /**
   * Get device ID
   */
  private getDeviceId(req: Request): string | undefined {
    return req.headers['x-device-id'] as string;
  }

  /**
   * Extract user ID from request
   */
  private extractUserId(req: Request): string | undefined {
    if (req.user && (req.user as any).id) {
      return (req.user as any).id;
    }

    // Try to extract from JWT token
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const payload = JSON.parse(
          Buffer.from(token.split('.')[1], 'base64').toString(),
        );
        return payload.sub || payload.userId || payload.id;
      } catch {
        // Invalid token
      }
    }

    return undefined;
  }

  /**
   * Map risk level to severity
   */
  private mapRiskToSeverity(
    risk: SecurityEvent['risk'],
  ): AuditLogEntry['severity'] {
    switch (risk) {
      case 'CRITICAL':
        return 'CRITICAL';
      case 'HIGH':
        return 'HIGH';
      case 'MEDIUM':
        return 'MEDIUM';
      case 'LOW':
        return 'LOW';
      default:
        return 'MEDIUM';
    }
  }

  /**
   * Send security alert for critical events
   */
  private async sendSecurityAlert(event: SecurityEvent): Promise<void> {
    // This would integrate with alerting systems (email, Slack, etc.)
    this.logger.error('SECURITY ALERT', {
      type: event.type,
      userId: event.userId,
      ipAddress: event.ipAddress,
      risk: event.risk,
      details: event.details,
      timestamp: event.timestamp,
    });

    // TODO: Implement actual alerting mechanism
    // - Email notifications
    // - Slack/Teams notifications
    // - SMS alerts
    // - Third-party security tools integration
  }

  /**
   * Determine if API access should be logged
   */
  private shouldLogApiAccess(statusCode: number, path: string): boolean {
    // Always log errors
    if (statusCode >= 400) return true;

    // Log auth-related endpoints
    if (path.includes('/auth/')) return true;

    // Skip health checks and static assets
    if (path.includes('/health') || path.includes('/static/')) return false;

    // Log based on configuration
    return this.config.logLevel === 'DEBUG';
  }

  /**
   * Clean up old log entries
   */
  async cleanupOldLogs(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

      // TODO: Implement when auditLog table is created
      const deletedCount = { count: 0 };

      this.logger.log(`Cleaned up ${deletedCount.count} old audit log entries`);
    } catch (error) {
      this.logger.error('Failed to cleanup old logs:', error);
    }
  }

  /**
   * Get audit log statistics
   */
  async getLogStatistics(days: number = 7): Promise<{
    totalLogs: number;
    successfulLogs: number;
    failedLogs: number;
    severityBreakdown: Record<string, number>;
    actionBreakdown: Record<string, number>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // TODO: Implement when auditLog table is created
      const logs: Array<{
        success: boolean;
        severity: string;
        action: string;
      }> = [];

      const successfulLogs = logs.filter((log) => log.success).length;
      const failedLogs = logs.filter((log) => !log.success).length;

      const severityBreakdown = logs.reduce(
        (acc, log) => {
          acc[log.severity] = (acc[log.severity] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      const actionBreakdown = logs.reduce(
        (acc, log) => {
          acc[log.action] = (acc[log.action] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      return {
        totalLogs: logs.length,
        successfulLogs,
        failedLogs,
        severityBreakdown,
        actionBreakdown,
      };
    } catch (error) {
      this.logger.error('Failed to get log statistics:', error);
      return {
        totalLogs: 0,
        successfulLogs: 0,
        failedLogs: 0,
        severityBreakdown: {},
        actionBreakdown: {},
      };
    }
  }
}
