import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';
import { TokenPairDto } from '../dto/token-pair.dto';
import { UserResponseDto } from '../../users/dto/user-response.dto';
import {
  OAuthSuccessData,
  OAuthApiResponseDto,
  OAuthErrorData,
  OAuthAccountLinkingData,
} from '../dto/oauth-redirect.dto';
import { OAuthHtmlTemplateService } from './oauth-html-template.service';

export interface OAuthRequestContext {
  isApiRequest: boolean;
  userAgent?: string;
  acceptHeader?: string;
  contentType?: string;
  isPopup?: boolean;
}

export interface OAuthResponseData {
  user: UserResponseDto;
  tokenPair: TokenPairDto;
  isNewUser: boolean;
  provider: string;
}

@Injectable()
export class OAuthResponseHandlerService {
  private readonly logger = new Logger(OAuthResponseHandlerService.name);

  constructor(
    private readonly oauthHtmlTemplateService: OAuthHtmlTemplateService,
  ) {}

  /**
   * Determine if the request is for API response or HTML redirect
   */
  analyzeRequest(req: any): OAuthRequestContext {
    const acceptHeader = req.headers.accept || '';
    const userAgent = req.headers['user-agent'] || '';
    const contentType = req.headers['content-type'] || '';
    const xRequestedWith = req.headers['x-requested-with'] || '';
    const authorizationHeader = req.headers.authorization || '';

    // Check for explicit API request indicators
    const isApiRequest =
      // Accept header indicates JSON preference
      acceptHeader.includes('application/json') ||
      // XMLHttpRequest indicator
      xRequestedWith === 'XMLHttpRequest' ||
      // Authorization header present (programmatic access)
      authorizationHeader.startsWith('Bearer ') ||
      // Content-Type indicates API usage
      contentType.includes('application/json') ||
      // Custom header for API requests
      req.headers['x-oauth-api-request'] === 'true';

    // Check if it's a popup window
    const isPopup =
      req.query.popup === 'true' || req.headers['x-oauth-popup'] === 'true';

    this.logger.debug('OAuth request analysis:', {
      acceptHeader,
      userAgent,
      contentType,
      xRequestedWith,
      hasAuthHeader: !!authorizationHeader,
      isApiRequest,
      isPopup,
    });

    return {
      isApiRequest,
      userAgent,
      acceptHeader,
      contentType,
      isPopup,
    };
  }

  /**
   * Handle successful OAuth authentication with dual response
   */
  async handleSuccess(
    res: Response,
    data: OAuthResponseData,
    context: OAuthRequestContext,
  ): Promise<void> {
    try {
      if (context.isApiRequest) {
        // Return API response
        await this.sendApiSuccessResponse(res, data);
      } else {
        // Return HTML redirect response
        await this.sendHtmlSuccessResponse(res, data, context);
      }
    } catch (error) {
      this.logger.error('Error handling OAuth success response:', error);
      throw error;
    }
  }

  /**
   * Handle OAuth error with dual response
   */
  async handleError(
    res: Response,
    errorType: string,
    message: string,
    details: string,
    context: OAuthRequestContext,
  ): Promise<void> {
    try {
      if (context.isApiRequest) {
        // Return API error response
        await this.sendApiErrorResponse(res, errorType, message, details);
      } else {
        // Return HTML error response
        await this.sendHtmlErrorResponse(res, errorType, message, details);
      }
    } catch (error) {
      this.logger.error('Error handling OAuth error response:', error);
      throw error;
    }
  }

  /**
   * Send API success response (JSON)
   */
  private async sendApiSuccessResponse(
    res: Response,
    data: OAuthResponseData,
  ): Promise<void> {
    const apiResponse = new OAuthApiResponseDto({
      accessToken: data.tokenPair.accessToken,
      refreshToken: data.tokenPair.refreshToken,
      accessTokenExpiresIn: data.tokenPair.accessTokenExpiresIn,
      refreshTokenExpiresIn: data.tokenPair.refreshTokenExpiresIn,
      tokenType: data.tokenPair.tokenType,
      user: data.user,
      isNewUser: data.isNewUser,
      provider: data.provider,
      message: data.isNewUser
        ? 'Welcome to Orbitum! Your account has been created successfully.'
        : 'OAuth authentication successful! Welcome back.',
    });

    this.logger.log(`OAuth API success response for user ${data.user.id}`);

    res.status(200).json(apiResponse);
  }

  /**
   * Send HTML success response (redirect page)
   */
  private async sendHtmlSuccessResponse(
    res: Response,
    data: OAuthResponseData,
    context: OAuthRequestContext,
  ): Promise<void> {
    const successData = new OAuthSuccessData({
      accessToken: data.tokenPair.accessToken,
      refreshToken: data.tokenPair.refreshToken,
      accessTokenExpiresIn: data.tokenPair.accessTokenExpiresIn,
      refreshTokenExpiresIn: data.tokenPair.refreshTokenExpiresIn,
      tokenType: data.tokenPair.tokenType,
      user: data.user,
      redirectUrl: this.buildRedirectUrlWithTokens(
        `${process.env.FRONTEND_URL}/auth/${data.provider}/success`,
        data.tokenPair,
        data.user,
      ),
      isNewUser: data.isNewUser,
      message: data.isNewUser
        ? 'Welcome to Orbitum! Your account has been created successfully.'
        : 'Authentication successful! Welcome back.',
    });

    const htmlResponse = this.oauthHtmlTemplateService.generateSuccessPage(
      successData,
      {
        autoRedirect: true,
        redirectDelay: 3000,
        showManualRedirect: true,
        isPopup: context.isPopup,
      },
    );

    this.logger.log(`OAuth HTML success response for user ${data.user.id}`);

    res.status(200).send(htmlResponse);
  }

  /**
   * Send API error response (JSON)
   */
  private async sendApiErrorResponse(
    res: Response,
    errorType: string,
    message: string,
    details: string,
  ): Promise<void> {
    const errorResponse = {
      statusCode: 400,
      error: 'OAuth Authentication Failed',
      message,
      errorType,
      details,
      timestamp: new Date().toISOString(),
    };

    this.logger.error('OAuth API error response:', errorResponse);

    res.status(400).json(errorResponse);
  }

  /**
   * Send HTML error response (error page)
   */
  private async sendHtmlErrorResponse(
    res: Response,
    errorType: string,
    message: string,
    details: string,
  ): Promise<void> {
    const errorData = new OAuthErrorData({
      errorType: errorType as any,
      message,
      details,
      redirectUrl: `${process.env.FRONTEND_URL}/auth/error`,
      canRetry: true,
      retryUrl: `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/v1/auth/google`,
      timestamp: new Date().toISOString(),
    });

    const htmlResponse =
      this.oauthHtmlTemplateService.generateErrorPage(errorData);

    this.logger.error('OAuth HTML error response:', {
      errorType,
      message,
      details,
    });

    res.status(400).send(htmlResponse);
  }

  /**
   * Handle account linking with dual response
   */
  async handleAccountLinking(
    res: Response,
    data: OAuthResponseData,
    context: OAuthRequestContext,
  ): Promise<void> {
    try {
      if (context.isApiRequest) {
        // For API requests, return success response with linking info
        const apiResponse = new OAuthApiResponseDto({
          accessToken: data.tokenPair.accessToken,
          refreshToken: data.tokenPair.refreshToken,
          accessTokenExpiresIn: data.tokenPair.accessTokenExpiresIn,
          refreshTokenExpiresIn: data.tokenPair.refreshTokenExpiresIn,
          tokenType: data.tokenPair.tokenType,
          user: data.user,
          isNewUser: false,
          provider: data.provider,
          message: `Your ${data.provider} account has been linked successfully.`,
        });

        res.status(200).json(apiResponse);
      } else {
        // Return HTML account linking response
        const linkingData = new OAuthAccountLinkingData({
          existingUser: data.user,
          provider: data.provider,
          accessToken: data.tokenPair.accessToken,
          expiresIn: data.tokenPair.accessTokenExpiresIn,
          redirectUrl: this.buildRedirectUrlWithTokens(
            `${process.env.FRONTEND_URL}/profile/linked`,
            data.tokenPair,
            data.user,
          ),
          message: `Your ${data.provider} account has been linked successfully.`,
        });

        const htmlResponse =
          this.oauthHtmlTemplateService.generateAccountLinkingPage(
            linkingData,
            {
              autoRedirect: true,
              redirectDelay: 3000,
              showManualRedirect: true,
              isPopup: context.isPopup,
            },
          );

        res.status(200).send(htmlResponse);
      }

      this.logger.log(
        `OAuth account linking response for user ${data.user.id}`,
      );
    } catch (error) {
      this.logger.error(
        'Error handling OAuth account linking response:',
        error,
      );
      throw error;
    }
  }

  /**
   * Build redirect URL with OAuth tokens as query parameters
   */
  private buildRedirectUrlWithTokens(
    baseUrl: string,
    tokenPair: any,
    user: any,
  ): string {
    try {
      const url = new URL(baseUrl);

      // Add token parameters
      if (tokenPair.accessToken) {
        url.searchParams.set('access_token', tokenPair.accessToken);
      }

      if (tokenPair.refreshToken) {
        url.searchParams.set('refresh_token', tokenPair.refreshToken);
      }

      // Add user data as JSON string
      if (user) {
        const userData = {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
        };
        url.searchParams.set('user', JSON.stringify(userData));
      }

      // Add token metadata
      if (tokenPair.accessTokenExpiresIn) {
        url.searchParams.set(
          'expires_in',
          tokenPair.accessTokenExpiresIn.toString(),
        );
      }

      if (tokenPair.tokenType) {
        url.searchParams.set('token_type', tokenPair.tokenType);
      }

      return url.toString();
    } catch (error) {
      this.logger.error('Error building redirect URL with tokens:', error);
      // Fallback to base URL without tokens
      return baseUrl;
    }
  }
}
