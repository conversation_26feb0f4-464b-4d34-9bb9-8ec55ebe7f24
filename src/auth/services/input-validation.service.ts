import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import validator from 'validator';

export interface ValidationRule {
  field: string;
  type:
    | 'string'
    | 'email'
    | 'url'
    | 'uuid'
    | 'number'
    | 'boolean'
    | 'array'
    | 'object';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => boolean;
  sanitize?: boolean;
  allowedValues?: any[];
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  sanitizedData?: any;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface SecurityValidationOptions {
  preventXSS: boolean;
  preventSQLInjection: boolean;
  preventPathTraversal: boolean;
  maxDepth: number;
  allowedHtmlTags: string[];
  blockedPatterns: RegExp[];
}

@Injectable()
export class InputValidationService {
  private readonly logger = new Logger(InputValidationService.name);
  private readonly defaultSecurityOptions: SecurityValidationOptions;

  // Security patterns
  private readonly securityPatterns = {
    xss: [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /onload\s*=/gi,
      /onerror\s*=/gi,
      /onclick\s*=/gi,
      /onmouseover\s*=/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>/gi,
    ],
    sqlInjection: [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(--|\/\*|\*\/)/gi,
      /(\bUNION\b.*\bSELECT\b)/gi,
      /(\b(EXEC|EXECUTE)\b)/gi,
    ],
    pathTraversal: [
      /\.\.\/|\.\.\\|\.\.%2f|\.\.%5c/gi,
      /%2e%2e%2f|%2e%2e%5c/gi,
      /\.\.\//gi,
      /\.\.\\\\|\.\.\/\//gi,
    ],
    commandInjection: [
      /(\||;|&|`|\$\()/gi,
      /(\b(eval|exec|system|shell_exec|passthru|popen)\b)/gi,
    ],
  };

  constructor(private readonly configService: ConfigService) {
    this.defaultSecurityOptions = {
      preventXSS: true,
      preventSQLInjection: true,
      preventPathTraversal: true,
      maxDepth: 5,
      allowedHtmlTags: [],
      blockedPatterns: [],
    };
  }

  /**
   * Validate data against rules
   */
  async validateData(
    data: any,
    rules: ValidationRule[],
    securityOptions?: Partial<SecurityValidationOptions>,
  ): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const sanitizedData = { ...data };
    const options = { ...this.defaultSecurityOptions, ...securityOptions };

    for (const rule of rules) {
      const value = data[rule.field];
      const validationError = await this.validateField(
        rule.field,
        value,
        rule,
        options,
      );

      if (validationError) {
        errors.push(validationError);
      } else if (rule.sanitize && value !== undefined && value !== null) {
        sanitizedData[rule.field] = this.sanitizeValue(value, rule, options);
      }
    }

    // Security validation
    const securityErrors = this.performSecurityValidation(data, options);
    errors.push(...securityErrors);

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: errors.length === 0 ? sanitizedData : undefined,
    };
  }

  /**
   * Validate authentication input
   */
  async validateAuthInput(data: any): Promise<ValidationResult> {
    const rules: ValidationRule[] = [
      {
        field: 'emailOrUsername',
        type: 'string',
        required: true,
        minLength: 3,
        maxLength: 254,
        sanitize: true,
        message: 'Email or username must be between 3 and 254 characters',
      },
      {
        field: 'password',
        type: 'string',
        required: true,
        minLength: 8,
        maxLength: 128,
        message: 'Password must be between 8 and 128 characters',
      },
      {
        field: 'rememberMe',
        type: 'boolean',
        required: false,
      },
    ];

    return this.validateData(data, rules);
  }

  /**
   * Validate OAuth callback data
   */
  async validateOAuthData(data: any): Promise<ValidationResult> {
    const rules: ValidationRule[] = [
      {
        field: 'email',
        type: 'email',
        required: true,
        maxLength: 254,
        sanitize: true,
        message: 'Valid email is required',
      },
      {
        field: 'firstName',
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100,
        sanitize: true,
        pattern: /^[a-zA-Z\s\-'\.]+$/,
        message: 'First name contains invalid characters',
      },
      {
        field: 'lastName',
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100,
        sanitize: true,
        pattern: /^[a-zA-Z\s\-'\.]+$/,
        message: 'Last name contains invalid characters',
      },
      {
        field: 'googleId',
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 50,
        pattern: /^[0-9]+$/,
        message: 'Invalid Google ID format',
      },
      {
        field: 'provider',
        type: 'string',
        required: true,
        allowedValues: ['google', 'facebook', 'github', 'linkedin'],
        message: 'Invalid OAuth provider',
      },
      {
        field: 'accessToken',
        type: 'string',
        required: true,
        minLength: 10,
        maxLength: 2048,
        message: 'Invalid access token',
      },
    ];

    return this.validateData(data, rules, {
      preventXSS: true,
      preventSQLInjection: true,
    });
  }

  /**
   * Validate token request
   */
  async validateTokenRequest(data: any): Promise<ValidationResult> {
    const rules: ValidationRule[] = [
      {
        field: 'refreshToken',
        type: 'string',
        required: true,
        minLength: 10,
        maxLength: 512,
        pattern: /^[a-zA-Z0-9]+$/,
        message: 'Invalid refresh token format',
      },
    ];

    return this.validateData(data, rules);
  }

  /**
   * Validate device info
   */
  async validateDeviceInfo(data: any): Promise<ValidationResult> {
    const rules: ValidationRule[] = [
      {
        field: 'userAgent',
        type: 'string',
        required: false,
        maxLength: 512,
        sanitize: true,
        message: 'User agent too long',
      },
      {
        field: 'ipAddress',
        type: 'string',
        required: false,
        customValidator: (value: string) => {
          return validator.isIP(value) || value === 'unknown';
        },
        message: 'Invalid IP address format',
      },
      {
        field: 'deviceId',
        type: 'string',
        required: false,
        maxLength: 128,
        pattern: /^[a-zA-Z0-9\-_]+$/,
        message: 'Invalid device ID format',
      },
    ];

    return this.validateData(data, rules);
  }

  /**
   * Validate URL parameters
   */
  validateUrlParameters(params: any): ValidationResult {
    const errors: ValidationError[] = [];

    // Validate redirect URLs
    if (params.redirect_uri) {
      if (!validator.isURL(params.redirect_uri, { require_protocol: true })) {
        errors.push({
          field: 'redirect_uri',
          message: 'Invalid redirect URI format',
          code: 'INVALID_URL',
          value: params.redirect_uri,
        });
      } else {
        // Check against allowed domains
        const allowedDomains = this.getAllowedRedirectDomains();
        const url = new URL(params.redirect_uri);

        if (!allowedDomains.includes(url.hostname)) {
          errors.push({
            field: 'redirect_uri',
            message: 'Redirect URI domain not allowed',
            code: 'DOMAIN_NOT_ALLOWED',
            value: url.hostname,
          });
        }
      }
    }

    // Validate state parameter
    if (params.state && params.state.length > 128) {
      errors.push({
        field: 'state',
        message: 'State parameter too long',
        code: 'PARAM_TOO_LONG',
        value: params.state.length,
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate individual field
   */
  private async validateField(
    fieldName: string,
    value: any,
    rule: ValidationRule,
    options: SecurityValidationOptions,
  ): Promise<ValidationError | null> {
    // Check if required
    if (
      rule.required &&
      (value === undefined || value === null || value === '')
    ) {
      return {
        field: fieldName,
        message: rule.message || `${fieldName} is required`,
        code: 'REQUIRED',
        value,
      };
    }

    // Skip validation if value is not provided and not required
    if (!rule.required && (value === undefined || value === null)) {
      return null;
    }

    // Type validation
    const typeError = this.validateType(
      fieldName,
      value,
      rule.type,
      rule.message,
    );
    if (typeError) return typeError;

    // Length validation for strings
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return {
          field: fieldName,
          message:
            rule.message ||
            `${fieldName} must be at least ${rule.minLength} characters`,
          code: 'MIN_LENGTH',
          value: value.length,
        };
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        return {
          field: fieldName,
          message:
            rule.message ||
            `${fieldName} must be at most ${rule.maxLength} characters`,
          code: 'MAX_LENGTH',
          value: value.length,
        };
      }
    }

    // Range validation for numbers
    if (rule.type === 'number' && typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        return {
          field: fieldName,
          message: rule.message || `${fieldName} must be at least ${rule.min}`,
          code: 'MIN_VALUE',
          value,
        };
      }

      if (rule.max !== undefined && value > rule.max) {
        return {
          field: fieldName,
          message: rule.message || `${fieldName} must be at most ${rule.max}`,
          code: 'MAX_VALUE',
          value,
        };
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        return {
          field: fieldName,
          message: rule.message || `${fieldName} format is invalid`,
          code: 'INVALID_FORMAT',
          value,
        };
      }
    }

    // Allowed values validation
    if (rule.allowedValues && !rule.allowedValues.includes(value)) {
      return {
        field: fieldName,
        message:
          rule.message ||
          `${fieldName} must be one of: ${rule.allowedValues.join(', ')}`,
        code: 'INVALID_VALUE',
        value,
      };
    }

    // Custom validator
    if (rule.customValidator && !rule.customValidator(value)) {
      return {
        field: fieldName,
        message: rule.message || `${fieldName} validation failed`,
        code: 'CUSTOM_VALIDATION',
        value,
      };
    }

    return null;
  }

  /**
   * Validate data type
   */
  private validateType(
    fieldName: string,
    value: any,
    expectedType: string,
    message?: string,
  ): ValidationError | null {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a string`,
            code: 'INVALID_TYPE',
            value: typeof value,
          };
        }
        break;

      case 'email':
        if (typeof value !== 'string' || !validator.isEmail(value)) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a valid email`,
            code: 'INVALID_EMAIL',
            value,
          };
        }
        break;

      case 'url':
        if (typeof value !== 'string' || !validator.isURL(value)) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a valid URL`,
            code: 'INVALID_URL',
            value,
          };
        }
        break;

      case 'uuid':
        if (typeof value !== 'string' || !validator.isUUID(value)) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a valid UUID`,
            code: 'INVALID_UUID',
            value,
          };
        }
        break;

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a number`,
            code: 'INVALID_NUMBER',
            value,
          };
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field: fieldName,
            message: message || `${fieldName} must be a boolean`,
            code: 'INVALID_BOOLEAN',
            value: typeof value,
          };
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be an array`,
            code: 'INVALID_ARRAY',
            value: typeof value,
          };
        }
        break;

      case 'object':
        if (
          typeof value !== 'object' ||
          value === null ||
          Array.isArray(value)
        ) {
          return {
            field: fieldName,
            message: message || `${fieldName} must be an object`,
            code: 'INVALID_OBJECT',
            value: typeof value,
          };
        }
        break;
    }

    return null;
  }

  /**
   * Perform security validation
   */
  private performSecurityValidation(
    data: any,
    options: SecurityValidationOptions,
  ): ValidationError[] {
    const errors: ValidationError[] = [];

    const checkValue = (value: any, field: string) => {
      if (typeof value !== 'string') return;

      // XSS validation
      if (options.preventXSS) {
        for (const pattern of this.securityPatterns.xss) {
          if (pattern.test(value)) {
            errors.push({
              field,
              message: 'Potentially malicious content detected',
              code: 'XSS_DETECTED',
              value: 'REDACTED',
            });
            break;
          }
        }
      }

      // SQL Injection validation
      if (options.preventSQLInjection) {
        for (const pattern of this.securityPatterns.sqlInjection) {
          if (pattern.test(value)) {
            errors.push({
              field,
              message: 'Potentially malicious SQL detected',
              code: 'SQL_INJECTION_DETECTED',
              value: 'REDACTED',
            });
            break;
          }
        }
      }

      // Path Traversal validation
      if (options.preventPathTraversal) {
        for (const pattern of this.securityPatterns.pathTraversal) {
          if (pattern.test(value)) {
            errors.push({
              field,
              message: 'Path traversal attempt detected',
              code: 'PATH_TRAVERSAL_DETECTED',
              value: 'REDACTED',
            });
            break;
          }
        }
      }
    };

    // Recursively check all string values
    this.traverseObject(data, checkValue, 0, options.maxDepth);

    return errors;
  }

  /**
   * Sanitize value based on rules
   */
  private sanitizeValue(
    value: any,
    rule: ValidationRule,
    options: SecurityValidationOptions,
  ): any {
    if (typeof value !== 'string') return value;

    let sanitized = value;

    // Basic sanitization
    sanitized = sanitized.trim();

    // XSS protection
    if (options.preventXSS) {
      sanitized = validator.escape(sanitized);
    }

    // Remove null bytes
    sanitized = sanitized.replace(/\x00/g, '');

    return sanitized;
  }

  /**
   * Traverse object recursively
   */
  private traverseObject(
    obj: any,
    callback: (value: any, field: string) => void,
    depth: number,
    maxDepth: number,
  ): void {
    if (depth > maxDepth) return;

    for (const [key, value] of Object.entries(obj)) {
      callback(value, key);

      if (
        typeof value === 'object' &&
        value !== null &&
        !Array.isArray(value)
      ) {
        this.traverseObject(value, callback, depth + 1, maxDepth);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            this.traverseObject(item, callback, depth + 1, maxDepth);
          } else {
            callback(item, `${key}[${index}]`);
          }
        });
      }
    }
  }

  /**
   * Get allowed redirect domains from configuration
   */
  private getAllowedRedirectDomains(): string[] {
    const frontendUrl = this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
    const additionalDomains = this.configService.get<string>(
      'ALLOWED_REDIRECT_DOMAINS',
      '',
    );

    const domains = [new URL(frontendUrl).hostname];

    if (additionalDomains) {
      domains.push(...additionalDomains.split(',').map((d) => d.trim()));
    }

    return domains;
  }

  /**
   * Throw validation exception with structured errors
   */
  throwValidationException(errors: ValidationError[]): never {
    const errorMessage = errors
      .map((e) => `${e.field}: ${e.message}`)
      .join('; ');

    this.logger.warn('Validation failed:', { errors });

    throw new BadRequestException({
      message: 'Validation failed',
      errors,
      statusCode: 400,
    });
  }
}
