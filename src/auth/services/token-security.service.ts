import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { DeviceInfoDto } from '../dto/device-info.dto';

@Injectable()
export class TokenSecurityService {
  private readonly suspiciousActivities = new Map<string, number>();

  /**
   * Validate token request rate limiting
   */
  validateTokenRequestRate(identifier: string): boolean {
    const current = this.suspiciousActivities.get(identifier) || 0;
    const maxAttempts = 10; // 10 refresh attempts per minute

    if (current >= maxAttempts) {
      return false;
    }

    this.suspiciousActivities.set(identifier, current + 1);

    // Reset after 1 minute
    setTimeout(() => {
      this.suspiciousActivities.delete(identifier);
    }, 60000);

    return true;
  }

  /**
   * Validate device fingerprint changes
   */
  validateDeviceFingerprint(
    storedFingerprint: any,
    currentFingerprint: DeviceInfoDto,
  ): boolean {
    // Basic validation - can be expanded with more sophisticated fingerprinting
    return (
      storedFingerprint.userAgent === currentFingerprint.userAgent ||
      storedFingerprint.ipAddress === currentFingerprint.ipAddress
    );
  }

  /**
   * Generate secure device ID
   */
  generateDeviceId(deviceInfo: DeviceInfoDto): string {
    const fingerprint = `${deviceInfo.userAgent}_${deviceInfo.ipAddress}`;
    return crypto.createHash('sha256').update(fingerprint).digest('hex');
  }

  /**
   * Validate access token format and structure
   */
  async validateAccessToken(token: string): Promise<{
    isValid: boolean;
    error?: string;
    decoded?: any;
  }> {
    try {
      if (!token || typeof token !== 'string') {
        return {
          isValid: false,
          error: 'Invalid token format',
        };
      }

      // Basic JWT format validation
      const parts = token.split('.');
      if (parts.length !== 3) {
        return {
          isValid: false,
          error: 'Invalid JWT format',
        };
      }

      // Validate each part is base64 encoded
      try {
        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

        return {
          isValid: true,
          decoded: { header, payload },
        };
      } catch (decodeError) {
        return {
          isValid: false,
          error: 'Invalid token encoding',
        };
      }
    } catch (error) {
      return {
        isValid: false,
        error: 'Token validation failed',
      };
    }
  }
}
