import { WalletType, ChainSymbol } from '../../../generated/prisma';

/**
 * Interface for wallet entity from database
 */
export interface WalletEntity {
  id: string;
  userId: string;
  walletAddress: string;
  walletType: string;
  isPrimary: boolean;
  status: string;
  createdAt: Date;
}

/**
 * Interface for chain entity from database
 */
export interface ChainEntity {
  id: string;
  chainId: string;
  chainName: string;
  symbol: string;
  walletId: string;
  createdAt: Date;
}

/**
 * Interface for wallet with chain information
 */
export interface WalletWithChain extends WalletEntity {
  chains: ChainEntity[];
}

/**
 * Interface for wallet creation data with multiple chains support
 */
export interface WalletCreationData {
  userId: string;
  walletAddress: string;
  walletType: WalletType;
  isPrimary: boolean;
  status: string;
  chains: {
    chainId: string;
    chainName: string;
    symbol: ChainSymbol;
  }[];
}

/**
 * Interface for wallet validation result
 */
export interface WalletValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Interface for wallet business rules
 */
export interface WalletBusinessRules {
  maxWalletsPerUser: number;
  supportedWalletTypes: WalletType[];
  supportedChainSymbols: ChainSymbol[];
  allowedAddressFormats: RegExp[];
  minAddressLength: number;
  maxAddressLength: number;
}

/**
 * Interface for wallet service configuration
 */
export interface WalletServiceConfig {
  businessRules: WalletBusinessRules;
  enableLogging: boolean;
  enableValidation: boolean;
  enablePrimaryWalletAutoAssignment: boolean;
}

/**
 * Type for wallet operation result
 */
export type WalletOperationResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
};

/**
 * Enum for wallet operation types
 */
export enum WalletOperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  STATUS_UPDATE = 'STATUS_UPDATE',
  PRIMARY_UPDATE = 'PRIMARY_UPDATE',
  CHAIN_UPDATE = 'CHAIN_UPDATE',
}

/**
 * Interface for wallet audit log
 */
export interface WalletAuditLog {
  id: string;
  userId: string;
  walletId: string;
  operation: WalletOperationType;
  oldStatus?: string;
  newStatus?: string;
  performedBy: string;
  isAdmin: boolean;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Type for validation function
 */
export type ValidationFunction<T> = (data: T) => WalletValidationResult;

/**
 * Interface for wallet address validation
 */
export interface AddressValidation {
  format: RegExp;
  name: string;
  description: string;
  examples: string[];
}

/**
 * Interface for supported blockchain networks
 */
export interface SupportedNetwork {
  chainId: string;
  chainName: string;
  symbol: ChainSymbol;
  isMainnet: boolean;
  rpcUrls: string[];
  blockExplorerUrls: string[];
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}
