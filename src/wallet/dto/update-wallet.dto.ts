import { IsBoolean, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for updating wallet information
 */
export class UpdateWalletDto {
  @ApiProperty({
    description:
      'Whether this wallet should be set as the primary wallet for the user',
    example: true,
    type: Boolean,
  })
  @IsBoolean({ message: 'is_primary must be a boolean value' })
  @IsNotEmpty({ message: 'is_primary field is required' })
  is_primary: boolean;
}
