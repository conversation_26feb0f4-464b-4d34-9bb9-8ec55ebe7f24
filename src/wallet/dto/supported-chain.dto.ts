import { ApiProperty } from '@nestjs/swagger';
import { ChainSymbol } from '../../../generated/prisma';

/**
 * DTO for native currency information
 */
export class NativeCurrencyDto {
  @ApiProperty({
    description: 'Full name of the native currency',
    example: 'Ether',
  })
  name: string;

  @ApiProperty({
    description: 'Symbol of the native currency',
    example: 'ETH',
  })
  symbol: string;

  @ApiProperty({
    description: 'Number of decimal places for the currency',
    example: 18,
  })
  decimals: number;
}

/**
 * DTO for supported blockchain network information
 * Provides comprehensive details about each supported blockchain
 */
export class SupportedChainDto {
  @ApiProperty({
    description: 'Unique identifier for the blockchain network',
    example: '1',
  })
  chainId: string;

  @ApiProperty({
    description: 'Human-readable name of the blockchain network',
    example: 'Ethereum Mainnet',
  })
  chainName: string;

  @ApiProperty({
    description: 'Native token symbol for the blockchain network',
    enum: ChainSymbol,
    example: ChainSymbol.ETH,
  })
  symbol: ChainSymbol;

  @ApiProperty({
    description: 'Whether this is a mainnet or testnet',
    example: true,
  })
  isMainnet: boolean;

  @ApiProperty({
    description: 'Array of RPC URLs for connecting to the network',
    type: [String],
    example: [
      'https://mainnet.infura.io/v3/',
      'https://eth-mainnet.alchemyapi.io/v2/',
    ],
  })
  rpcUrls: string[];

  @ApiProperty({
    description: 'Array of block explorer URLs for the network',
    type: [String],
    example: ['https://etherscan.io'],
  })
  blockExplorerUrls: string[];

  @ApiProperty({
    description: 'Native currency information for the network',
    type: NativeCurrencyDto,
  })
  nativeCurrency: NativeCurrencyDto;

  constructor(data: Partial<SupportedChainDto>) {
    Object.assign(this, data);
  }
}
