import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty } from 'class-validator';

/**
 * Request DTO for wallet chains endpoint
 */
export class WalletChainsRequestDto {
  @ApiProperty({
    description: 'Unique identifier of the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
    format: 'uuid',
  })
  @IsUUID(4, { message: 'Wallet ID must be a valid UUID format' })
  @IsNotEmpty({ message: 'Wallet ID is required' })
  wallet_id: string;
}
