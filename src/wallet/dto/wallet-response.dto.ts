import { ApiProperty } from '@nestjs/swagger';
import { WalletType, ChainSymbol } from '../../../generated/prisma';

/**
 * Response DTO for chain information
 */
export class ChainResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the chain record',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  id: string;

  @ApiProperty({
    description: 'Blockchain network identifier',
    example: '1',
  })
  chain_id: string;

  @ApiProperty({
    description: 'Human-readable name of the blockchain network',
    example: 'Ethereum Mainnet',
  })
  chain_name: string;

  @ApiProperty({
    description: 'Associated wallet identifier',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  wallet_id: string;

  @ApiProperty({
    description: 'Native token symbol for the blockchain network',
    enum: ChainSymbol,
    example: ChainSymbol.ETH,
  })
  symbol: ChainSymbol;

  @ApiProperty({
    description: 'Timestamp when the chain record was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  created_at: Date;
}

/**
 * Response DTO for wallet information
 */
export class WalletResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  id: string;

  @ApiProperty({
    description: 'User identifier who owns the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d481',
  })
  user_id: string;

  @ApiProperty({
    description: 'Blockchain wallet address',
    example: '******************************************',
  })
  wallet_address: string;

  @ApiProperty({
    description: 'Type of wallet',
    enum: WalletType,
    example: WalletType.METAMASK,
  })
  wallet_type: WalletType;

  @ApiProperty({
    description: 'Whether this is the primary wallet for the user',
    example: true,
  })
  is_primary: boolean;

  @ApiProperty({
    description: 'Current status of the wallet',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'PENDING', 'FREEZE', 'DEACTIVE'],
  })
  status: string;

  @ApiProperty({
    description: 'Timestamp when the wallet was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'Associated chain information (supports multiple chains)',
    type: [ChainResponseDto],
    isArray: true,
  })
  chains: ChainResponseDto[];
}

/**
 * Response DTO for wallet chain list operations
 */
export class WalletChainsResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  wallet_id: string;

  @ApiProperty({
    description: 'Blockchain wallet address',
    example: '******************************************',
  })
  wallet_address: string;

  @ApiProperty({
    description: 'Associated chain information for this wallet',
    type: [ChainResponseDto],
    isArray: true,
  })
  chains: ChainResponseDto[];

  @ApiProperty({
    description: 'Total number of chains associated with this wallet',
    example: 3,
  })
  total_chains: number;
}

/**
 * Response DTO for wallet list operations (simplified)
 */
export class WalletListResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the wallet',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
  })
  id: string;

  @ApiProperty({
    description: 'Blockchain wallet address',
    example: '******************************************',
  })
  wallet_address: string;

  @ApiProperty({
    description: 'Type of wallet',
    enum: WalletType,
    example: WalletType.METAMASK,
  })
  wallet_type: WalletType;

  @ApiProperty({
    description: 'Whether this is the primary wallet for the user',
    example: true,
  })
  is_primary: boolean;

  @ApiProperty({
    description: 'Current status of the wallet',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'PENDING', 'FREEZE', 'DEACTIVE'],
  })
  status: string;

  @ApiProperty({
    description: 'Chain names for quick reference (comma-separated)',
    example: 'Ethereum Mainnet, Polygon Mainnet',
  })
  chain_names: string;

  @ApiProperty({
    description: 'Timestamp when the wallet was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  created_at: Date;
}
