import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { WalletType, ChainSymbol } from '../../generated/prisma';

describe('WalletService - Update and Delete', () => {
  let service: WalletService;
  let prismaService: PrismaService;

  const mockUser = {
    id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    email: '<EMAIL>',
    status: 'ACTIVE',
  };

  const mockWallet = {
    id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
    userId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    walletAddress: '******************************************',
    walletType: WalletType.METAMASK,
    isPrimary: true,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    chains: [
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d481',
        chainId: '1',
        chainName: 'Ethereum Mainnet',
        symbol: ChainSymbol.ETH,
        walletId: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        createdAt: new Date('2024-01-01'),
      },
    ],
  };

  const mockSecondaryWallet = {
    id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482',
    userId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    walletAddress: '******************************************',
    walletType: WalletType.METAMASK,
    isPrimary: false,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-02'),
    chains: [
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d483',
        chainId: '137',
        chainName: 'Polygon Mainnet',
        symbol: ChainSymbol.MATIC,
        walletId: 'f47ac10b-58cc-4372-a567-0e02b2c3d482',
        createdAt: new Date('2024-01-02'),
      },
    ],
  };

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
    },
    wallet: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WalletService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<WalletService>(WalletService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateWallet', () => {
    const updateDto: UpdateWalletDto = { is_primary: true };

    it('should update wallet successfully when setting as primary', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(mockSecondaryWallet);
      mockPrismaService.wallet.updateMany.mockResolvedValue({ count: 1 });
      mockPrismaService.wallet.update.mockResolvedValue({
        ...mockSecondaryWallet,
        isPrimary: true,
      });

      const result = await service.updateWallet(
        'f47ac10b-58cc-4372-a567-0e02b2c3d482',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        updateDto,
      );

      expect(mockPrismaService.wallet.updateMany).toHaveBeenCalledWith({
        where: {
          userId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          id: { not: 'f47ac10b-58cc-4372-a567-0e02b2c3d482' },
        },
        data: {
          isPrimary: false,
        },
      });

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482' },
        data: { isPrimary: true },
        include: { chains: true },
      });

      expect(result.is_primary).toBe(true);
      expect(result.id).toBe('f47ac10b-58cc-4372-a567-0e02b2c3d482');
    });

    it('should handle unsetting primary wallet and assign to oldest wallet', async () => {
      const unsetPrimaryDto: UpdateWalletDto = { is_primary: false };

      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst
        .mockResolvedValueOnce(mockWallet) // Current wallet (primary)
        .mockResolvedValueOnce(mockSecondaryWallet); // Oldest remaining wallet
      mockPrismaService.wallet.update
        .mockResolvedValueOnce(mockSecondaryWallet) // Update oldest to primary
        .mockResolvedValueOnce({ ...mockWallet, isPrimary: false }); // Update current wallet

      const result = await service.updateWallet(
        'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        unsetPrimaryDto,
      );

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482' },
        data: { isPrimary: true },
      });

      expect(result.is_primary).toBe(false);
    });

    it('should throw NotFoundException when wallet not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(null);

      await expect(
        service.updateWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
          'f47ac10b-58cc-4372-a567-0e02b2c3d479',
          updateDto,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(
        service.updateWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
          updateDto,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid user ID format', async () => {
      await expect(
        service.updateWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'invalid-id',
          updateDto,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteWallet', () => {
    it('should delete non-primary wallet successfully', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(mockSecondaryWallet);
      mockPrismaService.wallet.delete.mockResolvedValue(mockSecondaryWallet);

      const result = await service.deleteWallet(
        'f47ac10b-58cc-4372-a567-0e02b2c3d482',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      expect(mockPrismaService.wallet.delete).toHaveBeenCalledWith({
        where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482' },
      });

      expect(result.message).toBe('Wallet deleted successfully');
    });

    it('should delete primary wallet and assign primary to next wallet', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst
        .mockResolvedValueOnce(mockWallet) // Current wallet (primary)
        .mockResolvedValueOnce(mockSecondaryWallet); // Next wallet to become primary
      mockPrismaService.wallet.update.mockResolvedValue({
        ...mockSecondaryWallet,
        isPrimary: true,
      });
      mockPrismaService.wallet.delete.mockResolvedValue(mockWallet);

      const result = await service.deleteWallet(
        'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      expect(mockPrismaService.wallet.update).toHaveBeenCalledWith({
        where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482' },
        data: { isPrimary: true },
      });

      expect(mockPrismaService.wallet.delete).toHaveBeenCalledWith({
        where: { id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480' },
      });

      expect(result.message).toBe('Wallet deleted successfully');
    });

    it('should throw NotFoundException when wallet not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(null);

      await expect(
        service.deleteWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
          'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(
        service.deleteWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid user ID format', async () => {
      await expect(
        service.deleteWallet(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'invalid-id',
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getWalletChains', () => {
    it('should return wallet chains successfully', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(mockWallet);

      const result = await service.getWalletChains(
        'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      );

      expect(mockPrismaService.wallet.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          userId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        },
        include: {
          chains: {
            orderBy: {
              createdAt: 'asc',
            },
          },
        },
      });

      expect(result).toEqual({
        wallet_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        wallet_address: '******************************************',
        chains: [
          {
            id: 'f47ac10b-58cc-4372-a567-0e02b2c3d481',
            chain_id: '1',
            chain_name: 'Ethereum Mainnet',
            symbol: ChainSymbol.ETH,
            wallet_id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
            created_at: new Date('2024-01-01'),
          },
        ],
        total_chains: 1,
      });
    });

    it('should throw NotFoundException when wallet not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.wallet.findFirst.mockResolvedValue(null);

      await expect(
        service.getWalletChains(
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
          'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(
        service.getWalletChains(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'f47ac10b-58cc-4372-a567-0e02b2c3d999',
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid wallet ID format', async () => {
      await expect(
        service.getWalletChains(
          'invalid-wallet-id',
          'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for invalid user ID format', async () => {
      await expect(
        service.getWalletChains(
          'f47ac10b-58cc-4372-a567-0e02b2c3d480',
          'invalid-user-id',
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
