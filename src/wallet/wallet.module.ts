import { Module, forwardRef } from '@nestjs/common';
import { WalletController } from './wallet.controller';
import { WalletService } from './wallet.service';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

/**
 * Wallet Module
 *
 * Provides wallet management functionality including:
 * - Creating new blockchain wallets for users
 * - Retrieving user wallet information
 * - Managing wallet-chain relationships
 * - Ensuring wallet address uniqueness
 *
 * Dependencies:
 * - PrismaModule: For database operations
 * - AuthModule: For authentication services and guards (with forwardRef to avoid circular dependency)
 *
 * Features:
 * - JWT-based authentication
 * - Comprehensive input validation
 * - Detailed API documentation
 * - Production-ready error handling
 * - Logging and monitoring
 */
@Module({
  imports: [PrismaModule, forwardRef(() => AuthModule)],
  controllers: [WalletController],
  providers: [WalletService],
  exports: [WalletService],
})
export class WalletModule {}
