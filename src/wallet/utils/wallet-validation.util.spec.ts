import { BadRequestException } from '@nestjs/common';
import { WalletValidationUtil } from './wallet-validation.util';

describe('WalletValidationUtil', () => {
  describe('validateUserId', () => {
    it('should accept valid UUIDv4', () => {
      const validUuidV4 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
      expect(() =>
        WalletValidationUtil.validateUserId(validUuidV4),
      ).not.toThrow();
    });

    it('should accept valid UUIDv7', () => {
      const validUuidV7 = '01234567-89ab-7def-8123-456789abcdef';
      expect(() =>
        WalletValidationUtil.validateUserId(validUuidV7),
      ).not.toThrow();
    });

    it('should reject invalid UUID format', () => {
      const invalidUuid = 'invalid-uuid';
      expect(() => WalletValidationUtil.validateUserId(invalidUuid)).toThrow(
        BadRequestException,
      );
    });

    it('should reject empty string', () => {
      expect(() => WalletValidationUtil.validateUserId('')).toThrow(
        BadRequestException,
      );
    });

    it('should reject null/undefined', () => {
      expect(() => WalletValidationUtil.validateUserId(null as any)).toThrow(
        BadRequestException,
      );
      expect(() =>
        WalletValidationUtil.validateUserId(undefined as any),
      ).toThrow(BadRequestException);
    });

    it('should reject UUID with wrong length', () => {
      const shortUuid = 'f47ac10b-58cc-4372-a567-0e02b2c3d47';
      expect(() => WalletValidationUtil.validateUserId(shortUuid)).toThrow(
        BadRequestException,
      );
    });

    it('should reject UUID with invalid characters', () => {
      const invalidCharUuid = 'g47ac10b-58cc-4372-a567-0e02b2c3d479';
      expect(() =>
        WalletValidationUtil.validateUserId(invalidCharUuid),
      ).toThrow(BadRequestException);
    });
  });

  describe('validateWalletId', () => {
    it('should accept valid UUIDv4', () => {
      const validUuidV4 = 'f47ac10b-58cc-4372-a567-0e02b2c3d480';
      expect(() =>
        WalletValidationUtil.validateWalletId(validUuidV4),
      ).not.toThrow();
    });

    it('should accept valid UUIDv7', () => {
      const validUuidV7 = '01234567-89ab-7def-8123-456789abcdef';
      expect(() =>
        WalletValidationUtil.validateWalletId(validUuidV7),
      ).not.toThrow();
    });

    it('should reject invalid UUID format', () => {
      const invalidUuid = 'invalid-wallet-id';
      expect(() => WalletValidationUtil.validateWalletId(invalidUuid)).toThrow(
        BadRequestException,
      );
    });

    it('should reject empty string', () => {
      expect(() => WalletValidationUtil.validateWalletId('')).toThrow(
        BadRequestException,
      );
    });

    it('should reject null/undefined', () => {
      expect(() => WalletValidationUtil.validateWalletId(null as any)).toThrow(
        BadRequestException,
      );
      expect(() =>
        WalletValidationUtil.validateWalletId(undefined as any),
      ).toThrow(BadRequestException);
    });
  });
});
