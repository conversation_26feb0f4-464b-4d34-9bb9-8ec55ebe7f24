import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../prisma/prisma.module';
import { NavManagementModule } from '../backoffice/nav-management/nav-management.module';
// import { TransactionStatusSchedulerService } from './services/transaction-status-scheduler.service';
import { JobMetricsService } from './services/job-metrics.service';
import { JobShutdownService } from './services/job-shutdown.service';
import { IndexPullerService } from './services/indexer-puller-service';

@Module({
  imports: [ScheduleModule.forRoot(), PrismaModule, NavManagementModule],
  providers: [
    // TransactionStatusSchedulerService,
    JobMetricsService,
    JobShutdownService,
    IndexPullerService,
  ],
  exports: [
    // TransactionStatusSchedulerService,
    JobMetricsService,
    JobShutdownService,
    IndexPullerService,
  ],
})
export class JobsModule {}
