import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface JobMetrics {
  jobName: string;
  lastRun: Date | null;
  lastSuccess: Date | null;
  lastError: Date | null;
  totalRuns: number;
  totalSuccess: number;
  totalErrors: number;
  averageDuration: number;
  lastDuration: number | null;
  consecutiveErrors: number;
  isHealthy: boolean;
}

export interface JobRun {
  id?: string;
  jobName: string;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  success: boolean;
  processed: number;
  errors: number;
  errorDetails?: string[];
  instanceId?: string;
}

@Injectable()
export class JobMetricsService {
  private readonly logger = new Logger(JobMetricsService.name);
  private readonly instanceId =
    process.env.FLY_MACHINE_ID || `local-${process.pid}`;

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Record a job run
   */
  async recordJobRun(jobRun: JobRun): Promise<void> {
    try {
      const structuredLog = {
        timestamp: new Date().toISOString(),
        level: jobRun.success ? 'info' : 'error',
        service: 'job-metrics',
        jobName: jobRun.jobName,
        instanceId: this.instanceId,
        duration: jobRun.duration,
        processed: jobRun.processed,
        errors: jobRun.errors,
        success: jobRun.success,
        errorDetails: jobRun.errorDetails,
      };

      // Log structured data
      this.logger.log(JSON.stringify(structuredLog));

      // Store in database if needed (optional)
      // await this.storeJobRunInDatabase(jobRun);
    } catch (error) {
      this.logger.error('Failed to record job run:', error);
    }
  }

  /**
   * Get job metrics
   */
  async getJobMetrics(jobName: string): Promise<JobMetrics> {
    try {
      // In a real implementation, you would query the database
      // For now, we'll return basic metrics from logs
      return {
        jobName,
        lastRun: new Date(),
        lastSuccess: new Date(),
        lastError: null,
        totalRuns: 0,
        totalSuccess: 0,
        totalErrors: 0,
        averageDuration: 0,
        lastDuration: 0,
        consecutiveErrors: 0,
        isHealthy: true,
      };
    } catch (error) {
      this.logger.error(`Failed to get metrics for job ${jobName}:`, error);
      throw error;
    }
  }

  /**
   * Get health status of all jobs
   */
  async getJobsHealthStatus(): Promise<{ [jobName: string]: JobMetrics }> {
    try {
      // In a real implementation, you would query all jobs from database
      return {};
    } catch (error) {
      this.logger.error('Failed to get jobs health status:', error);
      throw error;
    }
  }

  /**
   * Log job performance metrics
   */
  logJobPerformance(
    jobName: string,
    duration: number,
    processed: number,
    errors: number,
    success: boolean,
    errorDetails?: string[],
  ): void {
    const metrics = {
      timestamp: new Date().toISOString(),
      level: success ? 'info' : 'error',
      service: 'job-performance',
      jobName,
      instanceId: this.instanceId,
      duration,
      processed,
      errors,
      success,
      throughput: processed / (duration / 1000), // items per second
      errorRate: errors / Math.max(processed, 1),
      errorDetails: errorDetails?.slice(0, 5), // Limit error details
    };

    this.logger.log(JSON.stringify(metrics));
  }

  /**
   * Log job lifecycle events
   */
  logJobLifecycle(
    jobName: string,
    event: 'started' | 'completed' | 'failed' | 'skipped',
    details?: any,
  ): void {
    const log = {
      timestamp: new Date().toISOString(),
      level: event === 'failed' ? 'error' : 'info',
      service: 'job-lifecycle',
      jobName,
      instanceId: this.instanceId,
      event,
      details,
    };

    this.logger.log(JSON.stringify(log));
  }

  /**
   * Store job run in database (optional implementation)
   */
  private async storeJobRunInDatabase(jobRun: JobRun): Promise<void> {
    // This would require a job_runs table in your database
    // For now, we'll just log it
    this.logger.debug('Job run would be stored in database:', jobRun);
  }

  /**
   * Get instance information
   */
  getInstanceInfo(): {
    instanceId: string;
    region?: string;
    environment: string;
  } {
    return {
      instanceId: this.instanceId,
      region: process.env.FLY_REGION,
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
