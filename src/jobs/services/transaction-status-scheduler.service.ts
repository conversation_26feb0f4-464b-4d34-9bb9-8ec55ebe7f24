import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { PrismaService } from '../../prisma/prisma.service';
import { SafeService } from '../../backoffice/nav-management/safe.service';
import { NavStatus } from '../../../generated/prisma';
import { BaseJobService, JobConfig } from './base-job.service';
import { JobMetricsService } from './job-metrics.service';
import { JobShutdownService } from './job-shutdown.service';

@Injectable()
export class TransactionStatusSchedulerService extends BaseJobService {
  private readonly safeService: SafeService;

  constructor(
    prisma: PrismaService,
    safeService: SafeService,
    metricsService: JobMetricsService,
    shutdownService: JobShutdownService,
  ) {
    const config: JobConfig = {
      name: 'TransactionStatusCheck',
      lockId: 12345, // Unique lock ID for this job
      timeout: 300000, // 5 minutes timeout
      maxRetries: 3,
      retryDelay: 1000, // 1 second base delay
      batchSize: 10, // Process 10 transactions at a time
    };

    super(prisma, config, metricsService, shutdownService);
    this.safeService = safeService;
  }

  @Cron('*/30 * * * * *', {
    name: 'check-transaction-status',
    timeZone: 'UTC',
  })
  async checkTransactionStatuses() {
    return this.executeJob();
  }

  protected async executeJobLogic(): Promise<{
    processed: number;
    errors: number;
    errorDetails: string[];
  }> {
    // Get all transactions that are waiting for approval and have a safe transaction hash
    const waitingTransactions = await this.getWaitingTransactions();

    if (waitingTransactions.length === 0) {
      this.logger.log('✅ No transactions waiting for approval found');
      return { processed: 0, errors: 0, errorDetails: [] };
    }

    this.logger.log(
      `📋 Found ${waitingTransactions.length} transactions to check`,
    );

    // Process transactions in batches
    return this.processInBatches(waitingTransactions, async (batch) => {
      let processedCount = 0;
      let errorCount = 0;
      const errorDetails: string[] = [];

      for (const transaction of batch) {
        try {
          await this.processTransaction(transaction);
          processedCount++;
        } catch (error) {
          this.logger.error(
            `❌ Error processing transaction ${transaction.id}:`,
            error.message,
          );
          errorCount++;
          errorDetails.push(`Transaction ${transaction.id}: ${error.message}`);
        }
      }

      return { processed: processedCount, errors: errorCount, errorDetails };
    });
  }

  private async getWaitingTransactions() {
    return await this.prisma.navManagement.findMany({
      where: {
        status: NavStatus.WAITING_FOR_APPROVAL,
      },
      select: {
        id: true,
        status: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: 'asc',
      },
    });
  }

  private async processTransaction(transaction: any) {
    this.logger.debug(
      `🔍 Checking transaction ${transaction.id} with hash ${transaction.safeTxHash}`,
    );

    // Use retry mechanism for SAFE API calls
    const safeTransaction = await this.retryWithBackoff(
      () => this.safeService.getTransaction(transaction.safeTxHash),
      `SAFE API call for transaction ${transaction.id}`,
      2, // Max 2 retries for external API calls
    );

    if (!safeTransaction) {
      this.logger.warn(`⚠️ Transaction ${transaction.id} not found in SAFE`);
      await this.updateTransactionStatus(
        transaction.id,
        NavStatus.POSTING_ERROR,
        'Transaction not found in SAFE',
      );
      return;
    }

    // Determine new status based on SAFE transaction status
    const newStatus = this.determineTransactionStatus(safeTransaction);

    if (newStatus !== transaction.status) {
      await this.updateTransactionStatus(
        transaction.id,
        newStatus,
        `Status updated from SAFE: ${this.getTransactionStatus(safeTransaction)}`,
      );
      this.logger.log(
        `✅ Transaction ${transaction.id} status updated: ${transaction.status} → ${newStatus}`,
      );
    } else {
      this.logger.debug(
        `ℹ️ Transaction ${transaction.id} status unchanged: ${transaction.status}`,
      );
    }
  }

  private getTransactionStatus(safeTransaction: any): string {
    if (safeTransaction.isExecuted) {
      if (safeTransaction.isSuccessful === true) {
        return 'EXECUTED_SUCCESSFULLY';
      } else if (safeTransaction.isSuccessful === false) {
        return 'EXECUTED_FAILED';
      } else {
        return 'EXECUTED_UNKNOWN_SUCCESS';
      }
    } else {
      return 'NOT_EXECUTED';
    }
  }

  private determineTransactionStatus(safeTransaction: any): NavStatus {
    if (safeTransaction.isExecuted) {
      if (safeTransaction.isSuccessful === true) {
        return NavStatus.POSTED;
      } else {
        return NavStatus.POSTING_ERROR;
      }
    } else {
      return NavStatus.WAITING_FOR_APPROVAL;
    }
  }

  private async updateTransactionStatus(
    transactionId: string,
    newStatus: NavStatus,
    notes?: string,
  ): Promise<void> {
    try {
      await this.prisma.navManagement.update({
        where: { id: transactionId },
        data: {
          status: newStatus,
          notes: notes
            ? `${notes} (Updated at ${new Date().toISOString()})`
            : undefined,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(
        `❌ Failed to update transaction ${transactionId} status:`,
        error,
      );
      throw error;
    }
  }

  // Manual trigger method for testing or emergency situations
  async triggerManualCheck() {
    const result = await this.triggerManual();
    return {
      message: `Manual check completed in ${result.duration}ms`,
      processed: result.processed,
      errors: result.errors,
      success: result.success,
    };
  }
}
