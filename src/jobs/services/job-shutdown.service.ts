import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { BaseJobService } from './base-job.service';

@Injectable()
export class JobShutdownService implements OnModuleDestroy {
  private readonly logger = new Logger(JobShutdownService.name);
  private readonly runningJobs = new Set<BaseJobService>();
  private isShuttingDown = false;

  /**
   * Register a job service for graceful shutdown
   */
  registerJob(jobService: BaseJobService): void {
    this.runningJobs.add(jobService);
    this.logger.log(
      `📝 Registered job ${jobService.getConfig().name} for graceful shutdown`,
    );
  }

  /**
   * Unregister a job service
   */
  unregisterJob(jobService: BaseJobService): void {
    this.runningJobs.delete(jobService);
    this.logger.log(`📝 Unregistered job ${jobService.getConfig().name}`);
  }

  /**
   * Handle graceful shutdown
   */
  async onModuleDestroy(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    this.logger.log('🛑 Starting graceful shutdown of jobs...');

    const shutdownPromises = Array.from(this.runningJobs).map(async (job) => {
      const jobName = job.getConfig().name;
      try {
        this.logger.log(`🔄 Gracefully shutting down job: ${jobName}`);

        // Give each job a maximum of 30 seconds to complete
        const shutdownPromise = this.gracefulShutdownJob(job);
        const timeoutPromise = new Promise<void>((_, reject) => {
          setTimeout(
            () => reject(new Error(`Job ${jobName} shutdown timeout`)),
            30000,
          );
        });

        await Promise.race([shutdownPromise, timeoutPromise]);
        this.logger.log(`✅ Job ${jobName} shut down gracefully`);
      } catch (error) {
        this.logger.error(`❌ Error shutting down job ${jobName}:`, error);
      }
    });

    try {
      await Promise.allSettled(shutdownPromises);
      this.logger.log('✅ All jobs shut down gracefully');
    } catch (error) {
      this.logger.error('❌ Error during job shutdown:', error);
    }
  }

  /**
   * Gracefully shutdown a single job
   */
  private async gracefulShutdownJob(job: BaseJobService): Promise<void> {
    const jobName = job.getConfig().name;

    // If the job has a shutdown method, call it
    if (typeof (job as any).gracefulShutdown === 'function') {
      this.logger.log(`🔄 Calling gracefulShutdown for job: ${jobName}`);
      await (job as any).gracefulShutdown();
    }

    // Wait a bit for any ongoing operations to complete
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  /**
   * Get status of running jobs
   */
  getRunningJobsStatus(): { jobName: string; isRunning: boolean }[] {
    return Array.from(this.runningJobs).map((job) => ({
      jobName: job.getConfig().name,
      isRunning: !this.isShuttingDown,
    }));
  }

  /**
   * Check if shutdown is in progress
   */
  isShutdownInProgress(): boolean {
    return this.isShuttingDown;
  }
}
