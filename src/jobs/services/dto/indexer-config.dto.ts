import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class IndexerConfigDto {
  @ApiProperty({
    description: 'Chain ID for the target blockchain',
    example: 84532,
  })
  @IsNumber()
  @Min(1)
  chainId: number;

  @ApiProperty({
    description: 'Number of events to process per batch',
    example: 1000,
    default: 1000,
  })
  @IsNumber()
  @Min(1)
  @Max(10000)
  @IsOptional()
  batchSize?: number;

  @ApiProperty({
    description: 'Number of confirmations required before processing',
    example: 5,
    default: 5,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  confirmations?: number;

  @ApiProperty({
    description: 'PostgreSQL connection string for the Ponder indexer database',
  })
  @IsString()
  ponderPgUrl: string;

  @ApiProperty({
    description: 'RPC endpoint for the blockchain',
  })
  @IsString()
  rpcUrl: string;
}
