import { Injectable } from '@nestjs/common';
import { Pool } from 'pg';
import { createPublicClient, http } from 'viem';
import { sepolia } from 'viem/chains';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseJobService, JobConfig } from './base-job.service';
import { JobMetricsService } from './job-metrics.service';
import { JobShutdownService } from './job-shutdown.service';
import {
  IndexerEvent,
  EventSource,
  IndexerProcessingStats,
  EventSourceStats,
} from './interfaces/indexer-event.interface';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class IndexPullerService extends BaseJobService {
  private ponderPool = new Pool({
    connectionString: process.env.PONDER_PG_URL!,
  });

  private client = createPublicClient({
    chain: sepolia,
    transport: http(process.env.RPC_URL!),
  });

  private readonly BATCH = Number(process.env.BATCH_SIZE ?? 1000);
  private readonly CONF = Number(process.env.CONFIRMATIONS ?? 5);
  private readonly CHAIN_ID = Number(process.env.CHAIN_ID!);

  private readonly eventSources: EventSource[] = [
    {
      name: `Gateway:RequestedDeposit`,
      table: '"public_"."RequestedDeposit"',
      eventType: 'DEPOSIT',
    },
    {
      name: `Gateway:RequestedRedemption`,
      table: '"public_"."RequestedRedemption"',
      eventType: 'REDEMPTION',
    },
  ];

  constructor(
    prisma: PrismaService,
    metricsService: JobMetricsService,
    shutdownService: JobShutdownService,
  ) {
    const config: JobConfig = {
      name: 'IndexerPuller',
      lockId: 1001,
      timeout: 300000,
      maxRetries: 3,
      retryDelay: 5000,
      batchSize: Number(process.env.BATCH_SIZE ?? 1000),
    };

    super(prisma, config, metricsService, shutdownService);
  }

  @Cron('*/30 * * * * *', {
    name: 'indexer-puller',
    timeZone: 'UTC',
  })
  async pullIndexerEvents() {
    return this.executeJob();
  }

  async onModuleDestroy() {
    this.logger.log('Shutting down indexer puller service');
    await this.ponderPool.end();
  }

  protected async executeJobLogic(): Promise<{
    processed: number;
    errors: number;
    errorDetails: string[];
  }> {
    let totalProcessed = 0;
    let totalErrors = 0;
    const errorDetails: string[] = [];

    try {
      const head = await this.client.getBlockNumber();
      this.logger.debug(`Current block head: ${head}`);

      for (const source of this.eventSources) {
        try {
          const result = await this.processEventSource(source, head);
          totalProcessed += result.processed;
          totalErrors += result.errors;
          errorDetails.push(...result.errorDetails);
        } catch (error) {
          this.logger.error(
            `Error processing event source ${source.name}:`,
            error,
          );
          totalErrors += 1;
          errorDetails.push(`Event source ${source.name}: ${error.message}`);
        }
      }

      this.logger.log('Indexer puller job completed successfully');
    } catch (error) {
      this.logger.error('Error in indexer puller job:', error);
      totalErrors += 1;
      errorDetails.push(`Main job error: ${error.message}`);
    }

    return {
      processed: totalProcessed,
      errors: totalErrors,
      errorDetails,
    };
  }

  async tick() {
    const result = await this.executeJob();
    return result;
  }

  private async processEventSource(
    source: EventSource,
    head: bigint,
  ): Promise<{ processed: number; errors: number; errorDetails: string[] }> {
    this.logger.debug(`Processing event source: ${source.name}`);

    let processed = 0;
    let errors = 0;
    const errorDetails: string[] = [];

    try {
      const cp = await this.prisma.indexerCheckpoint.upsert({
        where: { source: source.name },
        create: {
          source: source.name,
          lastBlock: BigInt(0),
          lastLogIndex: 0,
        },
        update: {},
      });

      const { rows } = await this.ponderPool.query(
        `
        SELECT *
        FROM ${source.table}
        WHERE (block_number > $1 OR (block_number = $1 AND log_index > $2))
          AND ($3::bigint - block_number) >= $4
        ORDER BY block_number ASC, log_index ASC
        LIMIT $5
        `,
        [
          cp.lastBlock.toString(),
          cp.lastLogIndex,
          head.toString(),
          this.CONF,
          this.BATCH,
        ],
      );

      if (rows.length === 0) {
        this.logger.debug(`No new events found for source: ${source.name}`);
        return { processed: 0, errors: 0, errorDetails: [] };
      }

      this.logger.log(
        `Processing ${rows.length} events for source: ${source.name}`,
      );

      await this.prisma.$transaction(async (tx) => {
        for (const event of rows) {
          try {
            await this.processEvent(event, source, tx);
            processed++;
          } catch (error) {
            this.logger.error(
              `Error processing event ${event.request_id}:`,
              error,
            );
            errors++;
            errorDetails.push(`Event ${event.request_id}: ${error.message}`);
          }
        }

        // Update checkpoint to the last processed event
        const lastEvent = rows[rows.length - 1];
        await tx.indexerCheckpoint.update({
          where: { source: source.name },
          data: {
            lastBlock: BigInt(lastEvent.block_number),
            lastLogIndex: Number(lastEvent.log_index),
          },
        });
      });

      this.logger.log(
        `Successfully processed ${processed} events for source: ${source.name}`,
      );
    } catch (error) {
      this.logger.error(`Error processing event source ${source.name}`, error);
      errors++;
      errorDetails.push(`Event source ${source.name}: ${error.message}`);
    }

    return { processed, errors, errorDetails };
  }

  private async processEvent(
    event: IndexerEvent,
    source: EventSource,
    tx: any,
  ) {
    const chainIdStr = String(this.CHAIN_ID);

    if (event.removed) {
      // Handle removed events - mark as REMOVED
      await tx.subscribe.updateMany({
        where: {
          chainId: chainIdStr,
          requestId: event.request_id,
        },
        data: {
          onchainStatus: 'REMOVED',
          updatedAt: new Date(),
        },
      });
      this.logger.debug(`Marked event as REMOVED: ${event.request_id}`);
      return;
    }

    const existingSubscribe = await tx.subscribe.findFirst({
      where: { txHash: event.tx_hash },
    });

    if (existingSubscribe) {
      await tx.subscribe.update({
        where: { id: existingSubscribe.id },
        data: {
          requestId: event.request_id,
          logIndex: Number(event.log_index),
          blockNumber: BigInt(event.block_number),
          blockHash: event.block_hash,
          checkoutStatus: 'READY_TO_FULFILL',
          updatedAt: new Date(),
        },
      });
      this.logger.debug(
        `Updated existing subscribe record: ${existingSubscribe.id}`,
      );
    } else {
      // Create new record if no existing record found
      //   await tx.$executeRawUnsafe(
      //     `
      //     INSERT INTO "Subscribe" (
      //       id, product_id, wallet_id, chain_id, payment_provider, payment_status,
      //       checkout_status, subscribe_status, amount, expected_price, total_price, currencies,
      //       created_at, updated_at, user_id, status_buy, date_cutoff,
      //       request_id, tx_hash, log_index, block_number, block_hash, contract_address,
      //       onchain_amount, onchain_status
      //     )
      //     VALUES (
      //       gen_random_uuid(), '', '', $1, 'onchain', 'N/A',
      //       'SUBMITTED', 'CONFIRMED_EVENT', 0, 0, 0, '',
      //       NOW(), NOW(), '', 'IN_PROCESS', NOW(),
      //       $2, $3, $4, $5, $6, $7,
      //       $8, 'CONFIRMED_EVENT'
      //     )
      //     ON CONFLICT ("chain_id","request_id") DO UPDATE SET
      //       tx_hash = COALESCE("Subscribe".tx_hash, EXCLUDED.tx_hash),
      //       log_index = EXCLUDED.log_index,
      //       block_number = EXCLUDED.block_number,
      //       block_hash = EXCLUDED.block_hash,
      //       contract_address = EXCLUDED.contract_address,
      //       onchain_amount = EXCLUDED.onchain_amount,
      //       onchain_status = EXCLUDED.onchain_status,
      //       subscribe_status = 'CONFIRMED_EVENT',
      //       updated_at = NOW()
      //     `,
      //     chainIdStr,
      //     event.request_id,
      //     event.tx_hash,
      //     Number(event.log_index),
      //     BigInt(event.block_number),
      //     event.block_hash,
      //     event.contract_addr.toLowerCase(),
      //     event.amount_raw,
      //   );
      //   this.logger.debug(
      //     `Created new subscribe record for request: ${event.request_id}`,
      //   );
      this.logger.debug(
        `Not Found subscribe record for request: ${event.request_id}`,
      );
    }
  }

  async getProcessingStats(): Promise<IndexerProcessingStats> {
    const stats: EventSourceStats[] = await Promise.all(
      this.eventSources.map(async (source) => {
        const checkpoint = await this.prisma.indexerCheckpoint.findUnique({
          where: { source: source.name },
        });

        return {
          source: source.name,
          eventType: source.eventType,
          lastBlock: checkpoint?.lastBlock?.toString() || '0',
          lastLogIndex: checkpoint?.lastLogIndex || 0,
          updatedAt: checkpoint?.updatedAt,
        };
      }),
    );

    return {
      chainId: this.CHAIN_ID,
      batchSize: this.BATCH,
      confirmations: this.CONF,
      sources: stats,
    };
  }

  async resetCheckpoint(sourceName: string) {
    this.logger.warn(`Resetting checkpoint for source: ${sourceName}`);

    await this.prisma.indexerCheckpoint.upsert({
      where: { source: sourceName },
      create: { source: sourceName, lastBlock: BigInt(0), lastLogIndex: 0 },
      update: { lastBlock: BigInt(0), lastLogIndex: 0 },
    });

    this.logger.log(`Checkpoint reset for source: ${sourceName}`);
  }
}
