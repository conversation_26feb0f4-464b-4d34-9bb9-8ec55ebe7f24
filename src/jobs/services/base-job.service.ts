import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { JobMetricsService } from './job-metrics.service';
import { JobShutdownService } from './job-shutdown.service';

export interface JobConfig {
  name: string;
  lockId: number;
  timeout: number; // in milliseconds
  maxRetries: number;
  retryDelay: number; // in milliseconds
  batchSize: number;
}

export interface JobResult {
  success: boolean;
  processed: number;
  errors: number;
  duration: number;
  errorDetails?: string[];
}

@Injectable()
export abstract class BaseJobService {
  protected readonly logger: Logger;
  protected readonly prisma: PrismaService;
  protected readonly config: JobConfig;

  private isShuttingDown = false;

  constructor(
    prisma: PrismaService,
    config: JobConfig,
    private readonly metricsService?: JobMetricsService,
    private readonly shutdownService?: JobShutdownService,
  ) {
    this.prisma = prisma;
    this.config = config;
    this.logger = new Logger(`${config.name}Job`);

    // Register for graceful shutdown
    this.shutdownService?.registerJob(this);
  }

  /**
   * Main job execution method with all safety features
   */
  async executeJob(): Promise<JobResult> {
    const startTime = Date.now();
    const result: JobResult = {
      success: false,
      processed: 0,
      errors: 0,
      duration: 0,
      errorDetails: [],
    };

    // Check if jobs are enabled
    if (!this.isJobsEnabled()) {
      this.logger.log(
        '⏭️ Jobs are disabled via ENABLE_JOBS flag, skipping execution',
      );
      this.metricsService?.logJobLifecycle(this.config.name, 'skipped', {
        reason: 'ENABLE_JOBS=false',
      });
      return { ...result, success: true, duration: Date.now() - startTime };
    }

    // Check if shutdown is in progress
    if (this.isShuttingDown || this.shutdownService?.isShutdownInProgress()) {
      this.logger.log('⏭️ Shutdown in progress, skipping job execution');
      this.metricsService?.logJobLifecycle(this.config.name, 'skipped', {
        reason: 'shutdown_in_progress',
      });
      return { ...result, success: true, duration: Date.now() - startTime };
    }

    this.logger.log(`🔄 Starting ${this.config.name} job...`);
    this.metricsService?.logJobLifecycle(this.config.name, 'started');

    try {
      // Acquire distributed lock
      const lockAcquired = await this.acquireAdvisoryLock();
      if (!lockAcquired) {
        this.logger.log(
          `⏭️ Another worker is already processing ${this.config.name}, skipping this run`,
        );
        return { ...result, success: true, duration: Date.now() - startTime };
      }

      this.logger.log(
        `🔒 Advisory lock acquired for ${this.config.name}, processing...`,
      );

      // Execute the actual job with timeout
      const jobResult = await this.executeWithTimeout();

      result.success = true;
      result.processed = jobResult.processed;
      result.errors = jobResult.errors;
      result.errorDetails = jobResult.errorDetails;

      const duration = Date.now() - startTime;
      result.duration = duration;

      this.logger.log(
        `✅ ${this.config.name} completed in ${duration}ms. ` +
          `Processed: ${result.processed}, Success: ${result.processed - result.errors}, Errors: ${result.errors}`,
      );

      // Log performance metrics
      this.metricsService?.logJobPerformance(
        this.config.name,
        duration,
        result.processed,
        result.errors,
        result.success,
        result.errorDetails,
      );

      this.metricsService?.logJobLifecycle(this.config.name, 'completed', {
        duration,
        processed: result.processed,
        errors: result.errors,
      });
    } catch (error) {
      result.duration = Date.now() - startTime;
      result.errorDetails = [error.message];
      this.logger.error(`❌ Error in ${this.config.name} job:`, error);

      this.metricsService?.logJobLifecycle(this.config.name, 'failed', {
        error: error.message,
        duration: result.duration,
      });
    } finally {
      // Always release the advisory lock
      await this.releaseAdvisoryLock();
      this.logger.log(`🔓 Advisory lock released for ${this.config.name}`);
    }

    return result;
  }

  /**
   * Execute job with timeout protection
   */
  private async executeWithTimeout(): Promise<{
    processed: number;
    errors: number;
    errorDetails: string[];
  }> {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(
          new Error(
            `Job ${this.config.name} timed out after ${this.config.timeout}ms`,
          ),
        );
      }, this.config.timeout);

      try {
        const result = await this.executeJobLogic();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Abstract method to be implemented by specific job services
   */
  protected abstract executeJobLogic(): Promise<{
    processed: number;
    errors: number;
    errorDetails: string[];
  }>;

  /**
   * Check if jobs are enabled via environment variable
   */
  private isJobsEnabled(): boolean {
    const enableJobs = process.env.ENABLE_JOBS;
    return enableJobs === 'true' || enableJobs === '1';
  }

  /**
   * Acquire PostgreSQL advisory lock
   */
  private async acquireAdvisoryLock(): Promise<boolean> {
    try {
      const result = await this.prisma.$queryRaw`
        SELECT pg_try_advisory_lock(${this.config.lockId}) as pg_try_advisory_lock
      `;
      return result[0]?.pg_try_advisory_lock || false;
    } catch (error) {
      this.logger.error(
        `❌ Failed to acquire advisory lock for ${this.config.name}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Release PostgreSQL advisory lock
   */
  private async releaseAdvisoryLock(): Promise<void> {
    try {
      await this.prisma.$queryRaw`
        SELECT pg_advisory_unlock(${this.config.lockId})
      `;
    } catch (error) {
      this.logger.error(
        `❌ Failed to release advisory lock for ${this.config.name}:`,
        error,
      );
    }
  }

  /**
   * Process items in batches to avoid memory issues
   */
  protected async processInBatches<T>(
    items: T[],
    processor: (
      batch: T[],
    ) => Promise<{ processed: number; errors: number; errorDetails: string[] }>,
  ): Promise<{ processed: number; errors: number; errorDetails: string[] }> {
    let totalProcessed = 0;
    let totalErrors = 0;
    const allErrorDetails: string[] = [];

    for (let i = 0; i < items.length; i += this.config.batchSize) {
      const batch = items.slice(i, i + this.config.batchSize);
      this.logger.log(
        `📦 Processing batch ${Math.floor(i / this.config.batchSize) + 1}/${Math.ceil(items.length / this.config.batchSize)} (${batch.length} items)`,
      );

      try {
        const batchResult = await processor(batch);
        totalProcessed += batchResult.processed;
        totalErrors += batchResult.errors;
        allErrorDetails.push(...(batchResult.errorDetails || []));

        // Small delay between batches to prevent overwhelming the system
        if (i + this.config.batchSize < items.length) {
          await this.delay(100);
        }
      } catch (error) {
        this.logger.error(`❌ Error processing batch:`, error);
        totalErrors += batch.length;
        allErrorDetails.push(`Batch error: ${error.message}`);
      }
    }

    return {
      processed: totalProcessed,
      errors: totalErrors,
      errorDetails: allErrorDetails,
    };
  }

  /**
   * Retry mechanism with exponential backoff
   */
  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    context: string,
    maxRetries: number = this.config.maxRetries,
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.logger.warn(
          `⚠️ ${context} failed (attempt ${attempt}/${maxRetries}): ${error.message}`,
        );

        if (attempt < maxRetries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
          this.logger.log(`⏳ Retrying ${context} in ${delay}ms...`);
          await this.delay(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * Utility method for delays
   */
  protected delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get job configuration
   */
  getConfig(): JobConfig {
    return { ...this.config };
  }

  /**
   * Manual trigger for testing or emergency situations
   */
  async triggerManual(): Promise<JobResult> {
    this.logger.log(`🔧 Manual ${this.config.name} triggered`);
    return this.executeJob();
  }

  /**
   * Graceful shutdown method
   */
  async gracefulShutdown(): Promise<void> {
    this.logger.log(`🛑 Graceful shutdown initiated for ${this.config.name}`);
    this.isShuttingDown = true;

    // Wait for any ongoing operations to complete
    await this.delay(2000);

    // Unregister from shutdown service
    this.shutdownService?.unregisterJob(this);

    this.logger.log(`✅ Graceful shutdown completed for ${this.config.name}`);
  }

  /**
   * Check if job is shutting down
   */
  protected isJobShuttingDown(): boolean {
    return this.isShuttingDown;
  }
}
