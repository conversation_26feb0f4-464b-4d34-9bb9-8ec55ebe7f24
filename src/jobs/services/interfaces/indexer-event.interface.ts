export interface IndexerEvent {
  request_id: string;
  tx_hash: string;
  log_index: number;
  block_number: bigint;
  block_hash: string;
  contract_addr: string;
  amount_raw: string;
  removed: boolean;
  user: string;
  amount: string;
  created_at: Date;
}

export interface EventSource {
  name: string;
  table: string;
  eventType: 'DEPOSIT' | 'REDEMPTION';
}

export interface IndexerProcessingStats {
  chainId: number;
  batchSize: number;
  confirmations: number;
  sources: EventSourceStats[];
}

export interface EventSourceStats {
  source: string;
  eventType: 'DEPOSIT' | 'REDEMPTION';
  lastBlock: string;
  lastLogIndex: number;
  updatedAt: Date | null;
}
