import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard response structure for all backoffice endpoints
 */
export class BaseBackofficeResponseDto<T = any> {
  @ApiProperty({
    description: 'HTTP status code',
    example: 200,
  })
  code: number;

  @ApiProperty({
    description: 'Response status',
    example: 'success',
    enum: ['success', 'error', 'failed'],
  })
  status: 'success' | 'error' | 'failed';

  @ApiProperty({
    description: 'Response message',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Response data',
  })
  data: T;

  @ApiProperty({
    description: 'Response timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  constructor(
    code: number,
    status: 'success' | 'error' | 'failed',
    message: string,
    data: T,
  ) {
    this.code = code;
    this.status = status;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
  }

  static success<T>(
    data: T,
    message: string = 'Operation completed successfully',
  ): BaseBackofficeResponseDto<T> {
    return new BaseBackofficeResponseDto(200, 'success', message, data);
  }

  static error<T>(
    message: string,
    code: number = 500,
    data: T = null,
  ): BaseBackofficeResponseDto<T> {
    return new BaseBackofficeResponseDto(code, 'error', message, data);
  }

  static failed<T>(
    message: string,
    code: number = 400,
    data: T = null,
  ): BaseBackofficeResponseDto<T> {
    return new BaseBackofficeResponseDto(code, 'failed', message, data);
  }
}
