import {
  Controller,
  UsePipes,
  ValidationPipe,
  UseFilters,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import { HttpExceptionFilter } from '../../common/filters/http-exception.filter';

/**
 * Base controller for all backoffice endpoints
 * Provides common functionality like validation, error handling, and serialization
 */
@Controller()
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
@UseFilters(HttpExceptionFilter)
@UseInterceptors(ClassSerializerInterceptor)
export abstract class BaseBackofficeController {}
