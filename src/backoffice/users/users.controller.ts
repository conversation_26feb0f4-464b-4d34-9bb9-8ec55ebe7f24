import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Query,
  Body,
  Param,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { UsersListResponseDto } from './dto/users-list-response.dto';
import { UsersQueryDto } from './dto/users-query.dto';
import { CreateBackofficeUserDto } from './dto/create-backoffice-user.dto';
import { UpdateBackofficeUserDto } from './dto/update-backoffice-user.dto';
import { BackofficeUserResponseDto } from './dto/backoffice-user-response.dto';
import { BaseBackofficeController } from '../common/base-backoffice.controller';

@ApiTags('Backoffice - Users')
@Controller('backoffice/users')
export class UsersController extends BaseBackofficeController {
  constructor(private readonly usersService: UsersService) {
    super();
  }

  @Get()
  @ApiOperation({
    summary: 'Get all users',
    description:
      'Retrieve a paginated list of all users in the system for backoffice administration.',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/UserResponseDto' },
                },
                meta: {
                  type: 'object',
                  properties: {
                    page: { type: 'number', example: 1 },
                    limit: { type: 'number', example: 10 },
                    total: { type: 'number', example: 25 },
                    totalPages: { type: 'number', example: 3 },
                    hasNext: { type: 'boolean', example: true },
                    hasPrev: { type: 'boolean', example: false },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: [
      'id',
      'email',
      'username',
      'type',
      'businessType',
      'status',
      'createdAt',
      'updatedAt',
    ],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  async findAll(
    @Query() query: UsersQueryDto,
  ): Promise<BaseResponseDto<UsersListResponseDto>> {
    return await this.usersService.findAll(query);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new user',
    description:
      'Create a new user in the backoffice with all required fields. Status is automatically set to ACTIVE.',
  })
  @ApiBody({
    type: CreateBackofficeUserDto,
    description: 'User creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/BackofficeUserResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - user with email or username already exists',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async create(
    @Body() createUserDto: CreateBackofficeUserDto,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    return await this.usersService.create(createUserDto);
  }

  @Get('by-email')
  @ApiOperation({
    summary: 'Get user by email',
    description:
      'Retrieve a user by their email address. Password field is excluded from the response.',
  })
  @ApiQuery({
    name: 'email',
    required: true,
    type: String,
    description: 'Email address of the user to retrieve',
    example: '<EMAIL>',
  })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/BackofficeUserResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid email format',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findByEmail(
    @Query('email') email: string,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    return await this.usersService.findByEmail(email);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update user by ID',
    description:
      'Update user information by ID. Only provided fields will be updated. Password will be hashed if provided.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: String,
    description: 'User ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiBody({
    type: UpdateBackofficeUserDto,
    description: 'User update data (all fields are optional)',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/BackofficeUserResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed or invalid user ID',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - email or username already exists',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateBackofficeUserDto,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    return await this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete user by ID',
    description:
      'Permanently delete a user by ID. This is a hard delete operation and cannot be undone.',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: String,
    description: 'User ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'User deleted successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'User deleted successfully',
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid user ID',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async delete(
    @Param('id') id: string,
  ): Promise<BaseResponseDto<{ message: string }>> {
    return await this.usersService.delete(id);
  }
}
