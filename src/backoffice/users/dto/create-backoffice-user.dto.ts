import {
  IsEmail,
  <PERSON><PERSON>num,
  Is<PERSON><PERSON>al,
  IsString,
  Matches,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum UserType {
  CONSUMER = 'CONSUMER',
  BUSINESS = 'BUSINESS',
}

export enum BusinessType {
  RETAIL = 'RETAIL',
  MERCHANT = 'MERCHANT',
}

export class CreateBackofficeUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the user',
  })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({
    example: 'johndoe123',
    description: 'Unique username for the user',
  })
  @IsString({ message: 'Username must be a string' })
  @IsNotEmpty({ message: 'Username is required' })
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(50, { message: 'Userna<PERSON> must not exceed 50 characters' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Username can only contain letters, numbers, and underscores',
  })
  username: string;

  @ApiProperty({
    example: 'SecurePass123!',
    description:
      'Password with minimum 8 characters, including uppercase, lowercase, and number',
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*/, {
    message:
      'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  })
  password: string;

  @ApiProperty({
    enum: UserType,
    example: UserType.CONSUMER,
    description: 'Type of the user account',
  })
  @IsEnum(UserType, { message: 'Type must be either CONSUMER or BUSINESS' })
  @IsNotEmpty({ message: 'User type is required' })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    example: BusinessType.RETAIL,
    description: 'Business type (required for BUSINESS users)',
  })
  @IsOptional()
  @IsEnum(BusinessType, {
    message: 'Business type must be either RETAIL or MERCHANT',
  })
  businessType?: BusinessType;

  @ApiProperty({
    example: 'John',
    description: 'First name of the user',
  })
  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s'-]+$/, {
    message:
      'First name can only contain letters, spaces, hyphens, and apostrophes',
  })
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name of the user',
  })
  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s'-]+$/, {
    message:
      'Last name can only contain letters, spaces, hyphens, and apostrophes',
  })
  lastName: string;
}
