import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, <PERSON>N<PERSON><PERSON>, <PERSON>, Max, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export class UsersQueryDto {
  @ApiProperty({
    example: 1,
    description: 'Page number (starts from 1)',
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Page must be a number' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit must be a number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    example: 'createdAt',
    description:
      'Field to sort by (id, email, username, type, businessType, status, createdAt, updatedAt)',
    required: false,
    enum: [
      'id',
      'email',
      'username',
      'type',
      'businessType',
      'status',
      'createdAt',
      'updatedAt',
    ],
  })
  @IsOptional()
  @IsEnum(
    [
      'id',
      'email',
      'username',
      'type',
      'businessType',
      'status',
      'createdAt',
      'updatedAt',
    ],
    {
      message:
        'sortBy must be one of: id, email, username, type, businessType, status, createdAt, updatedAt',
    },
  )
  sortBy?:
    | 'id'
    | 'email'
    | 'username'
    | 'type'
    | 'businessType'
    | 'status'
    | 'createdAt'
    | 'updatedAt' = 'createdAt';

  @ApiProperty({
    example: 'desc',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'], { message: 'sortOrder must be either asc or desc' })
  sortOrder?: 'asc' | 'desc' = 'desc';
}
