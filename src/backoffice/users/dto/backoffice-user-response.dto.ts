import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

export enum UserType {
  CONSUMER = 'CONSUMER',
  BUSINESS = 'BUSINESS',
}

export enum BusinessType {
  RETAIL = 'RETAIL',
  MERCHANT = 'MERCHANT',
}

export enum Status {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  FREEZE = 'FREEZE',
  DEACTIVE = 'DEACTIVE',
}

/**
 * Detailed user response DTO for backoffice operations
 * Includes all user fields except password for comprehensive user management
 */
export class BackofficeUserResponseDto {
  @ApiProperty({ 
    description: 'Unique identifier for the user',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  @ApiProperty({ 
    description: 'First name of the user',
    example: 'John'
  })
  firstName: string;

  @ApiProperty({ 
    description: 'Last name of the user',
    example: 'Doe'
  })
  lastName: string;

  @ApiProperty({ 
    description: 'Email address of the user',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({ 
    description: 'Username of the user',
    example: 'johndoe123'
  })
  username: string;

  @ApiProperty({ 
    enum: UserType, 
    description: 'Type of the user',
    example: UserType.CONSUMER
  })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    description: 'Business type for business users',
    example: BusinessType.RETAIL
  })
  businessType?: BusinessType;

  @ApiProperty({ 
    enum: Status, 
    description: 'Current status of the user',
    example: Status.ACTIVE
  })
  status: Status;

  @ApiProperty({ 
    description: 'Account creation timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  createdAt: Date;

  @ApiProperty({ 
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Applicant ID for KYC/KYB processes',
    example: 'kyc_123456789',
    required: false,
  })
  applicantId?: string;

  @ApiProperty({
    description: 'Indicates whether the user has completed the initial setup/onboarding process',
    example: false,
    default: false,
  })
  isInitiated: boolean;

  @ApiProperty({
    description: 'Google OAuth ID if linked',
    example: '1234567890123456789',
    required: false,
  })
  googleId?: string;

  @ApiProperty({
    description: 'OAuth provider name',
    example: 'google',
    required: false,
  })
  oauthProvider?: string;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2024-01-15T10:30:00.000Z',
    required: false,
  })
  lastLoginAt?: Date;

  @ApiProperty({
    description: 'Token version for security purposes',
    example: 1,
  })
  tokenVersion: number;

  // Exclude password from response for security
  @Exclude()
  password: string;

  // Exclude OAuth access token from response for security
  @Exclude()
  oauthAccessToken?: string;

  constructor(partial: Partial<BackofficeUserResponseDto>) {
    Object.assign(this, partial);
  }
}
