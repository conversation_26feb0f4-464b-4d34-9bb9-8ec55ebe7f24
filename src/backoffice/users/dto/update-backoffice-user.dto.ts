import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Matches,
  Min<PERSON><PERSON>th,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum UserType {
  CONSUMER = 'CONSUMER',
  BUSINESS = 'BUSINESS',
}

export enum BusinessType {
  RETAIL = 'RETAIL',
  MERCHANT = 'MERCHANT',
}

export class UpdateBackofficeUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the user',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email must be a valid email address' })
  email?: string;

  @ApiProperty({
    example: 'johndoe123',
    description: 'Unique username for the user',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Username must be a string' })
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(50, { message: 'Username must not exceed 50 characters' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Username can only contain letters, numbers, and underscores',
  })
  username?: string;

  @ApiProperty({
    example: 'NewSecurePass123!',
    description:
      'New password with minimum 8 characters, including uppercase, lowercase, and number',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Password must be a string' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*/, {
    message:
      'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  })
  password?: string;

  @ApiProperty({
    enum: UserType,
    example: UserType.CONSUMER,
    description: 'Type of the user account',
    required: false,
  })
  @IsOptional()
  @IsEnum(UserType, { message: 'Type must be either CONSUMER or BUSINESS' })
  type?: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    example: BusinessType.RETAIL,
    description: 'Business type (required for BUSINESS users)',
  })
  @IsOptional()
  @IsEnum(BusinessType, {
    message: 'Business type must be either RETAIL or MERCHANT',
  })
  businessType?: BusinessType;

  @ApiProperty({
    example: 'John',
    description: 'First name of the user',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'First name must be a string' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s'-]+$/, {
    message:
      'First name can only contain letters, spaces, hyphens, and apostrophes',
  })
  firstName?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name of the user',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Last name must be a string' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s'-]+$/, {
    message:
      'Last name can only contain letters, spaces, hyphens, and apostrophes',
  })
  lastName?: string;
}
