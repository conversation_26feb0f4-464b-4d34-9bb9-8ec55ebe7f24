import {
  Injectable,
  Logger,
  ConflictException,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { UserResponseDto } from '../../users/dto/user-response.dto';
import { UsersQueryDto } from './dto/users-query.dto';
import {
  UsersListResponseDto,
  PaginationMetaDto,
} from './dto/users-list-response.dto';
import {
  CreateBackofficeUserDto,
  UserType,
  BusinessType,
} from './dto/create-backoffice-user.dto';
import { UpdateBackofficeUserDto } from './dto/update-backoffice-user.dto';
import {
  BackofficeUserResponseDto,
  Status,
} from './dto/backoffice-user-response.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  private readonly SALT_ROUNDS = 12;

  constructor(private readonly prisma: PrismaService) {}

  async findAll(
    query: UsersQueryDto,
  ): Promise<BaseResponseDto<UsersListResponseDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;

      const total = await this.prisma.user.count();

      const users = await this.prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          username: true,
          type: true,
          businessType: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      const userResponseDtos = users.map(
        (user) =>
          new UserResponseDto({
            id: user.id,
            email: user.email,
            username: user.username,
            type: user.type as any,
            businessType: user.businessType as any,
            status: user.status as any,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          }),
      );

      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      const response = new UsersListResponseDto({
        data: userResponseDtos,
        meta,
      });

      return BaseResponseDto.success(response, 'Users retrieved successfully');
    } catch (error) {
      this.logger.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Create a new user in the backoffice
   */
  async create(
    createUserDto: CreateBackofficeUserDto,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    try {
      await this.validateCreateUserRequest(createUserDto);
      const userData = await this.prepareUserDataForCreation(createUserDto);
      const user = await this.createUserInDatabase(userData);
      const userResponse = this.mapUserToResponseDto(user);

      return BaseResponseDto.success(userResponse, 'User created successfully');
    } catch (error) {
      return this.handleCreateUserError(error);
    }
  }

  /**
   * Validate create user request
   */
  private async validateCreateUserRequest(
    createUserDto: CreateBackofficeUserDto,
  ): Promise<void> {
    this.validateBusinessUserRequirements(createUserDto);
    await this.checkUserExists(createUserDto.email, createUserDto.username);
  }

  /**
   * Prepare user data for creation
   */
  private async prepareUserDataForCreation(
    createUserDto: CreateBackofficeUserDto,
  ): Promise<any> {
    const hashedPassword = await this.hashPassword(createUserDto.password);

    return {
      firstName: createUserDto.firstName.trim(),
      lastName: createUserDto.lastName.trim(),
      email: createUserDto.email.toLowerCase(),
      username: createUserDto.username,
      password: hashedPassword,
      type: createUserDto.type,
      businessType: createUserDto.businessType,
      status: 'ACTIVE', // Auto-set to ACTIVE as per requirements
    };
  }

  /**
   * Create user in database
   */
  private async createUserInDatabase(userData: any): Promise<any> {
    return this.prisma.user.create({ data: userData });
  }

  /**
   * Map user entity to response DTO
   */
  private mapUserToResponseDto(user: any): BackofficeUserResponseDto {
    return new BackofficeUserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as UserType,
      businessType: user.businessType as BusinessType,
      status: user.status as Status,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      applicantId: user.applicantId || undefined,
      isInitiated: user.isInitiated,
      googleId: user.googleId || undefined,
      oauthProvider: user.oauthProvider || undefined,
      lastLoginAt: user.lastLoginAt || undefined,
      tokenVersion: user.tokenVersion,
      password: user.password, // Will be excluded by @Exclude() decorator
    });
  }

  /**
   * Handle create user errors
   */
  private handleCreateUserError(error: any): never {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'User with this email or username already exists',
      );
    }
    this.handleServiceError(error, 'Failed to create user');
  }

  /**
   * Find a user by email
   */
  async findByEmail(
    email: string,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    try {
      // Validate email format
      if (!email || typeof email !== 'string' || !this.isValidEmail(email)) {
        throw new BadRequestException('Invalid email format');
      }

      const user = await this.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Create detailed response DTO
      const userResponse = new BackofficeUserResponseDto({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        username: user.username,
        type: user.type as UserType,
        businessType: user.businessType as BusinessType,
        status: user.status as Status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        applicantId: user.applicantId || undefined,
        isInitiated: user.isInitiated,
        googleId: user.googleId || undefined,
        oauthProvider: user.oauthProvider || undefined,
        lastLoginAt: user.lastLoginAt || undefined,
        tokenVersion: user.tokenVersion,
        password: user.password, // Will be excluded by @Exclude() decorator
      });

      return BaseResponseDto.success(
        userResponse,
        'User retrieved successfully',
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.handleServiceError(error, 'Failed to retrieve user');
    }
  }

  /**
   * Update a user by ID
   */
  async update(
    id: string,
    updateUserDto: UpdateBackofficeUserDto,
  ): Promise<BaseResponseDto<BackofficeUserResponseDto>> {
    try {
      await this.validateUpdateUserRequest(id, updateUserDto);
      const updateData = await this.prepareUserDataForUpdate(updateUserDto);
      const updatedUser = await this.updateUserInDatabase(id, updateData);
      const userResponse = this.mapUserToResponseDto(updatedUser);

      return BaseResponseDto.success(userResponse, 'User updated successfully');
    } catch (error) {
      return this.handleUpdateUserError(error);
    }
  }

  /**
   * Validate update user request
   */
  private async validateUpdateUserRequest(
    id: string,
    updateUserDto: UpdateBackofficeUserDto,
  ): Promise<void> {
    this.validateUserId(id);
    await this.findUserById(id);
    this.validateBusinessUserRequirementsForUpdate(updateUserDto);
    await this.checkConflictsForUpdate(id, updateUserDto);
  }

  /**
   * Validate user ID
   */
  private validateUserId(id: string): void {
    if (!id || typeof id !== 'string') {
      throw new BadRequestException('Invalid user ID');
    }
  }

  /**
   * Find user by ID
   */
  private async findUserById(id: string): Promise<any> {
    const existingUser = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new NotFoundException('User not found');
    }

    return existingUser;
  }

  /**
   * Validate business user requirements for update
   */
  private validateBusinessUserRequirementsForUpdate(
    updateUserDto: UpdateBackofficeUserDto,
  ): void {
    if (updateUserDto.type) {
      this.validateBusinessUserRequirements({
        type: updateUserDto.type,
        businessType: updateUserDto.businessType,
      } as CreateBackofficeUserDto);
    }
  }

  /**
   * Check for conflicts during update
   */
  private async checkConflictsForUpdate(
    id: string,
    updateUserDto: UpdateBackofficeUserDto,
  ): Promise<void> {
    if (updateUserDto.email || updateUserDto.username) {
      await this.checkUserExistsForUpdate(
        id,
        updateUserDto.email,
        updateUserDto.username,
      );
    }
  }

  /**
   * Prepare user data for update
   */
  private async prepareUserDataForUpdate(
    updateUserDto: UpdateBackofficeUserDto,
  ): Promise<any> {
    const updateData: any = {};

    if (updateUserDto.firstName) {
      updateData.firstName = updateUserDto.firstName.trim();
    }
    if (updateUserDto.lastName) {
      updateData.lastName = updateUserDto.lastName.trim();
    }
    if (updateUserDto.email) {
      updateData.email = updateUserDto.email.toLowerCase();
    }
    if (updateUserDto.username) {
      updateData.username = updateUserDto.username;
    }
    if (updateUserDto.type) {
      updateData.type = updateUserDto.type;
    }
    if (updateUserDto.businessType !== undefined) {
      updateData.businessType = updateUserDto.businessType;
    }
    if (updateUserDto.password) {
      updateData.password = await this.hashPassword(updateUserDto.password);
    }

    return updateData;
  }

  /**
   * Update user in database
   */
  private async updateUserInDatabase(
    id: string,
    updateData: any,
  ): Promise<any> {
    return this.prisma.user.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Handle update user errors
   */
  private handleUpdateUserError(error: any): never {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'User with this email or username already exists',
      );
    }
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof ConflictException
    ) {
      throw error;
    }
    this.handleServiceError(error, 'Failed to update user');
  }

  /**
   * Delete a user by ID (hard delete)
   */
  async delete(id: string): Promise<BaseResponseDto<{ message: string }>> {
    try {
      // Validate user ID
      if (!id || typeof id !== 'string') {
        throw new BadRequestException('Invalid user ID');
      }

      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // Delete the user (hard delete following existing codebase patterns)
      // Related records will be handled by Prisma cascade rules
      await this.prisma.user.delete({
        where: { id },
      });

      this.logger.log(
        `Successfully deleted user ${id} (${existingUser.email})`,
      );

      return BaseResponseDto.success(
        { message: 'User deleted successfully' },
        'User deleted successfully',
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.handleServiceError(error, 'Failed to delete user');
    }
  }

  // Helper methods

  /**
   * Validate business user requirements
   */
  private validateBusinessUserRequirements(
    createUserDto: Partial<CreateBackofficeUserDto>,
  ): void {
    const { type, businessType } = createUserDto;

    // For CONSUMER users, businessType should be undefined or null
    // For BUSINESS users, businessType is optional
    if (
      type === UserType.CONSUMER &&
      businessType !== undefined &&
      businessType !== null
    ) {
      throw new BadRequestException(
        'Business type should not be provided for consumer users',
      );
    }
  }

  /**
   * Check if user exists by email or username
   */
  private async checkUserExists(
    email: string,
    username: string,
  ): Promise<void> {
    const [existingUserByEmail, existingUserByUsername] = await Promise.all([
      this.prisma.user.findUnique({ where: { email: email.toLowerCase() } }),
      this.prisma.user.findUnique({ where: { username } }),
    ]);

    if (existingUserByEmail || existingUserByUsername) {
      throw new ConflictException(
        'User with this email or username already exists',
      );
    }
  }

  /**
   * Check if user exists by email or username for update operations (excluding current user)
   */
  private async checkUserExistsForUpdate(
    currentUserId: string,
    email?: string,
    username?: string,
  ): Promise<void> {
    const checks = [];

    if (email) {
      checks.push(
        this.prisma.user.findFirst({
          where: {
            email: email.toLowerCase(),
            id: { not: currentUserId },
          },
        }),
      );
    }

    if (username) {
      checks.push(
        this.prisma.user.findFirst({
          where: {
            username,
            id: { not: currentUserId },
          },
        }),
      );
    }

    const results = await Promise.all(checks);
    const existingUser = results.find((user) => user !== null);

    if (existingUser) {
      throw new ConflictException(
        'User with this email or username already exists',
      );
    }
  }

  /**
   * Hash password using bcrypt
   */
  private async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      this.logger.error('Failed to hash password:', error);
      throw new InternalServerErrorException('Failed to hash password');
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Handle service errors with proper logging and exception throwing
   */
  private handleServiceError(error: any, context: string): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }

    this.logger.error(`${context}:`, error);
    throw new InternalServerErrorException(
      'An error occurred while processing your request',
    );
  }
}
