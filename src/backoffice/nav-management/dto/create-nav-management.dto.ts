import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNumber,
  IsPositive,
  IsUUID,
  Matches,
} from 'class-validator';

export enum NavStatus {
  WAITING_FOR_APPROVAL = 'WAITING_FOR_APPROVAL',
  WAITING_FOR_POSTING = 'WAITING_FOR_POSTING',
  POSTING_IN_PROCESS = 'POSTING_IN_PROCESS',
  POSTED = 'POSTED',
  REJECTED = 'REJECTED',
  POSTING_ERROR = 'POSTING_ERROR',
}

export class CreateNavManagementDto {
  @ApiProperty({
    example: '2024-01-15',
    description: 'Date for the NAV entry in YYYY-MM-DD format',
  })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Date must be in YYYY-MM-DD format',
  })
  @IsDateString({}, { message: 'Date must be a valid date' })
  date: string;

  @ApiProperty({
    example: 100.5,
    description: 'Price value for the NAV entry',
  })
  @IsNumber({}, { message: 'Price must be a valid number' })
  @IsPositive({ message: 'Price must be a positive number' })
  price: number;

  @ApiProperty({
    example: '01234567-89ab-cdef-0123-456789abcdef',
    description: 'Product ID to associate with this NAV entry',
    required: false,
  })
  @IsUUID(undefined, { message: 'Product ID must be a valid UUID' })
  productId?: string;
}
