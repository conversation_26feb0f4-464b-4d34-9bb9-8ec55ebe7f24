import { ApiProperty } from '@nestjs/swagger';
import { NavStatus } from './create-nav-management.dto';

export class NavManagementResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Unique identifier for the NAV entry',
  })
  id: string;

  @ApiProperty({
    example: '2024-01-15T00:00:00.000Z',
    description: 'Date for the NAV entry (stored as full datetime)',
  })
  date: Date;

  @ApiProperty({
    example: 100.5,
    description: 'Price value for the NAV entry',
  })
  price: number;

  @ApiProperty({
    enum: NavStatus,
    example: NavStatus.WAITING_FOR_APPROVAL,
    description: 'Status of the NAV entry',
  })
  status: NavStatus;

  @ApiProperty({
    example: 'System generated note: Entry created successfully',
    description: 'Internal system notes (not user-provided)',
    required: false,
  })
  notes?: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Timestamp when the entry was created',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Timestamp when the entry was last updated',
  })
  updatedAt: Date;

  @ApiProperty({
    example: '0x1234567890abcdef...',
    description: 'Safe transaction hash (if transaction was proposed)',
    required: false,
  })
  safeTxHash?: string;

  @ApiProperty({
    example: '0x1234567890abcdef...',
    description: 'Transaction signature (if transaction was proposed)',
    required: false,
  })
  signature?: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Product display name',
    required: false,
  })
  productDisplayName: string;

  constructor(partial: Partial<NavManagementResponseDto>) {
    Object.assign(this, partial);
  }
}
