import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class NavManagementQueryDto {
  @ApiProperty({
    example: 1,
    description: 'Page number (starts from 1)',
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Page must be a number' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit must be a number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    example: 'date',
    description: 'Field to sort by (date, price, status, createdAt)',
    required: false,
    enum: ['date', 'price', 'status', 'createdAt'],
  })
  @IsOptional()
  sortBy?: 'date' | 'price' | 'status' | 'createdAt' = 'date';

  @ApiProperty({
    example: 'desc',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
