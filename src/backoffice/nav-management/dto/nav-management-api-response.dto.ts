import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto, ResponseStatus } from './base-response.dto';
import { NavManagementResponseDto } from './nav-management-response.dto';
import { NavManagementListResponseDto } from './nav-management-list-response.dto';

export class NavManagementCreateResponseDto extends BaseResponseDto<NavManagementResponseDto> {
  @ApiProperty({
    type: NavManagementResponseDto,
    description: 'Created NAV entry data',
  })
  data: NavManagementResponseDto;

  constructor(navData: NavManagementResponseDto, message?: string) {
    super(
      201,
      ResponseStatus.SUCCESS,
      message || 'NAV entry created successfully',
      navData,
    );
  }
}

export class NavManagementListApiResponseDto extends BaseResponseDto<NavManagementListResponseDto> {
  @ApiProperty({
    type: NavManagementListResponseDto,
    description: 'List of NAV entries with pagination',
  })
  data: NavManagementListResponseDto;

  constructor(navListData: NavManagementListResponseDto, message?: string) {
    super(
      200,
      ResponseStatus.SUCCESS,
      message || 'NAV entries retrieved successfully',
      navListData,
    );
  }
}

export class NavManagementErrorResponseDto extends BaseResponseDto<null> {
  @ApiProperty({
    example: null,
    description: 'No data on error',
  })
  data: null;

  constructor(message: string, code: number = 500) {
    super(code, ResponseStatus.ERROR, message, null);
  }
}
