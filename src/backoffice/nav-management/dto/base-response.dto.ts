import { ApiProperty } from '@nestjs/swagger';

export enum ResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  FAILED = 'failed',
}

export class BaseResponseDto<T = any> {
  @ApiProperty({
    example: 200,
    description: 'HTTP status code',
  })
  code: number;

  @ApiProperty({
    example: 'success',
    enum: ResponseStatus,
    description: 'Response status',
  })
  status: ResponseStatus;

  @ApiProperty({
    description: 'Response message',
  })
  message: string;

  @ApiProperty({
    description: 'Response data',
  })
  data: T;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Response timestamp',
  })
  timestamp: string;

  constructor(code: number, status: ResponseStatus, message: string, data: T) {
    this.code = code;
    this.status = status;
    this.message = message;
    this.data = data;
    this.timestamp = new Date().toISOString();
  }

  static success<T>(
    data: T,
    message: string = 'Operation completed successfully',
  ): BaseResponseDto<T> {
    return new BaseResponseDto(200, ResponseStatus.SUCCESS, message, data);
  }

  static error(message: string, code: number = 500): BaseResponseDto<null> {
    return new BaseResponseDto(code, ResponseStatus.ERROR, message, null);
  }

  static failed(message: string, code: number = 400): BaseResponseDto<null> {
    return new BaseResponseDto(code, ResponseStatus.FAILED, message, null);
  }
}
