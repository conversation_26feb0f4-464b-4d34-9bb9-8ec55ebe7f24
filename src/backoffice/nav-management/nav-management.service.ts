import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateNavManagementDto } from './dto/create-nav-management.dto';
import { NavManagementResponseDto } from './dto/nav-management-response.dto';
import { NavManagementQueryDto } from './dto/nav-management-query.dto';
import {
  NavManagementListResponseDto,
  PaginationMetaDto,
} from './dto/nav-management-list-response.dto';
import { NavManagementListApiResponseDto } from './dto/nav-management-api-response.dto';
import { CoboService } from '../cobo/cobo.service';
import { BaseResponseDto } from './dto/base-response.dto';
import { encodeFunctionData } from 'viem';
import * as crypto from 'crypto';

@Injectable()
export class NavManagementService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly coboService: CoboService,
  ) {}

  /**
   * Encode the addPrice function call for the NAV Manager contract
   * @param price - The price value to pass to addPrice function
   * @returns Encoded function call data
   */
  private encodeAddPriceFunction(price: number): string {
    try {
      const priceBigInt = BigInt(Math.floor(price * 1e6));

      const calldata = encodeFunctionData({
        abi: [
          { type: 'function', name: 'addPrice', inputs: [{ type: 'uint256' }] },
        ],
        functionName: 'addPrice',
        args: [priceBigInt],
      });

      return calldata;
    } catch (error) {
      console.error('Failed to encode addPrice function:', error);
      throw new InternalServerErrorException(
        'Failed to encode contract function call',
      );
    }
  }

  async create(
    createNavManagementDto: CreateNavManagementDto,
  ): Promise<BaseResponseDto<NavManagementResponseDto>> {
    try {
      const { date, price, productId } = createNavManagementDto;

      // Convert YYYY-MM-DD string to Date object
      const navDate = new Date(date + 'T00:00:00.000Z');

      // Validate that the date is valid
      if (isNaN(navDate.getTime())) {
        throw new BadRequestException('Invalid date format');
      }

      // Check if a NAV entry already exists for this date (excluding rejected entries)
      const existingNav = await this.prisma.navManagement.findFirst({
        where: {
          date: navDate,
          status: {
            not: 'REJECTED',
          },
          productId: productId,
        },
      });

      if (existingNav) {
        throw new BadRequestException('NAV entry already exists for this date');
      }

      // Get Cobo configuration from environment variables
      const coboWalletId = process.env.COBO_WALLET_ID;
      const coboWalletAddress = process.env.COBO_WALLET_ADDRESS;
      const navManagerContractAddress =
        process.env.NAV_MANAGER_CONTRACT_ADDRESS;
      const chainId = process.env.CHAIN_ID || 'SETH';
      if (!coboWalletId || !navManagerContractAddress) {
        throw new InternalServerErrorException(
          'Cobo wallet ID or NAV Manager contract configuration is missing',
        );
      }

      // Encode the addPrice function call
      const encodedData = this.encodeAddPriceFunction(price);

      // Generate unique request ID for Cobo transaction
      const requestId = `nav-management-${crypto.randomUUID()}`;

      // Create contract call transaction using Cobo
      const coboResult = await this.coboService.createContractCallTransaction({
        request_id: requestId,
        chain_id: chainId,
        source: {
          source_type: 'Org-Controlled',
          wallet_id: coboWalletId,
          address: coboWalletAddress,
        },
        destination: {
          destination_type: 'EVM_Contract',
          address: navManagerContractAddress,
          calldata: encodedData,
        },
      });

      // Only create the NAV entry if Cobo transaction was successful
      const navEntry = await this.prisma.navManagement.create({
        data: {
          date: navDate,
          price,
          status: 'WAITING_FOR_APPROVAL',
          notes: `Cobo transaction created successfully to call addPrice(${price}) on NAV Manager contract. Transaction ID: ${coboResult.transaction_id}`,
          productId: productId,
        },
        include: {
          product: {
            select: {
              displayName: true,
            },
          },
        },
      });

      // Create CoboTransaction record
      try {
        await this.prisma.coboTransaction.create({
          data: {
            id: requestId,
            navManagementId: navEntry.id,
            walletType: 'Org-Controlled',
            walletAddress: coboWalletAddress,
            chainId: chainId,
            kind: 'CONTRACT_CALL',
            toAddress: navManagerContractAddress,
            method: 'addPrice',
            calldata: encodedData,
            value: '0',
            status: 'SUBMITTED',
            coboTransactionId: coboResult.transaction_id,
            idempotencyKey: requestId,
            rawRequest: {
              request_id: requestId,
              chain_id: chainId,
              source: {
                source_type: 'Org-Controlled',
                wallet_id: coboWalletId,
                address: coboWalletAddress,
              },
              destination: {
                destination_type: 'EVM_Contract',
                address: navManagerContractAddress,
                calldata: encodedData,
              },
            },
            rawResponse: coboResult as any,
          },
        });
      } catch (coboTransactionError) {
        console.error(
          'Failed to create CoboTransaction record:',
          coboTransactionError,
        );
        // Log the error but don't fail the entire operation since NAV entry was created successfully
        // The CoboTransaction record is for tracking purposes
      }

      const navResponse = new NavManagementResponseDto({
        id: navEntry.id,
        date: navEntry.date,
        price: Number(navEntry.price),
        status: navEntry.status as any,
        notes: navEntry.notes,
        createdAt: navEntry.createdAt,
        updatedAt: navEntry.updatedAt,
        productDisplayName: navEntry.product?.displayName || null,
      });

      return BaseResponseDto.success(
        navResponse,
        'NAV entry created, Cobo transaction proposed, and transaction record logged successfully',
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      console.error('NavManagementService Error:', error);

      // If it's a Cobo-related error, provide more specific error message
      if (
        error.message?.includes('Cobo') ||
        error.message?.includes('Failed to create contract call transaction')
      ) {
        throw new InternalServerErrorException(
          `Failed to create transaction with Cobo: ${error.message}`,
        );
      }

      throw new InternalServerErrorException(
        'An error occurred while creating the NAV entry',
      );
    }
  }

  async findAll(
    query: NavManagementQueryDto,
  ): Promise<NavManagementListApiResponseDto> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'date',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;

      // Get total count
      const total = await this.prisma.navManagement.count();

      // Get paginated data
      const navEntries = await this.prisma.navManagement.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: {
          id: true,
          date: true,
          price: true,
          status: true,
          notes: true,
          createdAt: true,
          updatedAt: true,
          product: {
            select: {
              displayName: true,
            },
          },
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      // Transform data to response DTOs
      const data = navEntries.map(
        (entry) =>
          new NavManagementResponseDto({
            id: entry.id,
            date: entry.date,
            price: Number(entry.price),
            status: entry.status as any,
            notes: entry.notes,
            createdAt: entry.createdAt,
            updatedAt: entry.updatedAt,
            productDisplayName: entry.product?.displayName || null,
          }),
      );

      const listResponse = new NavManagementListResponseDto({
        data,
        meta,
      });

      return new NavManagementListApiResponseDto(
        listResponse,
        'NAV entries retrieved successfully',
      );
    } catch (error) {
      console.error('NavManagementService findAll Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching NAV entries',
      );
    }
  }
}
