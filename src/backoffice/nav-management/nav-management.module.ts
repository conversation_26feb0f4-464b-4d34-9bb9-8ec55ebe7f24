import { Module } from '@nestjs/common';
import { NavManagementController } from './nav-management.controller';
import { NavManagementService } from './nav-management.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { CoboModule } from '../cobo/cobo.module';

@Module({
  imports: [PrismaModule, CoboModule],
  controllers: [NavManagementController],
  providers: [NavManagementService],
  exports: [NavManagementService],
})
export class NavManagementModule {}
