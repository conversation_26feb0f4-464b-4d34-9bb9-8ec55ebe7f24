import { Injectable, InternalServerErrorException } from '@nestjs/common';
import SafeApiKit from '@safe-global/api-kit';
import Safe from '@safe-global/protocol-kit';
import { MetaTransactionData, OperationType } from '@safe-global/types-kit';

@Injectable()
export class SafeService {
  private apiKit: SafeApiKit;
  private protocolKit: Safe;

  constructor() {
    // Initialize SAFE API Kit
    this.apiKit = new SafeApiKit({
      chainId: 42161n,
      apiKey: process.env.SAFE_API_KEY,
    });
  }

  async initializeProtocolKit(
    privateKey: string,
    safeAddress: string,
    rpcUrl: string,
  ): Promise<void> {
    try {
      this.protocolKit = await Safe.init({
        provider: rpcUrl,
        signer: privateKey,
        safeAddress: safeAddress,
      });
    } catch (error) {
      console.error('Failed to initialize Protocol Kit:', error);
      throw new InternalServerErrorException(
        'Failed to initialize SAFE Protocol Kit',
      );
    }
  }

  async proposeTransaction(
    safeAddress: string,
    to: string,
    value: string,
    data: string,
    senderAddress: string,
    senderPrivateKey: string,
    rpcUrl: string,
  ): Promise<{ safeTxHash: string; signature: string }> {
    try {
      // Initialize Protocol Kit with sender's private key
      await this.initializeProtocolKit(senderPrivateKey, safeAddress, rpcUrl);

      // Create transaction data
      const safeTransactionData: MetaTransactionData = {
        to,
        value,
        data,
        operation: OperationType.Call,
      };

      // Create the transaction
      const safeTransaction = await this.protocolKit.createTransaction({
        transactions: [safeTransactionData],
      });

      // Get transaction hash
      const safeTxHash =
        await this.protocolKit.getTransactionHash(safeTransaction);

      // Sign the transaction
      const signature = await this.protocolKit.signHash(safeTxHash);

      // Propose transaction to the service
      await this.apiKit.proposeTransaction({
        safeAddress,
        safeTransactionData: safeTransaction.data,
        safeTxHash,
        senderAddress,
        senderSignature: signature.data,
      });

      return {
        safeTxHash,
        signature: signature.data,
      };
    } catch (error) {
      console.error('Failed to propose transaction:', error);
      throw new InternalServerErrorException(
        'Failed to propose transaction to SAFE',
      );
    }
  }

  async getTransaction(safeTxHash: string) {
    try {
      return await this.apiKit.getTransaction(safeTxHash);
    } catch (error) {
      console.error('Failed to get transaction:', error);
      throw new InternalServerErrorException(
        'Failed to retrieve transaction from SAFE',
      );
    }
  }

  async confirmTransaction(safeTxHash: string, signature: string) {
    try {
      return await this.apiKit.confirmTransaction(safeTxHash, signature);
    } catch (error) {
      console.error('Failed to confirm transaction:', error);
      throw new InternalServerErrorException('Failed to confirm transaction');
    }
  }

  /**
   * Encode the addPrice function call for the NAV Manager contract
   * @param price - The price value to pass to addPrice function
   * @returns Encoded function call data
   */
  encodeAddPriceFunction(price: number): string {
    try {
      // Function signature: addPrice(uint256)
      // Function selector: 0x3c6b4b28 (first 4 bytes of keccak256("addPrice(uint256)"))
      const functionSelector = '0x3c6b4b28';

      // Convert price to BigInt and then to hex, padding to 32 bytes (64 hex chars)
      const priceBigInt = BigInt(Math.floor(price * 1e6));
      const priceHex = priceBigInt.toString(16).padStart(64, '0');

      return functionSelector + priceHex;
    } catch (error) {
      console.error('Failed to encode addPrice function:', error);
      throw new InternalServerErrorException(
        'Failed to encode contract function call',
      );
    }
  }
}
