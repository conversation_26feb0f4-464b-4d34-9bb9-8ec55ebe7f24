import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { CreateNavManagementDto } from './dto/create-nav-management.dto';
import { NavManagementResponseDto } from './dto/nav-management-response.dto';
import { NavManagementQueryDto } from './dto/nav-management-query.dto';
import { NavManagementListApiResponseDto } from './dto/nav-management-api-response.dto';
import { NavManagementService } from './nav-management.service';
import { BaseBackofficeResponseDto } from '../common/dto/base-backoffice-response.dto';
import { BaseBackofficeController } from '../common/base-backoffice.controller';

@ApiTags('Backoffice - Nav Management')
@Controller('backoffice/nav-management')
export class NavManagementController extends BaseBackofficeController {
  constructor(private readonly navManagementService: NavManagementService) {
    super();
  }

  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new NAV entry',
    description: 'Create a new Net Asset Value entry with date and price.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    type: BaseBackofficeResponseDto,
    description: 'NAV entry created successfully',
    schema: {
      example: {
        code: 201,
        status: 'success',
        message:
          'NAV entry created and SAFE transaction proposed to call addPrice function on NAV Manager contract',
        data: {
          id: 'uuid',
          date: '2024-01-15T00:00:00.000Z',
          price: 100.5,
          status: 'WAITING_FOR_APPROVAL',
          notes:
            'SAFE transaction proposed successfully to call addPrice(100.5) on NAV Manager contract. TxHash: 0x1234...',
          createdAt: '2024-01-15T10:30:00.000Z',
          updatedAt: '2024-01-15T10:30:00.000Z',
          safeTxHash: '0x1234567890abcdef...',
          signature: '0x1234567890abcdef...',
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiBody({
    type: CreateNavManagementDto,
    description:
      'NAV entry data with date and price. The system will propose a transaction to SAFE to call addPrice(price) function on the NAV Manager contract.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or NAV entry already exists for this date',
    schema: {
      example: {
        code: 400,
        status: 'failed',
        message: 'NAV entry already exists for this date',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error or SAFE transaction proposal failed',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'Failed to propose transaction to SAFE: [error details]',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async create(
    @Body() createNavManagementDto: CreateNavManagementDto,
  ): Promise<BaseBackofficeResponseDto<NavManagementResponseDto>> {
    return this.navManagementService.create(createNavManagementDto);
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all NAV entries',
    description:
      'Retrieve paginated list of NAV management entries with filtering and sorting options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: BaseBackofficeResponseDto,
    description: 'List of NAV entries retrieved successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'NAV entries retrieved successfully',
        data: {
          data: [
            {
              id: 'uuid',
              date: '2024-01-15T00:00:00.000Z',
              price: 100.5,
              status: 'WAITING_FOR_APPROVAL',
              notes: 'System generated note',
              createdAt: '2024-01-15T10:30:00.000Z',
              updatedAt: '2024-01-15T10:30:00.000Z',
            },
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['date', 'price', 'status', 'createdAt'],
    description: 'Field to sort by',
    example: 'date',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while fetching NAV entries',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async findAll(
    @Query() query: NavManagementQueryDto,
  ): Promise<NavManagementListApiResponseDto> {
    return this.navManagementService.findAll(query);
  }
}
