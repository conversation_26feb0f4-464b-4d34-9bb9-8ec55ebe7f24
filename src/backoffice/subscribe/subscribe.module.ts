import { Module } from '@nestjs/common';
import { SubscribeController } from './subscribe.controller';
import { SubscribeService } from './subscribe.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { CoboModule } from '../cobo/cobo.module';

@Module({
  imports: [PrismaModule, CoboModule],
  controllers: [SubscribeController],
  providers: [SubscribeService],
  exports: [SubscribeService],
})
export class SubscribeModule {}
