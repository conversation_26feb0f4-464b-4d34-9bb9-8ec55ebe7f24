import { ApiProperty } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../common/dto/pagination.dto';

export class TokenAmountDto {
  @ApiProperty({
    description: 'Currency name',
    example: 'USDT',
  })
  currency: string;

  @ApiProperty({
    description: 'Amount in this currency',
    example: '1000.50',
  })
  amount: string;
}

export class FulfillItemDto {
  @ApiProperty({
    description: 'Fulfill record ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Product ID',
    example: 'uuid-string',
  })
  productId: string;

  @ApiProperty({
    description: 'Product name',
    example: 'Bitcoin Fund',
  })
  productName: string;

  @ApiProperty({
    description: 'Cutoff date',
    example: '2024-01-15T00:00:00.000Z',
  })
  cutoffDate: string;

  @ApiProperty({
    description: 'Current status',
    example: 'POSTED',
  })
  status: string;

  @ApiProperty({
    description: 'Total amount from all subscribes',
    example: '1000.50',
  })
  totalAmount: string;

  @ApiProperty({
    description: 'Price from NAV management',
    example: '50000.00',
    nullable: true,
  })
  price: string | null;

  @ApiProperty({
    description: 'Total subscribe value (totalAmount * price)',
    example: '50000500.00',
  })
  totalSubscribe: string;

  @ApiProperty({
    description: 'Number of subscribes',
    example: 10,
  })
  subscribeCount: number;

  @ApiProperty({
    description: 'Tokens breakdown by currency',
    type: [TokenAmountDto],
  })
  tokens: TokenAmountDto[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: string;

  constructor(data: {
    id: string;
    productId: string;
    productName: string;
    cutoffDate: string;
    status: string;
    totalAmount: string;
    price: string | null;
    totalSubscribe: string;
    subscribeCount: number;
    tokens: TokenAmountDto[];
    createdAt: string;
    updatedAt: string;
  }) {
    this.id = data.id;
    this.productId = data.productId;
    this.productName = data.productName;
    this.cutoffDate = data.cutoffDate;
    this.status = data.status;
    this.totalAmount = data.totalAmount;
    this.price = data.price;
    this.totalSubscribe = data.totalSubscribe;
    this.subscribeCount = data.subscribeCount;
    this.tokens = data.tokens;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
  }
}

export class FulfillListDataDto {
  @ApiProperty({
    description: 'List of fulfill records',
    type: [FulfillItemDto],
  })
  data: FulfillItemDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;

  constructor(data: { data: FulfillItemDto[]; meta: PaginationMetaDto }) {
    this.data = data.data;
    this.meta = data.meta;
  }
}

export class FulfillListApiResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Fulfill records retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Fulfill list data with pagination',
    type: FulfillListDataDto,
  })
  data: FulfillListDataDto;

  constructor(data: FulfillListDataDto, message: string) {
    this.success = true;
    this.message = message;
    this.data = data;
  }
}
