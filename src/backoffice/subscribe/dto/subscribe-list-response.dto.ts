import { ApiProperty } from '@nestjs/swagger';
import { SubscribeResponseDto } from './subscribe-response.dto';
import { PaginationMetaDto } from '../../common/dto/pagination.dto';

export class SubscribeListResponseDto {
  @ApiProperty({
    type: [SubscribeResponseDto],
    description: 'Array of subscribe requests',
  })
  data: SubscribeResponseDto[];

  @ApiProperty({
    type: PaginationMetaDto,
    description: 'Pagination metadata',
  })
  meta: PaginationMetaDto;

  constructor(partial: Partial<SubscribeListResponseDto>) {
    Object.assign(this, partial);
  }
}
