import { IsDateString, <PERSON>NotEmpty, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum DistributionTypeEnum {
  NONE = 'NONE',
  MINT = 'MINT',
  TRANSFER = 'TRANSFER',
}

export class FulfillRequestDto {
  @ApiProperty({
    description: 'Cutoff date for the fulfill operation (YYYY-MM-DD format)',
    example: '2024-01-15',
  })
  @IsDateString(
    {},
    { message: 'Date must be a valid date in YYYY-MM-DD format' },
  )
  @IsNotEmpty({ message: 'Date is required' })
  date: string;

  @ApiPropertyOptional({
    description: 'Distribution type for the subscription',
    enum: DistributionTypeEnum,
    example: 'DIRECT',
    default: 'DIRECT',
  })
  @IsOptional()
  @IsEnum(DistributionTypeEnum, {
    message: 'Distribution type must be NONE, MINT, or TRANSFER',
  })
  distributionType?: string;
}
