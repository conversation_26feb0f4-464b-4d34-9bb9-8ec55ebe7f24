import { ApiProperty } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../common/dto/pagination.dto';

export class SettlementRecordDto {
  @ApiProperty({
    description: 'Settlement record ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Settlement date',
    example: '2024-01-15T00:00:00.000Z',
  })
  settlementDate: string;

  @ApiProperty({
    description: 'Total number of fulfills',
    example: 5,
  })
  totalFulfill: number;

  @ApiProperty({
    description: 'Total number of transactions',
    example: 25,
  })
  totalTransaction: number;

  @ApiProperty({
    description: 'Token name (currency)',
    example: 'USDT',
  })
  tokenName: string;

  @ApiProperty({
    description: 'Token amount',
    example: '1000.50000000',
  })
  amount: string;

  @ApiProperty({
    description: 'Total value',
    example: '50000.50',
  })
  value: string;

  @ApiProperty({
    description: 'Current status',
    example: 'POSTED',
  })
  status: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: string;

  constructor(data: {
    id: string;
    settlementDate: string;
    totalFulfill: number;
    totalTransaction: number;
    tokenName: string;
    amount: string;
    value: string;
    status: string;
    createdAt: string;
  }) {
    this.id = data.id;
    this.settlementDate = data.settlementDate;
    this.totalFulfill = data.totalFulfill;
    this.totalTransaction = data.totalTransaction;
    this.tokenName = data.tokenName;
    this.amount = data.amount;
    this.value = data.value;
    this.status = data.status;
    this.createdAt = data.createdAt;
  }
}

export class SettlementListDataDto {
  @ApiProperty({
    description: 'List of settlement records',
    type: [SettlementRecordDto],
  })
  data: SettlementRecordDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  meta: PaginationMetaDto;

  constructor(data: { data: SettlementRecordDto[]; meta: PaginationMetaDto }) {
    this.data = data.data;
    this.meta = data.meta;
  }
}

export class SettlementListApiResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Settlement records retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Settlement list data with pagination',
    type: SettlementListDataDto,
  })
  data: SettlementListDataDto;

  constructor(data: SettlementListDataDto, message: string) {
    this.success = true;
    this.message = message;
    this.data = data;
  }
}
