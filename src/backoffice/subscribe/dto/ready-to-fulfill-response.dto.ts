import { ApiProperty } from '@nestjs/swagger';
import { BaseBackofficeResponseDto } from '../../common/dto/base-backoffice-response.dto';

export class TokenSummaryDto {
  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Token display name',
  })
  displayName: string;

  @ApiProperty({
    example: '100.50000000',
    description: 'Total token amount',
  })
  tokenAmount: string;

  @ApiProperty({
    example: '150000.00',
    description: 'Total amount in USD',
  })
  amount: string;

  @ApiProperty({
    example: '75.5',
    description: 'Percentage of total',
  })
  percentage: string;
}

export class ReadyToFulfillSummaryDto {
  @ApiProperty({
    example: '150000.00',
    description: 'Total amount across all requests',
  })
  totalAmount: string;

  @ApiProperty({
    example: 25,
    description: 'Number of transactions',
  })
  numOfTransactions: number;

  @ApiProperty({
    example: 18,
    description: 'Number of unique wallets',
  })
  numOfUniqueWallets: number;

  @ApiProperty({
    example: ['Bitcoin ETF', 'Ethereum ETF', 'Solana ETF'],
    description: 'Array of product display names',
    type: [String],
  })
  products: string[];

  @ApiProperty({
    type: [TokenSummaryDto],
    description: 'Token breakdown with percentages',
  })
  token: TokenSummaryDto[];
}

export class ReadyToFulfillRequestDto {
  @ApiProperty({
    example: '0x1234567890abcdef...',
    description: 'Transaction hash',
  })
  txHash: string;

  @ApiProperty({
    example: '0xabcdef1234567890...',
    description: 'Wallet address',
  })
  wallet: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Product display name',
  })
  product: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: 'APPROVED',
    description: 'KYC status',
  })
  kytStatus: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Token display name',
  })
  token: string;

  @ApiProperty({
    example: '100.50000000',
    description: 'Amount',
  })
  amount: string;

  @ApiProperty({
    example: '15000.00',
    description: 'Value in USD',
  })
  value: string;
}

export class ReadyToFulfillResponseDto {
  @ApiProperty({
    type: ReadyToFulfillSummaryDto,
    description: 'Aggregated summary data',
  })
  summary: ReadyToFulfillSummaryDto;

  @ApiProperty({
    type: [ReadyToFulfillRequestDto],
    description: 'Array of individual requests',
  })
  requests: ReadyToFulfillRequestDto[];

  constructor(partial: Partial<ReadyToFulfillResponseDto>) {
    Object.assign(this, partial);
  }
}

export class ReadyToFulfillApiResponseDto extends BaseBackofficeResponseDto<ReadyToFulfillResponseDto> {
  @ApiProperty({
    type: ReadyToFulfillResponseDto,
    description: 'Ready to fulfill requests data with summary and requests',
  })
  data: ReadyToFulfillResponseDto;

  constructor(readyToFulfillData: ReadyToFulfillResponseDto, message?: string) {
    super(
      200,
      'success',
      message || 'Ready to fulfill requests retrieved successfully',
      readyToFulfillData,
    );
  }
}
