import { ApiProperty } from '@nestjs/swagger';
import { FulfillResponseDto } from './fulfill-response.dto';

export class FulfillListResponseDto {
  @ApiProperty({
    description: 'Array of fulfill responses',
    type: [FulfillResponseDto],
  })
  data: FulfillResponseDto[];

  @ApiProperty({
    description: 'Total number of products fulfilled',
    example: 3,
  })
  totalProducts: number;

  @ApiProperty({
    description: 'Total number of subscribes processed',
    example: 15,
  })
  totalSubscribes: number;

  @ApiProperty({
    description: 'Overall total amount',
    example: '5000.50',
  })
  overallTotalAmount: string;

  constructor(data: {
    data: FulfillResponseDto[];
    totalProducts: number;
    totalSubscribes: number;
    overallTotalAmount: string;
  }) {
    this.data = data.data;
    this.totalProducts = data.totalProducts;
    this.totalSubscribes = data.totalSubscribes;
    this.overallTotalAmount = data.overallTotalAmount;
  }
}

export class FulfillOperationsApiResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Fulfill operations completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Fulfill list data',
    type: FulfillListResponseDto,
  })
  data: FulfillListResponseDto;

  constructor(data: FulfillListResponseDto, message: string) {
    this.success = true;
    this.message = message;
    this.data = data;
  }
}
