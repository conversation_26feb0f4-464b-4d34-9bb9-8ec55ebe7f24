import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { PaginationDto } from '../../common/dto/pagination.dto';

export enum SubscribeSortBy {
  CREATED_AT = 'createdAt',
  AMOUNT = 'amount',
  EXPECTED_PRICE = 'expectedPrice',
  PAYMENT_STATUS = 'paymentStatus',
  CHECKOUT_STATUS = 'checkoutStatus',
  SUBSCRIBE_STATUS = 'subscribeStatus',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export class SubscribeQueryDto extends PaginationDto {
  @ApiProperty({
    required: false,
    enum: SubscribeSortBy,
    description: 'Field to sort by',
    example: SubscribeSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(SubscribeSortBy)
  sortBy?: SubscribeSortBy = SubscribeSortBy.CREATED_AT;

  @ApiProperty({
    required: false,
    enum: SortOrder,
    description: 'Sort order',
    example: SortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({
    required: false,
    description: 'Filter by payment status',
    example: 'completed',
  })
  @IsOptional()
  paymentStatus?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by checkout status',
    example: 'completed',
  })
  @IsOptional()
  checkoutStatus?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by subscribe status',
    example: 'active',
  })
  @IsOptional()
  subscribeStatus?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by chain ID',
    example: '1',
  })
  @IsOptional()
  chainId?: string;
}
