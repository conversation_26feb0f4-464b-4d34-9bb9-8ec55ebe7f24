import { ApiProperty } from '@nestjs/swagger';
import { BaseBackofficeResponseDto } from '../../common/dto/base-backoffice-response.dto';

export class SettlementItemDto {
  @ApiProperty({
    description: 'Settlement ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Settlement date',
    example: '2024-01-15T00:00:00.000Z',
  })
  settlementDate: string;

  @ApiProperty({
    description: 'Number of fulfills included',
    example: 3,
  })
  totalFulfill: number;

  @ApiProperty({
    description: 'Total number of transactions',
    example: 25,
  })
  totalTransaction: number;

  @ApiProperty({
    description: 'Token name/currency',
    example: 'USDT',
  })
  tokenName: string;

  @ApiProperty({
    description: 'Total token amount',
    example: '1000.50000000',
  })
  tokenAmount: string;

  @ApiProperty({
    description: 'Total value in USD',
    example: '50000.00',
  })
  totalValue: string;

  @ApiProperty({
    description: 'Settlement status',
    example: 'POSTED',
  })
  status: string;

  @ApiProperty({
    description: 'Array of fulfill IDs included in this settlement',
    example: ['uuid-fulfill-1', 'uuid-fulfill-2'],
    type: [String],
  })
  fulfillIds: string[];

  @ApiProperty({
    description: 'Array of subscribe IDs included in this settlement',
    example: ['uuid-subscribe-1', 'uuid-subscribe-2'],
    type: [String],
  })
  subscribeIds: string[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: string;

  constructor(data: {
    id: string;
    settlementDate: string;
    totalFulfill: number;
    totalTransaction: number;
    tokenName: string;
    tokenAmount: string;
    totalValue: string;
    status: string;
    fulfillIds: string[];
    subscribeIds: string[];
    createdAt: string;
  }) {
    this.id = data.id;
    this.settlementDate = data.settlementDate;
    this.totalFulfill = data.totalFulfill;
    this.totalTransaction = data.totalTransaction;
    this.tokenName = data.tokenName;
    this.tokenAmount = data.tokenAmount;
    this.totalValue = data.totalValue;
    this.status = data.status;
    this.fulfillIds = data.fulfillIds;
    this.subscribeIds = data.subscribeIds;
    this.createdAt = data.createdAt;
  }
}

export class SettleResponseDto {
  @ApiProperty({
    description: 'Array of settlement records grouped by token',
    type: [SettlementItemDto],
  })
  settlements: SettlementItemDto[];

  @ApiProperty({
    description: 'Total number of fulfills processed',
    example: 5,
  })
  totalFulfills: number;

  @ApiProperty({
    description: 'Total number of transactions processed',
    example: 150,
  })
  totalTransactions: number;

  @ApiProperty({
    description: 'Total settlement value',
    example: '250000.00',
  })
  totalSettlementValue: string;

  constructor(data: {
    settlements: SettlementItemDto[];
    totalFulfills: number;
    totalTransactions: number;
    totalSettlementValue: string;
  }) {
    this.settlements = data.settlements;
    this.totalFulfills = data.totalFulfills;
    this.totalTransactions = data.totalTransactions;
    this.totalSettlementValue = data.totalSettlementValue;
  }
}

export class SettleApiResponseDto extends BaseBackofficeResponseDto<SettleResponseDto> {
  @ApiProperty({
    type: SettleResponseDto,
    description: 'Settlement operation result with grouped settlement data',
  })
  data: SettleResponseDto;

  constructor(settleData: SettleResponseDto, message?: string) {
    super(
      200,
      'success',
      message || 'Settlement operation completed successfully',
      settleData,
    );
  }
}
