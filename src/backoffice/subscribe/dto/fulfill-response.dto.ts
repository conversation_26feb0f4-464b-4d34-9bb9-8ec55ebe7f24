import { ApiProperty } from '@nestjs/swagger';
import { CoboTxStatus } from '../../../../generated/prisma';

export class FulfillResponseDto {
  @ApiProperty({
    description: 'Fulfill record ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Product ID',
    example: 'uuid-string',
  })
  productId: string;

  @ApiProperty({
    description: 'Product display name',
    example: 'Bitcoin',
  })
  productName: string;

  @ApiProperty({
    description: 'Cutoff date for the fulfill operation',
    example: '2024-01-15T00:00:00.000Z',
  })
  cutoffDate: string;

  @ApiProperty({
    description: 'Number of subscribe records included',
    example: 5,
  })
  subscribeCount: number;

  @ApiProperty({
    description: 'Total amount of all subscribes',
    example: '1000.50',
  })
  totalAmount: string;

  @ApiProperty({
    description: 'Current status of the fulfill operation',
    enum: CoboTxStatus,
    example: 'SUBMITTED',
  })
  status: CoboTxStatus;

  @ApiProperty({
    description: 'Additional notes',
    example: 'Fulfill operation initiated successfully',
    required: false,
  })
  notes?: string;

  @ApiProperty({
    description: 'Cobo transaction ID',
    example: 'cobo-tx-id',
    required: false,
  })
  coboTransactionId?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: string;

  constructor(data: {
    id: string;
    productId: string;
    productName: string;
    cutoffDate: string;
    subscribeCount: number;
    totalAmount: string;
    status: CoboTxStatus;
    notes?: string;
    coboTransactionId?: string;
    createdAt: string;
    updatedAt: string;
  }) {
    this.id = data.id;
    this.productId = data.productId;
    this.productName = data.productName;
    this.cutoffDate = data.cutoffDate;
    this.subscribeCount = data.subscribeCount;
    this.totalAmount = data.totalAmount;
    this.status = data.status;
    this.notes = data.notes;
    this.coboTransactionId = data.coboTransactionId;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
  }
}

export class FulfillApiResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Fulfill operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Fulfill data',
    type: FulfillResponseDto,
  })
  data: FulfillResponseDto;

  constructor(data: FulfillResponseDto, message: string) {
    this.success = true;
    this.message = message;
    this.data = data;
  }
}
