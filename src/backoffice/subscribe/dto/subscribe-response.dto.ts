import { ApiProperty } from '@nestjs/swagger';

export class SubscribeResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Subscribe ID',
  })
  id: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Transaction hash',
  })
  txHash: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Product display name',
  })
  productName: string;

  @ApiProperty({
    example: 'completed',
    description: 'Payment status',
  })
  paymentStatus: string;

  @ApiProperty({
    example: 'completed',
    description: 'Checkout status',
  })
  checkoutStatus: string;

  @ApiProperty({
    example: 'active',
    description: 'Subscribe status',
  })
  subscribeStatus: string;

  @ApiProperty({
    example: '100.00000000',
    description: 'Amount',
  })
  amount: string;

  @ApiProperty({
    example: 'USDC',
    description: 'Currency',
  })
  currency: string;

  @ApiProperty({
    example: '100.00000000',
    description: 'Expected price',
  })
  expectedPrice: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Created at timestamp',
  })
  createdAt: string;

  constructor(partial: Partial<SubscribeResponseDto>) {
    Object.assign(this, partial);
  }
}
