import { ApiProperty } from '@nestjs/swagger';
import { BaseBackofficeResponseDto } from '../../common/dto/base-backoffice-response.dto';
import { SubscribeListResponseDto } from './subscribe-list-response.dto';

export class SubscribeListApiResponseDto extends BaseBackofficeResponseDto<SubscribeListResponseDto> {
  @ApiProperty({
    type: SubscribeListResponseDto,
    description: 'List of subscribe requests with pagination',
  })
  data: SubscribeListResponseDto;

  constructor(subscribeListData: SubscribeListResponseDto, message?: string) {
    super(
      200,
      'success',
      message || 'Subscribe requests retrieved successfully',
      subscribeListData,
    );
  }
}
