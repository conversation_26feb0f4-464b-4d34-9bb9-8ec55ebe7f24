import { ApiProperty } from '@nestjs/swagger';
import { BaseBackofficeResponseDto } from '../../common/dto/base-backoffice-response.dto';

export class ReadyToSettleTokenSummaryDto {
  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Token display name',
  })
  displayName: string;

  @ApiProperty({
    example: '100.50000000',
    description: 'Total token amount',
  })
  tokenAmount: string;

  @ApiProperty({
    example: '150000.00',
    description: 'Total amount in USD',
  })
  amount: string;

  @ApiProperty({
    example: '75.5',
    description: 'Percentage of total',
  })
  percentage: string;
}

export class ReadyToSettleSummaryDto {
  @ApiProperty({
    example: '150000.00',
    description: 'Total amount across all requests',
  })
  totalAmount: string;

  @ApiProperty({
    example: 25,
    description: 'Number of transactions',
  })
  numOfTransactions: number;

  @ApiProperty({
    example: 18,
    description: 'Number of unique wallets',
  })
  numOfUniqueWallets: number;

  @ApiProperty({
    example: ['Bitcoin ETF', 'Ethereum ETF', 'Solana ETF'],
    description: 'Array of product display names',
    type: [String],
  })
  products: string[];

  @ApiProperty({
    type: [ReadyToSettleTokenSummaryDto],
    description: 'Token breakdown with percentages',
  })
  token: ReadyToSettleTokenSummaryDto[];
}

export class ReadyToSettleRequestDto {
  @ApiProperty({
    example: '0x1234567890abcdef...',
    description: 'Transaction hash',
  })
  txHash: string;

  @ApiProperty({
    example: '0xabcdef1234567890...',
    description: 'Wallet address',
  })
  walletAddress: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Product display name',
  })
  productName: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Creation timestamp',
  })
  timestamp: string;

  @ApiProperty({
    example: 'APPROVED',
    description: 'Subscribe status',
  })
  status: string;

  @ApiProperty({
    example: 'Bitcoin ETF',
    description: 'Token display name',
  })
  token: string;

  @ApiProperty({
    example: '100.50000000',
    description: 'Amount',
  })
  amount: string;

  @ApiProperty({
    example: '15000.00',
    description: 'Value in USD',
  })
  value: string;
}

export class ReadyToSettleResponseDto {
  @ApiProperty({
    type: ReadyToSettleSummaryDto,
    description: 'Aggregated summary data',
  })
  summary: ReadyToSettleSummaryDto;

  @ApiProperty({
    type: [ReadyToSettleRequestDto],
    description: 'Array of individual requests',
  })
  requests: ReadyToSettleRequestDto[];

  constructor(partial: Partial<ReadyToSettleResponseDto>) {
    Object.assign(this, partial);
  }
}

export class ReadyToSettleApiResponseDto extends BaseBackofficeResponseDto<ReadyToSettleResponseDto> {
  @ApiProperty({
    type: ReadyToSettleResponseDto,
    description: 'Ready to settle requests data with summary and requests',
  })
  data: ReadyToSettleResponseDto;

  constructor(readyToSettleData: ReadyToSettleResponseDto, message?: string) {
    super(
      200,
      'success',
      message || 'Ready to settle requests retrieved successfully',
      readyToSettleData,
    );
  }
}
