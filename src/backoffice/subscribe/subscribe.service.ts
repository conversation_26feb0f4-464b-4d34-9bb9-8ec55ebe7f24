import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { encodeFunctionData } from 'viem';
import { PrismaService } from '../../prisma/prisma.service';
import { CoboService } from '../cobo/cobo.service';
import {
  SubscribeResponseDto,
  SubscribeQueryDto,
  SubscribeListResponseDto,
  SubscribeListApiResponseDto,
  FulfillRequestDto,
  FulfillResponseDto,
  FulfillApiResponseDto,
  FulfillListResponseDto,
  FulfillOperationsApiResponseDto,
  FulfillListQueryDto,
  FulfillItemDto,
  TokenAmountDto,
  ReadyToSettleResponseDto,
  ReadyToSettleApiResponseDto,
  ReadyToSettleSummaryDto,
  ReadyToSettleRequestDto,
  ReadyToSettleTokenSummaryDto,
  SettleRequestDto,
  SettleApiResponseDto,
  SettleResponseDto,
  SettlementItemDto,
  SettlementListQueryDto,
} from './dto';
import {
  ReadyToFulfillResponseDto,
  ReadyToFulfillApiResponseDto,
  ReadyToFulfillSummaryDto,
  ReadyToFulfillRequestDto,
  TokenSummaryDto,
} from './dto/ready-to-fulfill-response.dto';
import {
  FulfillListApiResponseDto as FulfillListApiResponse,
  FulfillListDataDto,
} from './dto/fulfill-list.dto';
import {
  SettlementListApiResponseDto as SettlementListApiResponse,
  SettlementListDataDto,
  SettlementRecordDto,
} from './dto/settlement-list.dto';
import { PaginationMetaDto } from '../common/dto/pagination.dto';
import * as crypto from 'crypto';

@Injectable()
export class SubscribeService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly coboService: CoboService,
  ) {}

  async findAll(
    query: SubscribeQueryDto,
  ): Promise<SubscribeListApiResponseDto> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        paymentStatus,
        checkoutStatus,
        subscribeStatus,
        chainId,
      } = query;

      const skip = (page - 1) * limit;

      // Build where clause for filtering
      const where: any = {};
      if (paymentStatus) {
        where.paymentStatus = paymentStatus;
      }
      if (checkoutStatus) {
        where.checkoutStatus = checkoutStatus;
      }
      if (subscribeStatus) {
        where.subscribeStatus = subscribeStatus;
      }
      if (chainId) {
        where.chainId = chainId;
      }

      // Get total count
      const total = await this.prisma.subscribe.count({ where });

      // Get paginated data
      const subscribes = await this.prisma.subscribe.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          product: {
            select: {
              displayName: true,
              img: true,
            },
          },
          chain: {
            select: {
              id: true,
            },
          },
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      // Transform data to response DTOs
      const data = subscribes.map(
        (subscribe) =>
          new SubscribeResponseDto({
            id: subscribe.id,
            txHash: subscribe.txHash,
            productName: subscribe.product.displayName,
            paymentStatus: subscribe.paymentStatus,
            checkoutStatus: subscribe.checkoutStatus,
            subscribeStatus: subscribe.subscribeStatus,
            amount: subscribe.amount.toString(),
            currency: subscribe.currencies,
            expectedPrice: subscribe.expectedPrice.toString(),
            createdAt: subscribe.createdAt.toISOString(),
          }),
      );

      const listResponse = new SubscribeListResponseDto({
        data,
        meta,
      });

      return new SubscribeListApiResponseDto(
        listResponse,
        'Subscribe requests retrieved successfully',
      );
    } catch (error) {
      console.error('SubscribeService findAll Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching subscribe requests',
      );
    }
  }

  async findAllFulfills(
    query: FulfillListQueryDto,
  ): Promise<FulfillListApiResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        status,
        productId,
      } = query;

      const skip = (page - 1) * limit;

      // Build where clause for filtering
      const where: any = {};
      if (status) {
        where.status = status;
      }
      if (productId) {
        where.productId = productId;
      }

      // Get total count
      const total = await this.prisma.fulfill.count({ where });

      // Get paginated data with relations
      const fulfills = await this.prisma.fulfill.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          product: {
            select: {
              displayName: true,
            },
          },
          subscribes: {
            select: {
              amount: true,
              currencies: true,
            },
          },
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      // Process each fulfill record
      const data = await Promise.all(
        fulfills.map(async (fulfill) => {
          // Calculate total amount from subscribes
          const totalAmount = fulfill.subscribes.reduce(
            (sum, sub) => sum + Number(sub.amount),
            0,
          );

          // Get price from NavManagement for this product and date
          const navManagement = await this.prisma.navManagement.findFirst({
            where: {
              productId: fulfill.productId,
              date: {
                lte: fulfill.cutoffDate,
              },
              status: 'POSTED',
            },
            orderBy: {
              date: 'desc',
            },
          });

          const price = navManagement ? Number(navManagement.price) : null;
          const totalSubscribe =
            price !== null ? totalAmount * price : totalAmount;

          // Calculate tokens breakdown by currency
          const tokenMap = new Map<string, number>();
          fulfill.subscribes.forEach((sub) => {
            const existing = tokenMap.get(sub.currencies) || 0;
            tokenMap.set(sub.currencies, existing + Number(sub.amount));
          });

          const tokens: TokenAmountDto[] = Array.from(tokenMap.entries()).map(
            ([currency, amount]) => ({
              currency,
              amount: amount.toFixed(8),
            }),
          );

          return new FulfillItemDto({
            id: fulfill.id,
            productId: fulfill.productId,
            productName: fulfill.product.displayName,
            cutoffDate: fulfill.cutoffDate.toISOString(),
            status: fulfill.status,
            totalAmount: totalAmount.toFixed(8),
            price: price !== null ? price.toFixed(2) : null,
            totalSubscribe: totalSubscribe.toFixed(2),
            subscribeCount: fulfill.subscribes.length,
            tokens,
            createdAt: fulfill.createdAt.toISOString(),
            updatedAt: fulfill.updatedAt.toISOString(),
          });
        }),
      );

      const listData = new FulfillListDataDto({ data, meta });

      return new FulfillListApiResponse(
        listData,
        'Fulfill records retrieved successfully',
      ) as any;
    } catch (error) {
      console.error('SubscribeService findAllFulfills Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching fulfill records',
      );
    }
  }

  async findAllSettlements(
    query: SettlementListQueryDto,
  ): Promise<SettlementListApiResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        status,
        tokenName,
      } = query;

      const skip = (page - 1) * limit;

      // Build where clause for filtering
      const where: any = {};
      if (status) {
        where.status = status;
      }
      if (tokenName) {
        where.tokenName = tokenName;
      }

      // Get total count
      const total = await this.prisma.settlement.count({ where });

      // Get paginated data
      const settlements = await this.prisma.settlement.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      // Transform data to response DTOs
      const data = settlements.map(
        (settlement) =>
          new SettlementRecordDto({
            id: settlement.id,
            settlementDate: settlement.settlementDate.toISOString(),
            totalFulfill: settlement.totalFulfill,
            totalTransaction: settlement.totalTransaction,
            tokenName: settlement.tokenName,
            amount: settlement.tokenAmount.toString(),
            value: settlement.totalValue.toString(),
            status: settlement.status,
            createdAt: settlement.createdAt.toISOString(),
          }),
      );

      const listData = new SettlementListDataDto({ data, meta });

      return new SettlementListApiResponse(
        listData,
        'Settlement records retrieved successfully',
      ) as any;
    } catch (error) {
      console.error('SubscribeService findAllSettlements Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching settlement records',
      );
    }
  }

  async getReadyToFulfill(date: string): Promise<ReadyToFulfillApiResponseDto> {
    try {
      const dateFilter: any = {};
      if (date) {
        const startDate = new Date(date + 'T00:00:00.000Z');
        const endDate = new Date(date + 'T23:59:59.999Z');

        dateFilter.dateCutoff = {
          gte: startDate,
          lte: endDate,
        };
      }

      const subscribes = await this.prisma.subscribe.findMany({
        where: {
          checkoutStatus: 'READY_TO_FULFILL',
          subscribeStatus: 'ACTIVE',
          ...dateFilter,
        },
        include: {
          product: {
            select: {
              displayName: true,
              img: true,
              price: true,
            },
          },
          wallet: {
            select: {
              walletAddress: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      // Calculate summary data
      const totalAmount = subscribes.reduce(
        (sum, sub) => sum + Number(sub.amount) * Number(sub.product.price),
        0,
      );

      const numOfTransactions = subscribes.length;
      const uniqueWallets = new Set(
        subscribes.map((sub) => sub.wallet.walletAddress),
      );
      const numOfUniqueWallets = uniqueWallets.size;

      const products = Array.from(
        new Set(subscribes.map((sub) => sub.product.displayName)),
      );

      // Calculate token breakdown
      const tokenMap = new Map<
        string,
        { amount: number; tokenAmount: number }
      >();
      subscribes.forEach((sub) => {
        const tokenName = sub.currencies;
        const existing = tokenMap.get(tokenName) || {
          amount: 0,
          tokenAmount: 0,
        };
        const value = Number(sub.amount) * Number(sub.product.price);
        tokenMap.set(tokenName, {
          amount: existing.amount + value,
          tokenAmount: existing.tokenAmount + Number(sub.amount),
        });
      });

      const token: TokenSummaryDto[] = Array.from(tokenMap.entries()).map(
        ([currency, data]) => {
          const percentage =
            totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0;

          return {
            displayName: currency,
            tokenAmount: data.tokenAmount.toFixed(8),
            amount: data.amount.toFixed(2),
            percentage: percentage.toFixed(2),
          };
        },
      );

      const summary: ReadyToFulfillSummaryDto = {
        totalAmount: totalAmount.toFixed(2),
        numOfTransactions,
        numOfUniqueWallets,
        products,
        token,
      };

      // Transform requests data
      const requests: ReadyToFulfillRequestDto[] = subscribes.map((sub) => ({
        txHash: sub.id,
        wallet: sub.wallet.walletAddress,
        product: sub.product.displayName,
        createdAt: sub.createdAt.toISOString(),
        kytStatus: sub.subscribeStatus,
        token: sub.currencies,
        amount: sub.amount.toString(),
        value: (Number(sub.amount) * Number(sub.product.price)).toFixed(2),
      }));

      const readyToFulfillData = new ReadyToFulfillResponseDto({
        summary,
        requests,
      });

      return new ReadyToFulfillApiResponseDto(
        readyToFulfillData,
        'Ready to fulfill requests retrieved successfully',
      );
    } catch (error) {
      console.error('SubscribeService getReadyToFulfill Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching ready-to-fulfill requests',
      );
    }
  }

  async getReadyToSettle(date: string): Promise<ReadyToSettleApiResponseDto> {
    try {
      const dateFilter: any = {};
      if (date) {
        const startDate = new Date(date + 'T00:00:00.000Z');
        const endDate = new Date(date + 'T23:59:59.999Z');

        dateFilter.dateCutoff = {
          gte: startDate,
          lte: endDate,
        };
      }

      const fulfills = await this.prisma.fulfill.findMany({
        where: {
          status: 'POSTED',
          cutoffDate: dateFilter.dateCutoff,
        },
        include: {
          product: {
            select: {
              displayName: true,
              price: true,
            },
          },
          subscribes: {
            include: {
              wallet: {
                select: {
                  walletAddress: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      const subscribes = fulfills.flatMap((fulfill) =>
        fulfill.subscribes.map((subscribe) => ({
          ...subscribe,
          product: fulfill.product,
          fulfillCreatedAt: fulfill.createdAt,
        })),
      );

      const totalAmount = subscribes.reduce(
        (sum, sub) => sum + Number(sub.amount) * Number(sub.product.price),
        0,
      );

      const numOfTransactions = subscribes.length;
      const uniqueWallets = new Set(
        subscribes.map((sub) => sub.wallet?.walletAddress).filter(Boolean),
      );
      const numOfUniqueWallets = uniqueWallets.size;

      const products = Array.from(
        new Set(subscribes.map((sub) => sub.product.displayName)),
      );

      const tokenMap = new Map<
        string,
        { amount: number; tokenAmount: number }
      >();
      subscribes.forEach((sub) => {
        const tokenName = sub.currencies;
        const existing = tokenMap.get(tokenName) || {
          amount: 0,
          tokenAmount: 0,
        };
        const value = Number(sub.amount) * Number(sub.product.price);
        tokenMap.set(tokenName, {
          amount: existing.amount + value,
          tokenAmount: existing.tokenAmount + Number(sub.amount),
        });
      });

      const token: ReadyToSettleTokenSummaryDto[] = Array.from(
        tokenMap.entries(),
      ).map(([currency, data]) => {
        const percentage =
          totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0;

        return {
          displayName: currency,
          tokenAmount: data.tokenAmount.toFixed(8),
          amount: data.amount.toFixed(2),
          percentage: percentage.toFixed(2),
        };
      });

      const summary: ReadyToSettleSummaryDto = {
        totalAmount: totalAmount.toFixed(2),
        numOfTransactions,
        numOfUniqueWallets,
        products,
        token,
      };

      const requests: ReadyToSettleRequestDto[] = subscribes.map((sub) => ({
        txHash: sub.txHash || sub.id,
        walletAddress: sub.wallet?.walletAddress || 'N/A',
        productName: sub.product.displayName,
        timestamp: sub.fulfillCreatedAt.toISOString(),
        status: sub.subscribeStatus,
        token: sub.currencies,
        amount: sub.amount.toString(),
        value: (Number(sub.amount) * Number(sub.product.price)).toFixed(2),
      }));

      const readyToSettleData = new ReadyToSettleResponseDto({
        summary,
        requests,
      });

      return new ReadyToSettleApiResponseDto(
        readyToSettleData,
        'Ready to settle requests retrieved successfully',
      );
    } catch (error) {
      console.error('SubscribeService getReadyToSettle Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching ready-to-settle requests',
      );
    }
  }

  async fulfill(
    request: FulfillRequestDto,
  ): Promise<FulfillApiResponseDto | FulfillOperationsApiResponseDto> {
    try {
      const { date, distributionType } = request;

      const startDate = new Date(date + 'T00:00:00.000Z');
      const endDate = new Date(date + 'T23:59:59.999Z');

      // Get ready to fulfill subscribes for the date range
      const subscribes = await this.prisma.subscribe.findMany({
        where: {
          checkoutStatus: 'READY_TO_FULFILL',
          subscribeStatus: 'ACTIVE',
          dateCutoff: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          product: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      if (subscribes.length === 0) {
        throw new BadRequestException(
          'No ready-to-fulfill subscribes found for the specified date',
        );
      }

      const subscribesByProduct = subscribes.reduce(
        (acc, subscribe) => {
          const productId = subscribe.productId;
          if (!acc[productId]) {
            acc[productId] = {
              product: subscribe.product,
              subscribes: [],
            };
          }
          acc[productId].subscribes.push(subscribe);
          return acc;
        },
        {} as Record<string, { product: any; subscribes: any[] }>,
      );

      // Process each product group
      const fulfillResults: FulfillResponseDto[] = [];

      for (const [productId, group] of Object.entries(subscribesByProduct)) {
        const result = await this.processFulfillForProduct(
          productId,
          group.product,
          group.subscribes,
          endDate,
          distributionType,
        );
        // Extract the data from FulfillApiResponseDto
        fulfillResults.push(result.data);
      }

      // If only one product, return single response
      if (fulfillResults.length === 1) {
        return new FulfillApiResponseDto(
          fulfillResults[0],
          'Fulfill operation completed successfully',
        );
      }

      // Handle multiple products
      const totalProducts = fulfillResults.length;
      const totalSubscribes = fulfillResults.reduce(
        (sum, result) => sum + result.subscribeCount,
        0,
      );
      const overallTotalAmount = fulfillResults
        .reduce((sum, result) => sum + Number(result.totalAmount), 0)
        .toFixed(2);

      const listResponse = new FulfillListResponseDto({
        data: fulfillResults,
        totalProducts,
        totalSubscribes,
        overallTotalAmount,
      });

      return new FulfillOperationsApiResponseDto(
        listResponse,
        `Fulfill operations completed successfully for ${totalProducts} products`,
      );
    } catch (error) {
      console.error('SubscribeService fulfill Error:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'An error occurred while processing fulfill request',
      );
    }
  }

  private async processFulfillForProduct(
    productId: string,
    product: any,
    subscribes: any[],
    cutoffDate: Date,
    distributionType?: string,
  ): Promise<FulfillApiResponseDto> {
    const fulfill = await this.prisma.fulfill.create({
      data: {
        productId,
        cutoffDate,
        status: 'WAITING_FOR_APPROVAL',
        distributionType: distributionType as any,
        notes: `Fulfill operation initiated for ${subscribes.length} subscribes${distributionType ? ` with distribution type: ${distributionType}` : ''}`,
      },
      include: {
        product: true,
      },
    });

    await this.prisma.subscribe.updateMany({
      where: {
        id: {
          in: subscribes.map((s) => s.id),
        },
      },
      data: {
        fulfillId: fulfill.id,
      },
    });

    try {
      const requestIds = subscribes
        .filter((s) => s.requestId)
        .map((s) => s.requestId);

      if (requestIds.length === 0) {
        throw new BadRequestException(
          'No valid request IDs found in subscribes',
        );
      }

      const priceIds = new Array(requestIds.length).fill(1);

      // Generate unique request ID for Cobo transaction
      const requestId = `set-price-subscribe${fulfill.id}-${crypto.randomUUID()}`;
      const chainId = process.env.CHAIN_ID || 'SETH';
      const coboWalletId = process.env.COBO_WALLET_ID;
      const coboWalletAddress = process.env.COBO_WALLET_ADDRESS;
      const gatewayContractAddress = process.env.GATEWAY_CONTRACT_ADDRESS;

      if (!coboWalletId || !gatewayContractAddress) {
        throw new InternalServerErrorException(
          'Cobo wallet ID or Gateway contract configuration is missing',
        );
      }

      const coboResult = await this.coboService.createContractCallTransaction({
        request_id: requestId,
        chain_id: chainId,
        source: {
          source_type: 'Org-Controlled',
          wallet_id: coboWalletId,
          address: coboWalletAddress,
        },
        destination: {
          destination_type: 'EVM_Contract',
          address: gatewayContractAddress,
          calldata: this.encodeSetPriceForRequestSubscribe(
            requestIds,
            priceIds,
          ),
        },
      });

      const updatedFulfill = await this.prisma.fulfill.update({
        where: { id: fulfill.id },
        data: {
          status: 'WAITING_FOR_APPROVAL',
          notes: `Cobo transaction submitted successfully. Transaction ID: ${coboResult.transaction_id}`,
        },
        include: {
          product: true,
        },
      });

      try {
        await this.prisma.coboTransaction.create({
          data: {
            id: requestId,
            fulfillId: fulfill.id,
            walletType: 'Org-Controlled',
            walletAddress: coboWalletAddress,
            chainId: chainId,
            kind: 'CONTRACT_CALL',
            toAddress: gatewayContractAddress,
            method: 'setPriceForRequestSubscribe',
            calldata: this.encodeSetPriceForRequestSubscribe(
              requestIds,
              priceIds,
            ),
            value: '0',
            status: 'DRAFT',
            coboTransactionId: coboResult.transaction_id,
            idempotencyKey: requestId,
            rawRequest: {
              request_id: requestId,
              chain_id: chainId,
              source: {
                source_type: 'Org-Controlled',
                wallet_id: coboWalletId,
                address: coboWalletAddress,
              },
              destination: {
                destination_type: 'EVM_Contract',
                address: gatewayContractAddress,
                calldata: this.encodeSetPriceForRequestSubscribe(
                  requestIds,
                  priceIds,
                ),
              },
            },
            rawResponse: coboResult as any,
          },
        });
      } catch (coboTransactionError) {
        console.error(
          'Failed to create CoboTransaction record:',
          coboTransactionError,
        );
      }

      // Calculate total amount
      const totalAmount = subscribes.reduce(
        (sum, sub) => sum + Number(sub.amount) * Number(product.price),
        0,
      );

      const response = new FulfillResponseDto({
        id: updatedFulfill.id,
        productId: updatedFulfill.productId,
        productName: updatedFulfill.product.displayName,
        cutoffDate: updatedFulfill.cutoffDate.toISOString(),
        subscribeCount: subscribes.length,
        totalAmount: totalAmount.toFixed(2),
        status: 'DRAFT',
        notes: updatedFulfill.notes,
        coboTransactionId: coboResult.transaction_id,
        createdAt: updatedFulfill.createdAt.toISOString(),
        updatedAt: updatedFulfill.updatedAt.toISOString(),
      });

      return new FulfillApiResponseDto(
        response,
        'Fulfill operation completed successfully',
      );
    } catch (error) {
      // Update fulfill status to FAILED if Cobo call fails
      await this.prisma.fulfill.update({
        where: { id: fulfill.id },
        data: {
          status: 'POSTING_ERROR',
          notes: `Fulfill operation failed: ${error.message}`,
        },
      });

      throw error;
    }
  }

  private encodeSetPriceForRequestSubscribe(
    requestIds: string[],
    priceIds: number[],
  ): string {
    try {
      const formattedRequestIds = requestIds.map((id) => id as `0x${string}`);

      const formattedPriceIds = priceIds.map((id) => BigInt(id));

      const calldata = encodeFunctionData({
        abi: [
          {
            type: 'function',
            name: 'setPriceForRequestSubscribe',
            inputs: [
              { type: 'bytes32[]', name: 'requestIds' },
              { type: 'uint256[]', name: 'priceIds' },
            ],
            outputs: [],
            stateMutability: 'nonpayable',
          },
        ],
        functionName: 'setPriceForRequestSubscribe',
        args: [formattedRequestIds, formattedPriceIds],
      });

      return calldata;
    } catch (error) {
      console.error(
        'Failed to encode setPriceForRequestSubscribe function:',
        error,
      );
      throw new InternalServerErrorException(
        'Failed to encode contract function call',
      );
    }
  }

  async settle(request: SettleRequestDto): Promise<SettleApiResponseDto> {
    try {
      const { date } = request;
      const settlementDate = new Date(date + 'T00:00:00.000Z');

      // 1. Get ready to settle data using the same logic as getReadyToSettle function
      const readyToSettleData = await this.getReadyToSettle(date);

      if (readyToSettleData.data.requests.length === 0) {
        throw new BadRequestException(
          'No ready-to-settle fulfills found for the specified date',
        );
      }

      // 2. Get fulfills with POSTED status for the date (same logic as getReadyToSettle)
      const fulfills = await this.prisma.fulfill.findMany({
        where: {
          status: 'POSTED',
          cutoffDate: {
            gte: settlementDate,
            lte: new Date(date + 'T23:59:59.999Z'),
          },
        },
        include: {
          subscribes: {
            select: {
              id: true,
              requestId: true,
              currencies: true,
              amount: true,
              productId: true,
            },
          },
          product: {
            select: {
              displayName: true,
              price: true,
            },
          },
        },
      });

      if (fulfills.length === 0) {
        throw new BadRequestException(
          'No POSTED fulfills found for the specified date',
        );
      }

      // 3. Group subscribes by currency/token from ALL fulfills
      const subscribesByToken = new Map<
        string,
        {
          subscribes: any[];
          fulfills: any[];
          tokenName: string;
        }
      >();

      fulfills.forEach((fulfill) => {
        fulfill.subscribes.forEach((subscribe) => {
          const tokenName = subscribe.currencies;

          if (!subscribesByToken.has(tokenName)) {
            subscribesByToken.set(tokenName, {
              subscribes: [],
              fulfills: [],
              tokenName,
            });
          }

          const tokenGroup = subscribesByToken.get(tokenName)!;

          // Add subscribe if not already added (avoid duplicates)
          if (!tokenGroup.subscribes.find((s) => s.id === subscribe.id)) {
            tokenGroup.subscribes.push(subscribe);
          }

          // Add fulfill if not already added
          if (!tokenGroup.fulfills.find((f) => f.id === fulfill.id)) {
            tokenGroup.fulfills.push(fulfill);
          }
        });
      });

      const settlements: SettlementItemDto[] = [];
      let totalFulfills = 0;
      let totalTransactions = 0;
      let totalSettlementValue = 0;

      // 4. Process each token group separately
      for (const [tokenName, tokenGroup] of subscribesByToken.entries()) {
        // Calculate token totals
        const tokenAmount = tokenGroup.subscribes.reduce(
          (sum, sub) => sum + Number(sub.amount),
          0,
        );

        const totalValue = tokenGroup.subscribes.reduce((sum, sub) => {
          // Get price from the fulfill's product
          const fulfill = tokenGroup.fulfills.find((f) =>
            f.subscribes.some((s) => s.id === sub.id),
          );
          const price = fulfill ? Number(fulfill.product.price) : 0;
          return sum + Number(sub.amount) * price;
        }, 0);

        // 5. Get all requestIds for this token group (from ALL fulfills)
        const requestIds = tokenGroup.subscribes
          .filter((sub) => sub.requestId)
          .map((sub) => sub.requestId);

        if (requestIds.length === 0) {
          console.warn(`No valid request IDs found for token: ${tokenName}`);
          continue;
        }

        // Create settlement record
        const settlement = await this.prisma.settlement.create({
          data: {
            settlementDate,
            totalFulfill: tokenGroup.fulfills.length,
            totalTransaction: tokenGroup.subscribes.length,
            tokenName,
            tokenAmount,
            totalValue,
            status: 'WAITING_FOR_APPROVAL',
            fulfillIds: tokenGroup.fulfills.map((f) => f.id),
            subscribeIds: tokenGroup.subscribes.map((s) => s.id),
          },
        });

        // 6. Call Cobo settleSubscribe function with requestIds grouped by currency
        try {
          await this.callCoboSettleSubscribe(requestIds, settlement.id);

          // Update settlement status
          await this.prisma.settlement.update({
            where: { id: settlement.id },
            data: {
              status: 'WAITING_FOR_APPROVAL',
            },
          });

          settlements.push(
            new SettlementItemDto({
              id: settlement.id,
              settlementDate: settlement.settlementDate.toISOString(),
              totalFulfill: settlement.totalFulfill,
              totalTransaction: settlement.totalTransaction,
              tokenName: settlement.tokenName,
              tokenAmount: settlement.tokenAmount.toString(),
              totalValue: settlement.totalValue.toString(),
              status: settlement.status,
              fulfillIds: settlement.fulfillIds,
              subscribeIds: settlement.subscribeIds,
              createdAt: settlement.createdAt.toISOString(),
            }),
          );

          totalFulfills += settlement.totalFulfill;
          totalTransactions += settlement.totalTransaction;
          totalSettlementValue += Number(settlement.totalValue);
        } catch (coboError) {
          console.error(
            `Cobo settlement failed for token ${tokenName}:`,
            coboError,
          );

          await this.prisma.settlement.update({
            where: { id: settlement.id },
            data: {
              status: 'POSTING_ERROR',
            },
          });

          settlements.push(
            new SettlementItemDto({
              id: settlement.id,
              settlementDate: settlement.settlementDate.toISOString(),
              totalFulfill: settlement.totalFulfill,
              totalTransaction: settlement.totalTransaction,
              tokenName: settlement.tokenName,
              tokenAmount: settlement.tokenAmount.toString(),
              totalValue: settlement.totalValue.toString(),
              status: 'POSTING_ERROR',
              fulfillIds: settlement.fulfillIds,
              subscribeIds: settlement.subscribeIds,
              createdAt: settlement.createdAt.toISOString(),
            }),
          );
        }
      }

      const settleResponse = new SettleResponseDto({
        settlements,
        totalFulfills,
        totalTransactions,
        totalSettlementValue: totalSettlementValue.toFixed(2),
      });

      return new SettleApiResponseDto(
        settleResponse,
        `Settlement operation completed successfully for ${settlements.length} token groups`,
      );
    } catch (error) {
      console.error('SubscribeService settle Error:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'An error occurred while processing settlement request',
      );
    }
  }

  private async callCoboSettleSubscribe(
    requestIds: string[],
    settlementId: string,
  ): Promise<any> {
    try {
      // Generate unique request ID for Cobo transaction
      const requestId = `settle-subscribe-${crypto.randomUUID()}`;
      const chainId = process.env.CHAIN_ID || 'SETH';
      const coboWalletId = process.env.COBO_WALLET_ID;
      const coboWalletAddress = process.env.COBO_WALLET_ADDRESS;
      const gatewayContractAddress = process.env.GATEWAY_CONTRACT_ADDRESS;

      if (!coboWalletId || !gatewayContractAddress) {
        throw new InternalServerErrorException(
          'Cobo wallet ID or Gateway contract configuration is missing',
        );
      }

      const coboResult = await this.coboService.createContractCallTransaction({
        request_id: requestId,
        chain_id: chainId,
        source: {
          source_type: 'Org-Controlled',
          wallet_id: coboWalletId,
          address: coboWalletAddress,
        },
        destination: {
          destination_type: 'EVM_Contract',
          address: gatewayContractAddress,
          calldata: this.encodeSettleSubscribe(requestIds),
        },
      });

      // Create CoboTransaction record for settlement
      try {
        await this.prisma.coboTransaction.create({
          data: {
            id: requestId,
            settlementId: settlementId,
            walletType: 'Org-Controlled',
            walletAddress: coboWalletAddress,
            chainId: chainId,
            kind: 'CONTRACT_CALL',
            toAddress: gatewayContractAddress,
            method: 'settleSubscribe',
            calldata: this.encodeSettleSubscribe(requestIds),
            value: '0',
            status: 'SUBMITTED',
            coboTransactionId: coboResult.transaction_id,
            idempotencyKey: requestId,
            rawRequest: {
              request_id: requestId,
              chain_id: chainId,
              source: {
                source_type: 'Org-Controlled',
                wallet_id: coboWalletId,
                address: coboWalletAddress,
              },
              destination: {
                destination_type: 'EVM_Contract',
                address: gatewayContractAddress,
                calldata: this.encodeSettleSubscribe(requestIds),
              },
            },
            rawResponse: coboResult as any,
          },
        });
      } catch (coboTransactionError) {
        console.error(
          'Failed to create CoboTransaction record for settlement:',
          coboTransactionError,
        );
      }

      return coboResult;
    } catch (error) {
      console.error('Failed to call Cobo settleSubscribe:', error);
      throw new InternalServerErrorException(
        'Failed to process settlement with Cobo',
      );
    }
  }

  private encodeSettleSubscribe(requestIds: string[]): string {
    try {
      const formattedRequestIds = requestIds.map((id) => id as `0x${string}`);

      const calldata = encodeFunctionData({
        abi: [
          {
            type: 'function',
            name: 'settleSubscribe',
            inputs: [
              {
                internalType: 'bytes32[]',
                name: 'requestId',
                type: 'bytes32[]',
              },
            ],
            outputs: [],
            stateMutability: 'nonpayable',
          },
        ],
        functionName: 'settleSubscribe',
        args: [formattedRequestIds],
      });

      return calldata;
    } catch (error) {
      console.error('Failed to encode settleSubscribe function:', error);
      throw new InternalServerErrorException(
        'Failed to encode contract function call',
      );
    }
  }
}
