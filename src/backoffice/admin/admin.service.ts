import {
  Injectable,
  Logger,
  ConflictException,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { RegisterAdminDto, AdminStatus } from './dto/register-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);
  private readonly SALT_ROUNDS = 12;

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Register a new admin with role assignment
   */
  async register(
    registerAdminDto: RegisterAdminDto,
  ): Promise<BaseResponseDto<AdminResponseDto>> {
    try {
      await this.validateRegisterRequest(registerAdminDto);
      const adminData =
        await this.prepareAdminDataForCreation(registerAdminDto);
      const admin = await this.createAdminInDatabase(adminData);
      const adminResponse = await this.mapAdminToResponseDto(admin);

      return BaseResponseDto.success(
        adminResponse,
        'Admin registered successfully',
      );
    } catch (error) {
      return this.handleRegisterError(error);
    }
  }

  /**
   * Validate admin registration request
   */
  private async validateRegisterRequest(
    registerAdminDto: RegisterAdminDto,
  ): Promise<void> {
    await this.validateRoleExists(registerAdminDto.roleId);
    await this.checkAdminExists(
      registerAdminDto.email,
      registerAdminDto.username,
    );
  }

  /**
   * Validate that the role exists
   */
  private async validateRoleExists(roleId: string): Promise<void> {
    const role = await this.prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException('Role not found');
    }
  }

  /**
   * Check if admin already exists by email or username
   */
  private async checkAdminExists(
    email: string,
    username: string,
  ): Promise<void> {
    const [existingAdminByEmail, existingAdminByUsername] = await Promise.all([
      this.prisma.admin.findUnique({ where: { email: email.toLowerCase() } }),
      this.prisma.admin.findUnique({ where: { username } }),
    ]);

    if (existingAdminByEmail || existingAdminByUsername) {
      throw new ConflictException(
        'Admin with this email or username already exists',
      );
    }
  }

  /**
   * Prepare admin data for creation
   */
  private async prepareAdminDataForCreation(
    registerAdminDto: RegisterAdminDto,
  ): Promise<any> {
    const hashedPassword = await this.hashPassword(registerAdminDto.password);

    return {
      email: registerAdminDto.email.toLowerCase(),
      username: registerAdminDto.username,
      password: hashedPassword,
      roleId: registerAdminDto.roleId,
      status: registerAdminDto.status || AdminStatus.ACTIVE,
    };
  }

  /**
   * Create admin in database
   */
  private async createAdminInDatabase(adminData: any): Promise<any> {
    return this.prisma.admin.create({
      data: adminData,
      include: {
        role: true, // Include role data for response
      },
    });
  }

  /**
   * Map admin entity to response DTO with role name
   */
  private async mapAdminToResponseDto(admin: any): Promise<AdminResponseDto> {
    return new AdminResponseDto({
      id: admin.id,
      email: admin.email,
      username: admin.username,
      roleId: admin.roleId,
      roleName: admin.role.name,
      status: admin.status as AdminStatus,
      createdAt: admin.createdAt,
      updatedAt: admin.updatedAt,
      password: admin.password, // Will be excluded by @Exclude() decorator
    });
  }

  /**
   * Handle registration errors
   */
  private handleRegisterError(error: any): never {
    if (error.code === 'P2002') {
      throw new ConflictException(
        'Admin with this email or username already exists',
      );
    }
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }
    this.handleServiceError(error, 'Failed to register admin');
  }

  /**
   * Find an admin by email
   */
  async findByEmail(email: string): Promise<BaseResponseDto<AdminResponseDto>> {
    try {
      // Validate email format
      if (!email || typeof email !== 'string' || !this.isValidEmail(email)) {
        throw new BadRequestException('Invalid email format');
      }

      const admin = await this.prisma.admin.findUnique({
        where: { email: email.toLowerCase() },
        include: {
          role: true,
        },
      });

      if (!admin) {
        throw new NotFoundException('Admin not found');
      }

      const adminResponse = await this.mapAdminToResponseDto(admin);

      return BaseResponseDto.success(
        adminResponse,
        'Admin retrieved successfully',
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.handleServiceError(error, 'Failed to retrieve admin');
    }
  }

  /**
   * Get all admins with pagination
   */
  async findAll(
    page: number = 1,
    limit: number = 10,
    sortBy: string = 'createdAt',
    sortOrder: 'asc' | 'desc' = 'desc',
  ): Promise<BaseResponseDto<{ data: AdminResponseDto[]; meta: any }>> {
    try {
      const skip = (page - 1) * limit;

      const total = await this.prisma.admin.count();

      const admins = await this.prisma.admin.findMany({
        skip,
        take: limit,
        include: {
          role: true,
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      const adminResponseDtos = await Promise.all(
        admins.map((admin) => this.mapAdminToResponseDto(admin)),
      );

      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      const response = {
        data: adminResponseDtos,
        meta,
      };

      return BaseResponseDto.success(response, 'Admins retrieved successfully');
    } catch (error) {
      this.handleServiceError(error, 'Failed to retrieve admins');
    }
  }

  /**
   * Hash password using bcrypt
   */
  private async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, this.SALT_ROUNDS);
    } catch (error) {
      this.logger.error('Failed to hash password:', error);
      throw new InternalServerErrorException('Failed to hash password');
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Handle service errors with proper logging and exception throwing
   */
  private handleServiceError(error: any, context: string): never {
    if (
      error instanceof ConflictException ||
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    }

    this.logger.error(`${context}:`, error);
    throw new InternalServerErrorException(
      'An error occurred while processing your request',
    );
  }
}
