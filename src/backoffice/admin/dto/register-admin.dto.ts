import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsString,
  Matches,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum AdminStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  FREEZE = 'FREEZE',
  DEACTIVE = 'DEACTIVE',
}

/**
 * DTO for admin registration request
 * Includes all required fields for creating a new admin with role assignment
 */
export class RegisterAdminDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the admin',
  })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  @Transform(({ value }) => value?.toLowerCase()?.trim())
  email: string;

  @ApiProperty({
    example: 'admin123',
    description: 'Unique username for the admin',
  })
  @IsString({ message: 'Username must be a string' })
  @IsNotEmpty({ message: 'Username is required' })
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(50, { message: 'Username must not exceed 50 characters' })
  @Matches(/^\w+$/, {
    message: 'Username can only contain letters, numbers, and underscores',
  })
  @Transform(({ value }) => value?.trim())
  username: string;

  @ApiProperty({
    example: 'SecurePass123!',
    description:
      'Password with minimum 8 characters, including uppercase, lowercase, and number',
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*/, {
    message:
      'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  })
  password: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Role ID to assign to the admin',
  })
  @IsUUID('4', { message: 'Role ID must be a valid UUID v4' })
  @IsNotEmpty({ message: 'Role ID is required' })
  roleId: string;

  @ApiProperty({
    enum: AdminStatus,
    example: AdminStatus.ACTIVE,
    description: 'Initial status of the admin account',
    required: false,
    default: AdminStatus.ACTIVE,
  })
  @IsEnum(AdminStatus, {
    message: 'Status must be one of: ACTIVE, PENDING, FREEZE, DEACTIVE',
  })
  @IsOptional()
  status?: AdminStatus = AdminStatus.ACTIVE;
}
