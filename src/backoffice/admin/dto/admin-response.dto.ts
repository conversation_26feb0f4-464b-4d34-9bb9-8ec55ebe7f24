import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

export enum AdminStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  FREEZE = 'FREEZE',
  DEACTIVE = 'DEACTIVE',
}

/**
 * Admin response DTO for admin registration and retrieval
 * Excludes sensitive information like password and includes role name
 */
export class AdminResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the admin',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @ApiProperty({
    description: 'Email address of the admin',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Username of the admin',
    example: 'admin123',
  })
  username: string;

  @ApiProperty({
    description: 'Role ID assigned to the admin',
    example: '550e8400-e29b-41d4-a716-************',
  })
  roleId: string;

  @ApiProperty({
    description: 'Role name assigned to the admin',
    example: 'Super Admin',
  })
  roleName: string;

  @ApiProperty({
    enum: AdminStatus,
    description: 'Current status of the admin account',
    example: AdminStatus.ACTIVE,
  })
  status: AdminStatus;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt: Date;

  // Exclude password from response for security
  @Exclude()
  password: string;

  constructor(partial: Partial<AdminResponseDto>) {
    Object.assign(this, partial);
  }
}
