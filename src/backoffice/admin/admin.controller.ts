import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AdminService } from './admin.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { RegisterAdminDto } from './dto/register-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import { BaseBackofficeController } from '../common/base-backoffice.controller';

@ApiTags('Backoffice - Admin')
@Controller('backoffice/admin')
export class AdminController extends BaseBackofficeController {
  constructor(private readonly adminService: AdminService) {
    super();
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register a new admin',
    description:
      'Create a new admin account with role assignment. Password will be automatically hashed.',
  })
  @ApiBody({
    type: RegisterAdminDto,
    description: 'Admin registration data',
  })
  @ApiResponse({
    status: 201,
    description: 'Admin registered successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/AdminResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - role not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - admin with email or username already exists',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async register(
    @Body() registerAdminDto: RegisterAdminDto,
  ): Promise<BaseResponseDto<AdminResponseDto>> {
    return await this.adminService.register(registerAdminDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all admins',
    description:
      'Retrieve a paginated list of all admins in the system for backoffice administration.',
  })
  @ApiResponse({
    status: 200,
    description: 'Admins retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/AdminResponseDto' },
                },
                meta: {
                  type: 'object',
                  properties: {
                    page: { type: 'number', example: 1 },
                    limit: { type: 'number', example: 10 },
                    total: { type: 'number', example: 25 },
                    totalPages: { type: 'number', example: 3 },
                    hasNext: { type: 'boolean', example: true },
                    hasPrev: { type: 'boolean', example: false },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['id', 'email', 'username', 'status', 'createdAt', 'updatedAt'],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
  ): Promise<BaseResponseDto<{ data: AdminResponseDto[]; meta: any }>> {
    return await this.adminService.findAll(page, limit, sortBy, sortOrder);
  }

  @Get('by-email')
  @ApiOperation({
    summary: 'Get admin by email',
    description:
      'Retrieve an admin by their email address. Password field is excluded from the response.',
  })
  @ApiQuery({
    name: 'email',
    required: true,
    type: String,
    description: 'Email address of the admin to retrieve',
    example: '<EMAIL>',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/AdminResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid email format',
  })
  @ApiResponse({
    status: 404,
    description: 'Admin not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findByEmail(
    @Query('email') email: string,
  ): Promise<BaseResponseDto<AdminResponseDto>> {
    return await this.adminService.findByEmail(email);
  }
}
