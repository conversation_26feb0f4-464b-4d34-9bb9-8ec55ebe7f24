import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from './admin.service';
import { PrismaService } from '../../prisma/prisma.service';
import {
  ConflictException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { RegisterAdminDto, AdminStatus } from './dto/register-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import * as bcrypt from 'bcrypt';

describe('AdminService', () => {
  let service: AdminService;

  const mockPrismaService = {
    role: {
      findUnique: jest.fn(),
    },
    admin: {
      findUnique: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const validRegisterAdminDto: RegisterAdminDto = {
      email: '<EMAIL>',
      username: 'admin123',
      password: 'SecurePass123!',
      roleId: '550e8400-e29b-41d4-a716-446655440000',
      status: AdminStatus.ACTIVE,
    };

    const mockRole = {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Super Admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockAdmin = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      email: '<EMAIL>',
      username: 'admin123',
      password: 'hashedPassword',
      roleId: '550e8400-e29b-41d4-a716-446655440000',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: mockRole,
    };

    it('should successfully register a new admin', async () => {
      // Mock role exists
      mockPrismaService.role.findUnique.mockResolvedValue(mockRole);

      // Mock admin doesn't exist
      mockPrismaService.admin.findUnique
        .mockResolvedValueOnce(null) // email check
        .mockResolvedValueOnce(null); // username check

      // Mock password hashing
      jest.spyOn(bcrypt, 'hash' as any).mockResolvedValue('hashedPassword');

      // Mock admin creation
      mockPrismaService.admin.create.mockResolvedValue(mockAdmin);

      const result = await service.register(validRegisterAdminDto);

      expect(result.status).toBe('success');
      expect(result.message).toBe('Admin registered successfully');
      expect(result.data).toBeInstanceOf(AdminResponseDto);
      expect(result.data.email).toBe(validRegisterAdminDto.email);
      expect(result.data.username).toBe(validRegisterAdminDto.username);
      expect(result.data.roleName).toBe(mockRole.name);
      expect(result.data.password).toBeUndefined(); // Password should be excluded
    });

    it('should throw NotFoundException when role does not exist', async () => {
      mockPrismaService.role.findUnique.mockResolvedValue(null);

      await expect(service.register(validRegisterAdminDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockPrismaService.role.findUnique).toHaveBeenCalledWith({
        where: { id: validRegisterAdminDto.roleId },
      });
    });

    it('should throw ConflictException when admin with email already exists', async () => {
      mockPrismaService.role.findUnique.mockResolvedValue(mockRole);

      const existingAdmin = {
        id: 'existing-id',
        email: validRegisterAdminDto.email,
      };
      mockPrismaService.admin.findUnique
        .mockResolvedValueOnce(existingAdmin) // email check
        .mockResolvedValueOnce(null); // username check

      await expect(service.register(validRegisterAdminDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should throw ConflictException when admin with username already exists', async () => {
      mockPrismaService.role.findUnique.mockResolvedValue(mockRole);

      mockPrismaService.admin.findUnique
        .mockResolvedValueOnce(null) // email check
        .mockResolvedValueOnce({
          id: 'existing-id',
          username: validRegisterAdminDto.username,
        }); // username check

      await expect(service.register(validRegisterAdminDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should handle database constraint violation', async () => {
      mockPrismaService.role.findUnique.mockResolvedValue(mockRole);
      mockPrismaService.admin.findUnique
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null);

      jest.spyOn(bcrypt, 'hash' as any).mockResolvedValue('hashedPassword');

      const dbError = { code: 'P2002' };
      mockPrismaService.admin.create.mockRejectedValue(dbError);

      await expect(service.register(validRegisterAdminDto)).rejects.toThrow(
        ConflictException,
      );
    });

    it('should convert email to lowercase', async () => {
      const registerDtoWithUppercase = {
        ...validRegisterAdminDto,
        email: '<EMAIL>',
      };

      mockPrismaService.role.findUnique.mockResolvedValue(mockRole);
      mockPrismaService.admin.findUnique
        .mockResolvedValue(null)
        .mockResolvedValue(null);
      jest.spyOn(bcrypt, 'hash' as any).mockResolvedValue('hashedPassword');
      mockPrismaService.admin.create.mockResolvedValue(mockAdmin);

      await service.register(registerDtoWithUppercase);

      expect(mockPrismaService.admin.create).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>', // Should be lowercase
        }),
      );
    });
  });

  describe('findByEmail', () => {
    const email = '<EMAIL>';
    const mockRole = {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Super Admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockAdmin = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      email: '<EMAIL>',
      username: 'admin123',
      password: 'hashedPassword',
      roleId: '550e8400-e29b-41d4-a716-446655440000',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      role: mockRole,
    };

    it('should successfully find admin by email', async () => {
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);

      const result = await service.findByEmail(email);

      expect(result.status).toBe('success');
      expect(result.message).toBe('Admin retrieved successfully');
      expect(result.data).toBeInstanceOf(AdminResponseDto);
      expect(result.data.email).toBe(email);
      expect(result.data.roleName).toBe(mockRole.name);
    });

    it('should throw BadRequestException for invalid email format', async () => {
      const invalidEmail = 'invalid-email';

      await expect(service.findByEmail(invalidEmail)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw NotFoundException when admin not found', async () => {
      mockPrismaService.admin.findUnique.mockResolvedValue(null);

      await expect(service.findByEmail(email)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should convert email to lowercase for search', async () => {
      const uppercaseEmail = '<EMAIL>';
      mockPrismaService.admin.findUnique.mockResolvedValue(mockAdmin);

      await service.findByEmail(uppercaseEmail);

      expect(mockPrismaService.admin.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        include: { role: true },
      });
    });
  });

  describe('findAll', () => {
    const mockRole = {
      id: '550e8400-e29b-41d4-a716-446655440000',
      name: 'Super Admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockAdmins = [
      {
        id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        email: '<EMAIL>',
        username: 'admin1',
        password: 'hashedPassword1',
        roleId: mockRole.id,
        status: AdminStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        role: mockRole,
      },
      {
        id: 'b2c3d4e5-f6a7-8901-bcde-f23456789012',
        email: '<EMAIL>',
        username: 'admin2',
        password: 'hashedPassword2',
        roleId: mockRole.id,
        status: AdminStatus.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        role: mockRole,
      },
    ];

    it('should return paginated list of admins', async () => {
      mockPrismaService.admin.count.mockResolvedValue(2);
      mockPrismaService.admin.findMany.mockResolvedValue(mockAdmins);

      const result = await service.findAll(1, 10, 'createdAt', 'desc');

      expect(result.status).toBe('success');
      expect(result.message).toBe('Admins retrieved successfully');
      expect(result.data.data).toHaveLength(2);
      expect(result.data.meta).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it('should calculate pagination correctly', async () => {
      mockPrismaService.admin.count.mockResolvedValue(25);
      mockPrismaService.admin.findMany.mockResolvedValue(mockAdmins);

      const result = await service.findAll(2, 10, 'createdAt', 'desc');

      expect(result.data.meta).toEqual({
        page: 2,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });
    });

    it('should use default parameters when not provided', async () => {
      mockPrismaService.admin.count.mockResolvedValue(0);
      mockPrismaService.admin.findMany.mockResolvedValue([]);

      await service.findAll();

      expect(mockPrismaService.admin.findMany).toHaveBeenCalledWith({
        skip: 0, // (1-1)*10
        take: 10,
        include: { role: true },
        orderBy: { createdAt: 'desc' },
      });
    });
  });
});
