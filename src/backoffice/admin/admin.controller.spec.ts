import { Test, TestingModule } from '@nestjs/testing';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { RegisterAdminDto, AdminStatus } from './dto/register-admin.dto';
import { AdminResponseDto } from './dto/admin-response.dto';
import {
  BaseResponseDto,
  ResponseStatus,
} from '../nav-management/dto/base-response.dto';

describe('AdminController', () => {
  let controller: AdminController;
  let adminService: AdminService;

  const mockAdminService = {
    register: jest.fn(),
    findAll: jest.fn(),
    findByEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: AdminService,
          useValue: mockAdminService,
        },
      ],
    }).compile();

    controller = module.get<AdminController>(AdminController);
    adminService = module.get<AdminService>(AdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const validRegisterAdminDto: RegisterAdminDto = {
      email: '<EMAIL>',
      username: 'admin123',
      password: 'SecurePass123!',
      roleId: '550e8400-e29b-41d4-a716-************',
    };

    const mockAdminResponse: AdminResponseDto = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      email: '<EMAIL>',
      username: 'admin123',
      roleId: '550e8400-e29b-41d4-a716-************',
      roleName: 'Super Admin',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      password: 'hashedPassword', // This will be excluded in response
    };

    const mockSuccessResponse: BaseResponseDto<AdminResponseDto> = {
      code: 200,
      status: ResponseStatus.SUCCESS,
      message: 'Admin registered successfully',
      data: mockAdminResponse,
      timestamp: new Date().toISOString(),
    };

    it('should successfully register a new admin', async () => {
      mockAdminService.register.mockResolvedValue(mockSuccessResponse);

      const result = await controller.register(validRegisterAdminDto);

      expect(result).toEqual(mockSuccessResponse);
      expect(adminService.register).toHaveBeenCalledWith(validRegisterAdminDto);
    });

    it('should pass DTO validation errors to service', async () => {
      const invalidDto = {
        email: 'invalid-email',
        username: 'ab', // too short
        password: '123', // too weak
        roleId: 'invalid-uuid',
      };

      // Validation is handled by NestJS ValidationPipe
      // This test ensures the controller passes the DTO to the service
      mockAdminService.register.mockResolvedValue(mockSuccessResponse);

      await controller.register(invalidDto as any);

      expect(adminService.register).toHaveBeenCalledWith(invalidDto);
    });
  });

  describe('findAll', () => {
    const mockAdminResponse1: AdminResponseDto = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      email: '<EMAIL>',
      username: 'admin1',
      roleId: '550e8400-e29b-41d4-a716-************',
      roleName: 'Super Admin',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      password: 'hashedPassword1',
    };

    const mockAdminResponse2: AdminResponseDto = {
      id: 'b2c3d4e5-f6a7-8901-bcde-f23456789012',
      email: '<EMAIL>',
      username: 'admin2',
      roleId: '550e8400-e29b-41d4-a716-************',
      roleName: 'Admin',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      password: 'hashedPassword2',
    };

    const mockFindAllResponse: BaseResponseDto<{
      data: AdminResponseDto[];
      meta: any;
    }> = {
      code: 200,
      status: ResponseStatus.SUCCESS,
      message: 'Admins retrieved successfully',
      data: {
        data: [mockAdminResponse1, mockAdminResponse2],
        meta: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      },
      timestamp: new Date().toISOString(),
    };

    it('should return paginated list of admins', async () => {
      mockAdminService.findAll.mockResolvedValue(mockFindAllResponse);

      const result = await controller.findAll(1, 10, 'createdAt', 'desc');

      expect(result).toEqual(mockFindAllResponse);
      expect(adminService.findAll).toHaveBeenCalledWith(
        1,
        10,
        'createdAt',
        'desc',
      );
    });

    it('should use default parameters when not provided', async () => {
      mockAdminService.findAll.mockResolvedValue(mockFindAllResponse);

      await controller.findAll();

      expect(adminService.findAll).toHaveBeenCalledWith(
        undefined,
        undefined,
        undefined,
        undefined,
      );
    });

    it('should handle custom pagination parameters', async () => {
      mockAdminService.findAll.mockResolvedValue(mockFindAllResponse);

      await controller.findAll(2, 5, 'email', 'asc');

      expect(adminService.findAll).toHaveBeenCalledWith(2, 5, 'email', 'asc');
    });
  });

  describe('findByEmail', () => {
    const email = '<EMAIL>';
    const mockAdminResponse: AdminResponseDto = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      email: '<EMAIL>',
      username: 'admin123',
      roleId: '550e8400-e29b-41d4-a716-************',
      roleName: 'Super Admin',
      status: AdminStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date(),
      password: 'hashedPassword',
    };

    const mockFindByEmailResponse: BaseResponseDto<AdminResponseDto> = {
      code: 200,
      status: ResponseStatus.SUCCESS,
      message: 'Admin retrieved successfully',
      data: mockAdminResponse,
      timestamp: new Date().toISOString(),
    };

    it('should successfully find admin by email', async () => {
      mockAdminService.findByEmail.mockResolvedValue(mockFindByEmailResponse);

      const result = await controller.findByEmail(email);

      expect(result).toEqual(mockFindByEmailResponse);
      expect(adminService.findByEmail).toHaveBeenCalledWith(email);
    });

    it('should handle email parameter correctly', async () => {
      mockAdminService.findByEmail.mockResolvedValue(mockFindByEmailResponse);

      const testEmail = '<EMAIL>';
      await controller.findByEmail(testEmail);

      expect(adminService.findByEmail).toHaveBeenCalledWith(testEmail);
    });
  });
});
