import {
  IsString,
  IsNotEmpty,
  Valida<PERSON>Nested,
  IsEnum,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for source information in contract call
 */
export class ContractCallSourceDto {
  @ApiProperty({
    description: 'Type of source for the transaction',
    enum: ['Org-Controlled', 'External'],
    example: 'Org-Controlled',
  })
  @IsEnum(['Org-Controlled', 'External'], {
    message: 'Source type must be either Org-Controlled or External',
  })
  source_type: 'Org-Controlled' | 'External';

  @ApiProperty({
    description: 'Wallet ID for the source (required for Org-Controlled)',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    required: false,
  })
  @IsString()
  @IsNotEmpty({ message: 'Wallet ID is required for Org-Controlled source' })
  wallet_id?: string;

  @ApiProperty({
    description: 'Source address (required for External)',
    example: '******************************************',
    pattern: '^0x[a-fA-F0-9]{40}$',
    required: false,
  })
  @IsString()
  @Matches(/^0x[a-fA-F0-9]{40}$/, {
    message: 'Source address must be a valid Ethereum address format',
  })
  address?: string;
}

/**
 * DTO for destination information in contract call
 */
export class ContractCallDestinationDto {
  @ApiProperty({
    description: 'Type of destination for the transaction',
    enum: ['EVM_Contract', 'EVM_Address'],
    example: 'EVM_Contract',
  })
  @IsEnum(['EVM_Contract', 'EVM_Address'], {
    message: 'Destination type must be either EVM_Contract or EVM_Address',
  })
  destination_type: 'EVM_Contract' | 'EVM_Address';

  @ApiProperty({
    description: 'Destination contract or address',
    example: '******************************************',
    pattern: '^0x[a-fA-F0-9]{40}$',
  })
  @IsString()
  @IsNotEmpty({ message: 'Destination address is required' })
  @Matches(/^0x[a-fA-F0-9]{40}$/, {
    message: 'Destination address must be a valid Ethereum address format',
  })
  address: string;

  @ApiProperty({
    description: 'Contract call data (hex encoded)',
    example:
      '0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO',
    pattern: '^0x[a-fA-F0-9]*$',
    required: false,
  })
  @IsString()
  @Matches(/^0x[a-fA-F0-9]*$/, {
    message: 'Call data must be a valid hex string starting with 0x',
  })
  calldata?: string;
}

/**
 * DTO for creating a contract call transaction
 */
export class ContractCallDto {
  @ApiProperty({
    description: 'Unique request ID for the transaction',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
  })
  @IsString()
  @IsNotEmpty({ message: 'Request ID is required' })
  @Matches(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/, {
    message: 'Request ID must be a valid UUID format',
  })
  request_id: string;

  @ApiProperty({
    description: 'Blockchain chain ID',
    example: 'ETH',
    enum: ['ETH', 'BSC', 'MATIC', 'AVAX', 'FTM', 'ARB', 'OP'],
  })
  @IsEnum(['ETH', 'BSC', 'MATIC', 'AVAX', 'FTM', 'ARB', 'OP'], {
    message:
      'Invalid chain ID. Must be one of: ETH, BSC, MATIC, AVAX, FTM, ARB, OP',
  })
  chain_id: string;

  @ApiProperty({
    description: 'Source information for the transaction',
    type: ContractCallSourceDto,
  })
  @ValidateNested()
  @Type(() => ContractCallSourceDto)
  source: ContractCallSourceDto;

  @ApiProperty({
    description: 'Destination information for the transaction',
    type: ContractCallDestinationDto,
  })
  @ValidateNested()
  @Type(() => ContractCallDestinationDto)
  destination: ContractCallDestinationDto;
}

/**
 * DTO for contract call response
 */
export class ContractCallResponseDto {
  @ApiProperty({
    description: 'Request ID that was provided',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  request_id: string;

  @ApiProperty({
    description: 'Transaction ID from Cobo',
    example: 'c986cb3b-1301-412f-9450-13a52c43a95f',
  })
  transaction_id: string;

  @ApiProperty({
    description: 'Transaction status',
    example: 'Submitted',
    enum: [
      'Submitted',
      'PendingScreening',
      'PendingAuthorization',
      'PendingSignature',
      'Broadcasting',
      'Confirming',
      'Completed',
      'Failed',
      'Rejected',
      'Pending',
    ],
  })
  status: string;
}
