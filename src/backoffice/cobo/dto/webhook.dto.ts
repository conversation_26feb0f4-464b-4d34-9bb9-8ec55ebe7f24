import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class TransactionSourceDto {
  @ApiProperty({ description: 'Source type' })
  @IsString()
  source_type: string;

  @ApiProperty({ description: 'Wallet ID' })
  @IsString()
  @IsOptional()
  wallet_id?: string;

  @ApiProperty({ description: 'Address' })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({ description: 'Included UTXOs' })
  @IsOptional()
  included_utxos?: any;

  @ApiProperty({ description: 'Excluded UTXOs' })
  @IsOptional()
  excluded_utxos?: any;

  @ApiProperty({ description: 'Signer key share holder group ID' })
  @IsString()
  @IsOptional()
  signer_key_share_holder_group_id?: string;
}

export class TransactionDestinationDto {
  @ApiProperty({ description: 'Destination type' })
  @IsString()
  destination_type: string;

  @ApiProperty({ description: 'Contract address' })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({ description: 'Calldata' })
  @IsString()
  @IsOptional()
  calldata?: string;

  @ApiProperty({ description: 'Value' })
  @IsString()
  @IsOptional()
  value?: string;

  @ApiProperty({ description: 'Calldata info' })
  @IsOptional()
  calldata_info?: any;
}

export class TransactionFeeDto {
  @ApiProperty({ description: 'Fee type' })
  @IsString()
  fee_type: string;

  @ApiProperty({ description: 'Token ID' })
  @IsString()
  token_id: string;

  @ApiProperty({ description: 'Fee used' })
  @IsString()
  fee_used: string;

  @ApiProperty({ description: 'Estimated fee used' })
  @IsString()
  estimated_fee_used: string;

  @ApiProperty({ description: 'Gas used' })
  @IsString()
  @IsOptional()
  gas_used?: string;

  @ApiProperty({ description: 'Gas price' })
  @IsString()
  @IsOptional()
  gas_price?: string;

  @ApiProperty({ description: 'Gas limit' })
  @IsString()
  @IsOptional()
  gas_limit?: string;
}

export class TransactionTimelineDto {
  @ApiProperty({ description: 'Status' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Finished' })
  @IsOptional()
  finished: boolean;

  @ApiProperty({ description: 'Finished timestamp' })
  @IsNumber()
  @IsOptional()
  finished_timestamp?: number;

  @ApiProperty({ description: 'Sub status' })
  @IsString()
  sub_status: string;
}

export class BlockInfoDto {
  @ApiProperty({ description: 'Block number' })
  @IsNumber()
  block_number: number;

  @ApiProperty({ description: 'Block timestamp' })
  @IsNumber()
  block_timestamp: number;

  @ApiProperty({ description: 'Block hash' })
  @IsString()
  block_hash: string;
}

export class RawTxInfoDto {
  @ApiProperty({ description: 'Used nonce' })
  @IsNumber()
  used_nonce: number;

  @ApiProperty({ description: 'Selected UTXOs' })
  @IsArray()
  selected_utxos: any[];

  @ApiProperty({ description: 'Raw transaction' })
  @IsString()
  raw_tx: string;

  @ApiProperty({ description: 'Unsigned raw transaction' })
  @IsString()
  @IsOptional()
  unsigned_raw_tx?: string;

  @ApiProperty({ description: 'UTXO change' })
  @IsOptional()
  utxo_change?: any;
}

export class TransactionDataDto {
  @ApiProperty({ description: 'Transaction ID' })
  @IsString()
  transaction_id: string;

  @ApiProperty({ description: 'Wallet ID' })
  @IsString()
  wallet_id: string;

  @ApiProperty({ description: 'Transaction type' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Transaction status' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Initiator type' })
  @IsString()
  initiator_type: string;

  @ApiProperty({
    description: 'Source information',
    type: TransactionSourceDto,
  })
  @ValidateNested()
  @Type(() => TransactionSourceDto)
  source: TransactionSourceDto;

  @ApiProperty({
    description: 'Destination information',
    type: TransactionDestinationDto,
  })
  @ValidateNested()
  @Type(() => TransactionDestinationDto)
  destination: TransactionDestinationDto;

  @ApiProperty({ description: 'Created timestamp' })
  @IsNumber()
  created_timestamp: number;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsNumber()
  updated_timestamp: number;

  @ApiProperty({ description: 'Cobo ID' })
  @IsString()
  cobo_id: string;

  @ApiProperty({ description: 'Request ID' })
  @IsString()
  request_id: string;

  @ApiProperty({ description: 'Sub status' })
  @IsString()
  @IsOptional()
  sub_status?: string;

  @ApiProperty({ description: 'Failed reason' })
  @IsString()
  @IsOptional()
  failed_reason?: string;

  @ApiProperty({ description: 'Chain ID' })
  @IsString()
  chain_id: string;

  @ApiProperty({ description: 'Token ID' })
  @IsString()
  token_id: string;

  @ApiProperty({ description: 'Asset ID' })
  @IsOptional()
  asset_id?: string;

  @ApiProperty({ description: 'Result' })
  @IsOptional()
  result?: any;

  @ApiProperty({ description: 'Fee information', type: TransactionFeeDto })
  @ValidateNested()
  @Type(() => TransactionFeeDto)
  fee: TransactionFeeDto;

  @ApiProperty({ description: 'Initiator' })
  @IsString()
  initiator: string;

  @ApiProperty({ description: 'Confirmed number' })
  @IsNumber()
  @IsOptional()
  confirmed_num?: number;

  @ApiProperty({ description: 'Confirming threshold' })
  @IsNumber()
  confirming_threshold: number;

  @ApiProperty({ description: 'Transaction hash' })
  @IsString()
  @IsOptional()
  transaction_hash?: string;

  @ApiProperty({ description: 'Block info', type: BlockInfoDto })
  @ValidateNested()
  @Type(() => BlockInfoDto)
  @IsOptional()
  block_info?: BlockInfoDto;

  @ApiProperty({ description: 'Raw transaction info', type: RawTxInfoDto })
  @ValidateNested()
  @Type(() => RawTxInfoDto)
  @IsOptional()
  raw_tx_info?: RawTxInfoDto;

  @ApiProperty({ description: 'Replacement' })
  @IsOptional()
  replacement?: any;

  @ApiProperty({ description: 'Fueling info' })
  @IsOptional()
  fueling_info?: any;

  @ApiProperty({ description: 'Category' })
  @IsArray()
  category: string[];

  @ApiProperty({ description: 'Cobo category' })
  @IsArray()
  cobo_category: string[];

  @ApiProperty({ description: 'Description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Is loop' })
  @IsOptional()
  is_loop: boolean;

  @ApiProperty({ description: 'Extra' })
  @IsOptional()
  extra?: any;

  @ApiProperty({ description: 'Timeline', type: [TransactionTimelineDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TransactionTimelineDto)
  timeline: TransactionTimelineDto[];

  @ApiProperty({ description: 'Data type' })
  @IsString()
  data_type: string;
}

export class WebhookEventDto {
  @ApiProperty({ description: 'Event ID' })
  @IsString()
  event_id: string;

  @ApiProperty({ description: 'Event URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: 'Timestamp' })
  @IsNumber()
  created_timestamp: number;

  @ApiProperty({ description: 'Event type' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Transaction data', type: TransactionDataDto })
  @ValidateNested()
  @Type(() => TransactionDataDto)
  data: TransactionDataDto;
}

// Updated callback DTO to match actual Cobo callback structure
export class WebhookCallbackDto {
  @ApiProperty({ description: 'Transaction ID' })
  @IsString()
  transaction_id: string;

  @ApiProperty({ description: 'Wallet ID' })
  @IsString()
  wallet_id: string;

  @ApiProperty({ description: 'Transaction type' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Transaction status' })
  @IsString()
  status: string;

  @ApiProperty({ description: 'Initiator type' })
  @IsString()
  initiator_type: string;

  @ApiProperty({
    description: 'Source information',
    type: TransactionSourceDto,
  })
  @ValidateNested()
  @Type(() => TransactionSourceDto)
  source: TransactionSourceDto;

  @ApiProperty({
    description: 'Destination information',
    type: TransactionDestinationDto,
  })
  @ValidateNested()
  @Type(() => TransactionDestinationDto)
  destination: TransactionDestinationDto;

  @ApiProperty({ description: 'Created timestamp' })
  @IsNumber()
  created_timestamp: number;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsNumber()
  updated_timestamp: number;

  @ApiProperty({ description: 'Cobo ID' })
  @IsString()
  cobo_id: string;

  @ApiProperty({ description: 'Request ID' })
  @IsString()
  request_id: string;

  @ApiProperty({ description: 'Sub status' })
  @IsString()
  @IsOptional()
  sub_status?: string;

  @ApiProperty({ description: 'Failed reason' })
  @IsString()
  @IsOptional()
  failed_reason?: string;

  @ApiProperty({ description: 'Chain ID' })
  @IsString()
  chain_id: string;

  @ApiProperty({ description: 'Token ID' })
  @IsString()
  token_id: string;

  @ApiProperty({ description: 'Asset ID' })
  @IsOptional()
  asset_id?: string;

  @ApiProperty({ description: 'Result' })
  @IsOptional()
  result?: any;

  @ApiProperty({ description: 'Fee information', type: TransactionFeeDto })
  @ValidateNested()
  @Type(() => TransactionFeeDto)
  fee: TransactionFeeDto;

  @ApiProperty({ description: 'Initiator' })
  @IsString()
  initiator: string;

  @ApiProperty({ description: 'Confirmed number' })
  @IsNumber()
  @IsOptional()
  confirmed_num?: number;

  @ApiProperty({ description: 'Confirming threshold' })
  @IsNumber()
  confirming_threshold: number;

  @ApiProperty({ description: 'Transaction hash' })
  @IsString()
  @IsOptional()
  transaction_hash?: string;

  @ApiProperty({ description: 'Block info', type: BlockInfoDto })
  @ValidateNested()
  @Type(() => BlockInfoDto)
  @IsOptional()
  block_info?: BlockInfoDto;

  @ApiProperty({ description: 'Raw transaction info', type: RawTxInfoDto })
  @ValidateNested()
  @Type(() => RawTxInfoDto)
  @IsOptional()
  raw_tx_info?: RawTxInfoDto;

  @ApiProperty({ description: 'Replacement' })
  @IsOptional()
  replacement?: any;

  @ApiProperty({ description: 'Fueling info' })
  @IsOptional()
  fueling_info?: any;

  @ApiProperty({ description: 'Category' })
  @IsArray()
  category: string[];

  @ApiProperty({ description: 'Cobo category' })
  @IsArray()
  cobo_category: string[];

  @ApiProperty({ description: 'Description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Is loop' })
  @IsOptional()
  is_loop: boolean;

  @ApiProperty({ description: 'Extra' })
  @IsOptional()
  extra?: any;
}

export class WebhookResponseDto {
  @ApiProperty({ description: 'Response status', example: 'ok' })
  @IsString()
  status: 'ok' | 'deny';
}
