import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as CoboWaas2 from '@cobo/cobo-waas2';
import {
  ContractCallDto,
  ContractCallResponseDto,
} from './dto/contract-call.dto';

@Injectable()
export class CoboService {
  private readonly logger = new Logger(CoboService.name);
  private readonly apiClient: any;
  private readonly walletsApi: any;
  private readonly transactionsApi: any;

  constructor(private configService: ConfigService) {
    // Initialize the API client
    this.apiClient = CoboWaas2.ApiClient.instance;

    // Set the environment (development or production)
    const environment = this.configService.get<string>('COBO_ENV', 'DEV');
    this.apiClient.setEnv(
      environment === 'PROD' ? CoboWaas2.Env.PROD : CoboWaas2.Env.DEV,
    );

    // Configure API Secret
    const apiSecret = this.configService.get<string>('COBO_API_SECRET');
    if (!apiSecret) {
      this.logger.warn('COBO_API_SECRET not found in environment variables');
    } else {
      this.apiClient.setPrivateKey(apiSecret);
    }

    // Create API instances
    this.walletsApi = new CoboWaas2.WalletsApi();
    this.transactionsApi = new CoboWaas2.TransactionsApi();

    this.logger.log('Cobo service initialized');
  }

  /**
   * List supported chains from Cobo WaaS
   * @param options Optional parameters for filtering chains
   */
  async listSupportedChains(options?: {
    wallet_type?: string;
    wallet_subtype?: string;
    chain_ids?: string;
    limit?: number;
    before?: string;
    after?: string;
  }) {
    try {
      this.logger.log('Fetching supported chains from Cobo WaaS');

      const opts = {
        wallet_type: options?.wallet_type || 'Custodial',
        wallet_subtype: options?.wallet_subtype || 'Asset',
        chain_ids: options?.chain_ids || 'BTC,ETH',
        limit: options?.limit || 10,
        ...(options?.before && { before: options.before }),
        ...(options?.after && { after: options.after }),
      };

      const data = await this.walletsApi.listSupportedChains(opts);

      this.logger.log(
        `Successfully fetched ${data.data?.length || 0} supported chains`,
      );
      return data;
    } catch (error) {
      this.logger.error('Error fetching supported chains:', error);
      throw new Error(`Failed to fetch supported chains: ${error.message}`);
    }
  }

  /**
   * Create a new wallet using Cobo WaaS
   * @param walletParams Parameters for wallet creation
   */
  async createWallet(walletParams: {
    name: string;
    wallet_type?: string;
    wallet_subtype?: string;
  }) {
    try {
      this.logger.log(`Creating wallet: ${walletParams.name}`);

      const opts = {
        CreateWalletParams: CoboWaas2.CreateWalletParams.constructFromObject({
          name: walletParams.name,
          wallet_type: walletParams.wallet_type || 'Custodial',
          wallet_subtype: walletParams.wallet_subtype || 'Asset',
        }),
      };

      const data = await this.walletsApi.createWallet(opts);

      this.logger.log(`Successfully created wallet: ${walletParams.name}`);
      return data;
    } catch (error) {
      this.logger.error('Error creating wallet:', error);
      throw new Error(`Failed to create wallet: ${error.message}`);
    }
  }

  /**
   * Create a contract call transaction using Cobo WaaS
   * @param contractCallParams Parameters for contract call transaction
   */
  async createContractCallTransaction(
    contractCallParams: ContractCallDto,
  ): Promise<ContractCallResponseDto> {
    try {
      this.logger.log(
        `Creating contract call transaction: ${contractCallParams.request_id}`,
      );

      const opts = {
        ContractCallParams: CoboWaas2.ContractCallParams.constructFromObject({
          request_id: contractCallParams.request_id,
          chain_id: contractCallParams.chain_id,
          source: {
            source_type: contractCallParams.source.source_type,
            ...(contractCallParams.source.wallet_id && {
              wallet_id: contractCallParams.source.wallet_id,
            }),
            ...(contractCallParams.source.address && {
              address: contractCallParams.source.address,
            }),
          },
          destination: {
            destination_type: contractCallParams.destination.destination_type,
            address: contractCallParams.destination.address,
            ...(contractCallParams.destination.calldata && {
              calldata: contractCallParams.destination.calldata,
            }),
          },
        }),
      };

      const data =
        await this.transactionsApi.createContractCallTransaction(opts);

      this.logger.log(
        `Successfully created contract call transaction: ${contractCallParams.request_id}`,
      );
      return data;
    } catch (error) {
      this.logger.error(
        'Error creating contract call transaction:',
        error.body.error_message,
      );
      throw new Error(
        `Failed to create contract call transaction: ${error.body.error_message}`,
      );
    }
  }
}
