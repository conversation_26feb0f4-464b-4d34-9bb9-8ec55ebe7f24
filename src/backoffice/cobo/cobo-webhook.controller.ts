import {
  Controller,
  Post,
  Headers,
  HttpC<PERSON>,
  HttpStatus,
  Logger,
  UnauthorizedException,
  Req,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiHeader,
  ApiBody,
} from '@nestjs/swagger';
import { Request } from 'express';
import { CoboWebhookService } from './cobo-webhook.service';
import { WebhookCallbackDto, WebhookEventDto } from './dto/webhook.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

@ApiTags('Cobo Webhooks')
@Controller('cobo/webhook')
export class CoboWebhookController {
  private readonly logger = new Logger(CoboWebhookController.name);

  constructor(private readonly webhookService: CoboWebhookService) {}

  @Post('event')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle Cobo webhook events',
    description: 'Endpoint for receiving webhook events from Cobo WaaS',
  })
  @ApiHeader({
    name: 'BIZ_TIMESTAMP',
    description: 'Request timestamp',
    required: true,
  })
  @ApiHeader({
    name: 'BIZ_RESP_SIGNATURE',
    description: 'Request signature for verification',
    required: true,
  })
  @ApiBody({
    description: 'Webhook event data',
    type: WebhookEventDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Event processed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'success' },
        message: { type: 'string', example: 'Event processed' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid signature',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid payload',
  })
  async handleWebhookEvent(
    @Req() req: Request,
    @Headers('BIZ_TIMESTAMP') timestamp: string,
    @Headers('BIZ_RESP_SIGNATURE') signature: string,
  ): Promise<{ status: string; message: string }> {
    try {
      // Get raw body for signature verification
      const rawBody = req.body?.toString('utf8');

      if (!rawBody) {
        this.logger.error('No raw body received');
        throw new UnauthorizedException('No body received');
      }

      // Parse the JSON body manually
      const parsedData = JSON.parse(rawBody);

      // Transform and validate the data
      const eventData = plainToClass(WebhookEventDto, parsedData);
      const validationErrors = await validate(eventData);

      if (validationErrors.length > 0) {
        this.logger.error('Validation errors:', validationErrors);
        throw new BadRequestException('Invalid webhook payload');
      }

      this.logger.log(`Received webhook event: ${eventData.event_id}`);

      // Verify signature
      if (!this.webhookService.verifySignature(rawBody, timestamp, signature)) {
        this.logger.error('Invalid webhook signature');
        throw new UnauthorizedException('Invalid signature');
      }

      // Process the event asynchronously
      // Note: According to Cobo docs, we should respond quickly and process asynchronously
      setImmediate(async () => {
        try {
          await this.webhookService.processWebhookEvent(eventData);
        } catch (error) {
          this.logger.error('Error processing webhook event:', error);
        }
      });

      this.logger.log(
        `Webhook event ${eventData.event_id} processed successfully`,
      );
      return {
        status: 'success',
        message: 'Event processed',
      };
    } catch (error) {
      this.logger.error('Error handling webhook event:', error);
      throw error;
    }
  }

  @Post('callback')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle Cobo callback messages',
    description:
      'Endpoint for receiving callback messages from Cobo WaaS for transaction approval',
  })
  @ApiHeader({
    name: 'BIZ_TIMESTAMP',
    description: 'Request timestamp',
    required: true,
  })
  @ApiHeader({
    name: 'BIZ_RESP_SIGNATURE',
    description: 'Request signature for verification',
    required: true,
  })
  @ApiBody({
    description: 'Callback message data',
    type: WebhookCallbackDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Callback processed successfully',
    schema: {
      type: 'string',
      example: 'ok',
      description:
        'Response should be "ok" for approval or "deny" for rejection',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid signature',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid payload',
  })
  async handleCallback(
    @Req() req: Request,
    @Headers('BIZ_TIMESTAMP') timestamp: string,
    @Headers('BIZ_RESP_SIGNATURE') signature: string,
  ): Promise<string> {
    try {
      // Get raw body for signature verification
      const rawBody = req.body?.toString('utf8');

      if (!rawBody) {
        this.logger.error('No raw body received');
        throw new UnauthorizedException('No body received');
      }

      // Parse the JSON body manually
      const parsedData = JSON.parse(rawBody);

      // Transform and validate the data
      const callbackData = plainToClass(WebhookCallbackDto, parsedData);
      const validationErrors = await validate(callbackData);

      if (validationErrors.length > 0) {
        this.logger.error('Validation errors:', validationErrors);
        throw new BadRequestException('Invalid callback payload');
      }

      this.logger.log(
        `Received callback for transaction: ${callbackData.transaction_id}`,
      );

      // Verify signature
      if (!this.webhookService.verifySignature(rawBody, timestamp, signature)) {
        this.logger.error('Invalid callback signature');
        throw new UnauthorizedException('Invalid signature');
      }

      // Process the callback and get response
      const response = await this.webhookService.processCallback(callbackData);

      this.logger.log(
        `Callback response for transaction ${callbackData.transaction_id}: ${response}`,
      );
      return response;
    } catch (error) {
      this.logger.error('Error handling callback:', error);
      // Return 'deny' on error for security
      return 'deny';
    }
  }
}
