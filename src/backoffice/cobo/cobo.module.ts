import { Modu<PERSON> } from '@nestjs/common';
import { CoboController } from './cobo.controller';
import { CoboService } from './cobo.service';
import { CoboWebhookController } from './cobo-webhook.controller';
import { CoboWebhookService } from './cobo-webhook.service';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CoboController, CoboWebhookController],
  providers: [CoboService, CoboWebhookService],
  exports: [CoboService, CoboWebhookService],
})
export class CoboModule {}
