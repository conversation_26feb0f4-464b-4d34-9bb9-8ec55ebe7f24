import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  HttpException,
  HttpStatus,
  ValidationPipe,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { CoboService } from './cobo.service';
import {
  ContractCallDto,
  ContractCallResponseDto,
} from './dto/contract-call.dto';

@ApiTags('Cobo Integration')
@Controller('cobo')
export class CoboController {
  private readonly logger = new Logger(CoboController.name);

  constructor(private readonly coboService: CoboService) {}

  @Get('chains')
  @ApiOperation({ summary: 'List supported chains from Cobo WaaS' })
  @ApiQuery({
    name: 'wallet_type',
    required: false,
    description: 'Wallet type (default: Custodial)',
  })
  @ApiQuery({
    name: 'wallet_subtype',
    required: false,
    description: 'Wallet subtype (default: Asset)',
  })
  @ApiQuery({
    name: 'chain_ids',
    required: false,
    description: 'Comma-separated chain IDs (default: BTC,ETH)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of results to return (default: 10)',
  })
  @ApiQuery({
    name: 'before',
    required: false,
    description: 'Pagination cursor (before)',
  })
  @ApiQuery({
    name: 'after',
    required: false,
    description: 'Pagination cursor (after)',
  })
  @ApiResponse({
    status: 200,
    description: 'List of supported chains',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              chain_id: { type: 'string' },
              symbol: { type: 'string' },
              icon_url: { type: 'string' },
              explorer_tx_url: { type: 'string' },
              explorer_address_url: { type: 'string' },
              require_memo: { type: 'boolean' },
              confirming_threshold: { type: 'number' },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            before: { type: 'string' },
            after: { type: 'string' },
            total_count: { type: 'number' },
          },
        },
      },
    },
  })
  async listSupportedChains(
    @Query('wallet_type') wallet_type?: string,
    @Query('wallet_subtype') wallet_subtype?: string,
    @Query('chain_ids') chain_ids?: string,
    @Query('limit') limit?: number,
    @Query('before') before?: string,
    @Query('after') after?: string,
  ) {
    try {
      this.logger.log('Fetching supported chains from Cobo WaaS');

      const options = {
        ...(wallet_type && { wallet_type }),
        ...(wallet_subtype && { wallet_subtype }),
        ...(chain_ids && { chain_ids }),
        ...(limit && { limit: Number(limit) }),
        ...(before && { before }),
        ...(after && { after }),
      };

      const result = await this.coboService.listSupportedChains(options);
      this.logger.log(
        `Successfully fetched ${result.data?.length || 0} supported chains`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to fetch supported chains: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to fetch supported chains: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('wallet')
  @ApiOperation({ summary: 'Create a new wallet using Cobo WaaS' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: 'Wallet name' },
        wallet_type: {
          type: 'string',
          description: 'Wallet type (default: Custodial)',
        },
        wallet_subtype: {
          type: 'string',
          description: 'Wallet subtype (default: Asset)',
        },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Wallet created successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            wallet_id: { type: 'string' },
            name: { type: 'string' },
            wallet_type: { type: 'string' },
            wallet_subtype: { type: 'string' },
            created_at: { type: 'string' },
          },
        },
      },
    },
  })
  async createWallet(
    @Body(ValidationPipe)
    walletParams: {
      name: string;
      wallet_type?: string;
      wallet_subtype?: string;
    },
  ) {
    try {
      this.logger.log(`Creating wallet: ${walletParams.name}`);

      if (!walletParams.name) {
        throw new HttpException(
          'Wallet name is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.coboService.createWallet(walletParams);
      this.logger.log(`Successfully created wallet: ${walletParams.name}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create wallet: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to create wallet: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('contract-call')
  @ApiOperation({
    summary: 'Create a contract call transaction using Cobo WaaS',
  })
  @ApiBody({ type: ContractCallDto })
  @ApiResponse({
    status: 201,
    description: 'Contract call transaction created successfully',
    type: ContractCallResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
    schema: {
      example: {
        statusCode: 400,
        message: ['Request ID must be a valid UUID format'],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    schema: {
      example: {
        statusCode: 500,
        message: 'Failed to create contract call transaction: API error',
        error: 'Internal Server Error',
      },
    },
  })
  async createContractCallTransaction(
    @Body(ValidationPipe) contractCallDto: ContractCallDto,
  ): Promise<ContractCallResponseDto> {
    try {
      this.logger.log(
        `Creating contract call transaction: ${contractCallDto.request_id}`,
      );

      const result =
        await this.coboService.createContractCallTransaction(contractCallDto);
      this.logger.log(
        `Successfully created contract call transaction: ${contractCallDto.request_id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to create contract call transaction: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to create contract call transaction: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
