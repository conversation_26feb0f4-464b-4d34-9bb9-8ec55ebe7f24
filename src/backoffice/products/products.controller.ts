import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { ProductsService } from '../../products/products.service';
import { BaseBackofficeResponseDto } from '../common/dto/base-backoffice-response.dto';
import { PaginationDto } from '../common/dto/pagination.dto';
import { ProductResponseDto, ProductSummaryDto } from '../../products/dto';
import { BaseBackofficeController } from '../common/base-backoffice.controller';

// TODO TEMPORARY END POINT
@ApiTags('Backoffice - Products (Temporary)')
@Controller('backoffice/products')
export class BackofficeProductsController extends BaseBackofficeController {
  constructor(private readonly productsService: ProductsService) {
    super();
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get product by ID (detailed view)',
    description: 'Get product by ID with detailed information.',
  })
  @ApiResponse({
    status: 200,
    description: 'Product retrieved successfully',
    type: BaseBackofficeResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/ProductResponseDto' },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findOne(
    @Param('id') id: string,
  ): Promise<BaseBackofficeResponseDto<ProductResponseDto>> {
    return await this.productsService.findOne(id);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all products (summary view)',
    description:
      'Get all products with summary information and pagination support.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Products retrieved successfully',
    type: BaseBackofficeResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/ProductSummaryDto' },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No products found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findAll(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    @Query() _pagination: PaginationDto,
  ): Promise<BaseBackofficeResponseDto<ProductSummaryDto[]>> {
    return await this.productsService.findAll();
  }
}
