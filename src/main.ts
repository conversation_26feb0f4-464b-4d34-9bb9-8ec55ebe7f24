import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from './common/pipes/validation.pipe';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import * as express from 'express';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    // Log environment information for debugging
    logger.log('🔧 Starting application bootstrap...');
    logger.log(`🔧 Node.js version: ${process.version}`);
    logger.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`🔧 Port: ${process.env.PORT || 3000}`);
    logger.log(`🔧 Database URL configured: ${!!process.env.DATABASE_URL}`);
    logger.log(`🔧 JWT Secret configured: ${!!process.env.JWT_SECRET}`);

    // Validate required environment variables
    if (!process.env.DATABASE_URL) {
      logger.error('❌ DATABASE_URL environment variable is required');
      process.exit(1);
    }

    if (!process.env.JWT_SECRET) {
      logger.error('❌ JWT_SECRET environment variable is required');
      process.exit(1);
    }

    const app = await NestFactory.create(AppModule, {
      logger:
        process.env.NODE_ENV === 'production'
          ? ['error', 'warn', 'log']
          : ['error', 'warn', 'log', 'debug', 'verbose'],
      abortOnError: false,
    });

    app.use('/api/v1/cobo/webhook', express.raw({ type: 'application/json' }));

    // Enable graceful shutdown hooks
    app.enableShutdownHooks();

    app.useGlobalPipes(new ValidationPipe());
    app.useGlobalFilters(new HttpExceptionFilter());

    app.setGlobalPrefix('api/v1');

    // CORS configuration
    app.enableCors({
      origin:
        process.env.NODE_ENV === 'production'
          ? [
              'https://your-frontend-domain.com',
              'https://orbitum-app.fly.dev',
              'https://orbitum-backoffice.netlify.app',
            ]
          : true,
      credentials: true,
    });

    // Swagger configuration
    const config = new DocumentBuilder()
      .setTitle('Orbitum API')
      .setDescription('User management and KYC/KYB API')
      .setVersion('1.0')
      .addTag('Users', 'User management endpoints')
      .addTag('Authentication', 'Authentication endpoints')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addServer(
        process.env.NODE_ENV === 'production'
          ? 'https://orbitum-app.fly.dev'
          : 'http://localhost:3000',
        process.env.NODE_ENV === 'production'
          ? 'Production server'
          : 'Development server',
      )
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
        filter: true,
        showRequestDuration: true,
      },
      customSiteTitle: 'Orbitum API Documentation',
    });

    // Simple root endpoint
    app.getHttpAdapter().get('/', (req, res) => {
      res.status(200).json({
        message: 'Orbitum API is running',
        timestamp: new Date().toISOString(),
        port: process.env.PORT || 3000,
      });
    });

    // Health check endpoint
    app.getHttpAdapter().get('/api/v1/health', async (req, res) => {
      try {
        const healthData = {
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
          memory: {
            used:
              Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
            total:
              Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB',
          },
          port: process.env.PORT || 3000,
        };

        res.status(200).json(healthData);
      } catch (error) {
        logger.error('Health check error:', error);
        res.status(500).json({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error.message,
        });
      }
    });

    // Ready endpoint for startup probe
    app.getHttpAdapter().get('/api/v1/ready', (req, res) => {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });

    // API Info endpoint
    app.getHttpAdapter().get('/api/v1/info', (req, res) => {
      res.json({
        name: 'Orbitum API',
        version: '1.0.0',
        description: 'User management and KYC/KYB API',
        environment: process.env.NODE_ENV || 'development',
      });
    });

    const port = process.env.PORT || 3000;

    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 Application is running on port ${port}`);
    logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`📚 Swagger docs: http://localhost:${port}/api/docs`);
    logger.log(`❤️ Health check: http://localhost:${port}/api/v1/health`);

    // Setup graceful shutdown
    setupGracefulShutdown(app, logger);
  } catch (error) {
    logger.error('❌ Failed to start application:', error);
    process.exit(1);
  }
}

function setupGracefulShutdown(app: any, logger: Logger) {
  const gracefulShutdown = async (signal: string) => {
    logger.log(`🛑 ${signal} received, shutting down gracefully...`);
    try {
      await app.close();
      logger.log('✅ Application closed successfully');
      process.exit(0);
    } catch (error) {
      logger.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  process.on('uncaughtException', (error) => {
    logger.error('❌ Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
  });
}

bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('❌ Failed to start application:', error);
  process.exit(1);
});
