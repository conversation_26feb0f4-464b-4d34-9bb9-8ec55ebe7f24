import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as process from 'process';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface RestartOptions {
  reason: string;
  delay?: number;
  killExistingProcess?: boolean;
  maxRestarts?: number;
}

export interface ProcessInfo {
  pid: number;
  port: number;
  command: string;
}

@Injectable()
export class AutoRestartService {
  private readonly logger = new Logger(AutoRestartService.name);
  private restartCount = 0;
  private readonly maxRestarts: number;
  private readonly restartWindow = 300000; // 5 minutes
  private lastRestartTime = 0;

  constructor(private readonly configService: ConfigService) {
    this.maxRestarts = this.configService.get<number>('MAX_RESTARTS', 5);
  }

  /**
   * Restart the application with proper cleanup
   */
  async restartApplication(options: RestartOptions): Promise<void> {
    try {
      this.logger.warn(`Application restart requested: ${options.reason}`);

      // Check restart limits
      if (!this.canRestart()) {
        this.logger.error('Maximum restart limit reached, preventing restart');
        return;
      }

      // Kill existing processes on the port if requested
      if (options.killExistingProcess) {
        await this.killProcessesOnPort();
      }

      // Delay before restart if specified
      if (options.delay && options.delay > 0) {
        this.logger.log(`Waiting ${options.delay}ms before restart...`);
        await this.sleep(options.delay);
      }

      // Increment restart counter
      this.incrementRestartCount();

      // Log restart information
      this.logRestartInfo(options);

      // Perform graceful shutdown and restart
      await this.performRestart();
    } catch (error) {
      this.logger.error('Failed to restart application:', error);
      // If restart fails, try emergency shutdown
      this.emergencyShutdown();
    }
  }

  /**
   * Kill processes running on the application port
   */
  async killProcessesOnPort(): Promise<void> {
    const port = this.configService.get<number>('PORT', 3001);

    try {
      this.logger.log(`Killing processes on port ${port}...`);

      // Find processes using the port
      const processes = await this.findProcessesOnPort(port);

      if (processes.length === 0) {
        this.logger.log(`No processes found on port ${port}`);
        return;
      }

      // Kill each process
      for (const processInfo of processes) {
        await this.killProcess(processInfo);
      }

      // Wait a moment for processes to terminate
      await this.sleep(1000);

      // Verify processes are killed
      const remainingProcesses = await this.findProcessesOnPort(port);
      if (remainingProcesses.length > 0) {
        this.logger.warn(
          `${remainingProcesses.length} processes still running on port ${port}`,
        );
        // Force kill remaining processes
        for (const processInfo of remainingProcesses) {
          await this.forceKillProcess(processInfo);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to kill processes on port ${port}:`, error);
    }
  }

  /**
   * Find processes running on a specific port
   */
  private async findProcessesOnPort(port: number): Promise<ProcessInfo[]> {
    try {
      const command =
        process.platform === 'win32'
          ? `netstat -ano | findstr :${port}`
          : `lsof -ti:${port}`;

      const { stdout } = await execAsync(command);

      if (!stdout.trim()) {
        return [];
      }

      if (process.platform === 'win32') {
        return this.parseWindowsNetstat(stdout, port);
      } else {
        return this.parseUnixLsof(stdout, port);
      }
    } catch (error) {
      // Command might fail if no processes found, which is normal
      this.logger.debug(`No processes found on port ${port}: ${error.message}`);
      return [];
    }
  }

  /**
   * Parse Windows netstat output
   */
  private parseWindowsNetstat(output: string, port: number): ProcessInfo[] {
    const processes: ProcessInfo[] = [];
    const lines = output.split('\n');

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 5) {
        const pid = parseInt(parts[4]);
        if (!isNaN(pid)) {
          processes.push({
            pid,
            port,
            command: 'unknown',
          });
        }
      }
    }

    return processes;
  }

  /**
   * Parse Unix lsof output
   */
  private parseUnixLsof(output: string, port: number): ProcessInfo[] {
    const processes: ProcessInfo[] = [];
    const pids = output.trim().split('\n');

    for (const pidStr of pids) {
      const pid = parseInt(pidStr.trim());
      if (!isNaN(pid)) {
        processes.push({
          pid,
          port,
          command: 'unknown',
        });
      }
    }

    return processes;
  }

  /**
   * Kill a process gracefully
   */
  private async killProcess(processInfo: ProcessInfo): Promise<void> {
    try {
      this.logger.log(
        `Killing process ${processInfo.pid} on port ${processInfo.port}`,
      );

      if (process.platform === 'win32') {
        await execAsync(`taskkill /PID ${processInfo.pid} /F`);
      } else {
        process.kill(processInfo.pid, 'SIGTERM');
      }
    } catch (error) {
      this.logger.warn(
        `Failed to kill process ${processInfo.pid}:`,
        error.message,
      );
    }
  }

  /**
   * Force kill a process
   */
  private async forceKillProcess(processInfo: ProcessInfo): Promise<void> {
    try {
      this.logger.log(`Force killing process ${processInfo.pid}`);

      if (process.platform === 'win32') {
        await execAsync(`taskkill /PID ${processInfo.pid} /F`);
      } else {
        process.kill(processInfo.pid, 'SIGKILL');
      }
    } catch (error) {
      this.logger.error(
        `Failed to force kill process ${processInfo.pid}:`,
        error.message,
      );
    }
  }

  /**
   * Check if restart is allowed based on limits
   */
  private canRestart(): boolean {
    const now = Date.now();

    // Reset counter if restart window has passed
    if (now - this.lastRestartTime > this.restartWindow) {
      this.restartCount = 0;
    }

    return this.restartCount < this.maxRestarts;
  }

  /**
   * Increment restart counter
   */
  private incrementRestartCount(): void {
    this.restartCount++;
    this.lastRestartTime = Date.now();
  }

  /**
   * Log restart information
   */
  private logRestartInfo(options: RestartOptions): void {
    this.logger.warn('Application Restart Information:', {
      reason: options.reason,
      restartCount: this.restartCount,
      maxRestarts: this.maxRestarts,
      timestamp: new Date().toISOString(),
      processId: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
    });
  }

  /**
   * Perform the actual restart
   */
  private async performRestart(): Promise<void> {
    this.logger.log('Performing application restart...');

    // Emit restart event for cleanup
    process.emit('SIGTERM');

    // Give time for graceful shutdown
    setTimeout(() => {
      this.logger.log('Graceful restart - exiting with code 0');
      process.exit(0);
    }, 2000);

    // Force exit after timeout if graceful shutdown fails
    setTimeout(() => {
      this.logger.error('Forced exit due to restart timeout');
      process.exit(1);
    }, 10000);
  }

  /**
   * Emergency shutdown when restart fails
   */
  private emergencyShutdown(): void {
    this.logger.error('Performing emergency shutdown...');

    setTimeout(() => {
      process.exit(1);
    }, 1000);
  }

  /**
   * Sleep utility function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get restart statistics
   */
  getRestartStats(): {
    restartCount: number;
    maxRestarts: number;
    lastRestartTime: number;
    canRestart: boolean;
  } {
    return {
      restartCount: this.restartCount,
      maxRestarts: this.maxRestarts,
      lastRestartTime: this.lastRestartTime,
      canRestart: this.canRestart(),
    };
  }
}
