import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    // Enhanced logging for debugging
    console.error('HTTP Exception caught:', {
      status,
      message: exception.message,
      response: exception.getResponse(),
      path: request.url,
      method: request.method,
      body: request.body,
      headers: request.headers,
      stack: exception.stack,
    });

    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: exception.message || 'Internal server error',
      details: exception.getResponse(), // Add detailed error information
    };

    response.status(status).json(errorResponse);
  }
}
