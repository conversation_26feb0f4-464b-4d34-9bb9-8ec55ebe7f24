/*
  Warnings:

  - You are about to drop the `Redeem` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `RedeemSubscribes` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."RedeemSubscribes" DROP CONSTRAINT "RedeemSubscribes_redeem_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."RedeemSubscribes" DROP CONSTRAINT "RedeemSubscribes_subscribe_id_fkey";

-- DropTable
DROP TABLE "public"."Redeem";

-- DropTable
DROP TABLE "public"."RedeemSubscribes";

-- CreateTable
CREATE TABLE "public"."redeem" (
    "id" UUID NOT NULL,
    "request_number" VARCHAR(66),
    "date_cutoff" TIMESTAMPTZ(6) NOT NULL,
    "payment_status" TEXT NOT NULL,
    "checkout_status" TEXT NOT NULL,
    "redeem_status" TEXT NOT NULL,
    "status_buy" "public"."StatusBuy" NOT NULL,
    "total_amount" DECIMAL(18,8) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "redeem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."redeem_subscribes" (
    "id" UUID NOT NULL,
    "subscribe_id" UUID NOT NULL,
    "redeem_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "redeem_subscribes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "redeem_request_number_key" ON "public"."redeem"("request_number");

-- CreateIndex
CREATE INDEX "redeem_subscribes_redeem_id_subscribe_id_idx" ON "public"."redeem_subscribes"("redeem_id", "subscribe_id");

-- AddForeignKey
ALTER TABLE "public"."redeem_subscribes" ADD CONSTRAINT "redeem_subscribes_redeem_id_fkey" FOREIGN KEY ("redeem_id") REFERENCES "public"."redeem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."redeem_subscribes" ADD CONSTRAINT "redeem_subscribes_subscribe_id_fkey" FOREIGN KEY ("subscribe_id") REFERENCES "public"."subscribe"("id") ON DELETE CASCADE ON UPDATE CASCADE;
