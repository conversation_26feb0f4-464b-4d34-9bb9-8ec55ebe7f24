/*
  Warnings:

  - A unique constraint covering the columns `[request_number]` on the table `subscribe` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateTable
CREATE TABLE "public"."Redeem" (
    "id" UUID NOT NULL,
    "request_number" VARCHAR(66),
    "date_cutoff" TIMESTAMPTZ(6) NOT NULL,
    "payment_status" TEXT NOT NULL,
    "checkout_status" TEXT NOT NULL,
    "redeem_status" TEXT NOT NULL,
    "status_buy" "public"."StatusBuy" NOT NULL,
    "total_amount" DECIMAL(18,8) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Redeem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."RedeemSubscribes" (
    "id" UUID NOT NULL,
    "subscribe_id" UUID NOT NULL,
    "redeem_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RedeemSubscribes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Redeem_request_number_key" ON "public"."Redeem"("request_number");

-- CreateIndex
CREATE INDEX "RedeemSubscribes_redeem_id_subscribe_id_idx" ON "public"."RedeemSubscribes"("redeem_id", "subscribe_id");

-- CreateIndex
CREATE UNIQUE INDEX "subscribe_request_number_key" ON "public"."subscribe"("request_number");

-- AddForeignKey
ALTER TABLE "public"."RedeemSubscribes" ADD CONSTRAINT "RedeemSubscribes_redeem_id_fkey" FOREIGN KEY ("redeem_id") REFERENCES "public"."Redeem"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."RedeemSubscribes" ADD CONSTRAINT "RedeemSubscribes_subscribe_id_fkey" FOREIGN KEY ("subscribe_id") REFERENCES "public"."subscribe"("id") ON DELETE CASCADE ON UPDATE CASCADE;
