#!/usr/bin/env node

/**
 * OAuth Dual Response System Test Script
 * 
 * This script demonstrates how to test both API and HTML responses
 * from the OAuth callback endpoint.
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.BACKEND_URL || 'http://localhost:3001';
const API_BASE = `${BASE_URL}/api/v1/auth`;

/**
 * Test API Response (JSON)
 */
async function testApiResponse() {
  console.log('\n🔍 Testing API Response (JSON)...');
  
  const options = {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-OAuth-API-Request': 'true',
      'X-Requested-With': 'XMLHttpRequest',
      'User-Agent': 'OAuth-Test-Client/1.0'
    }
  };

  try {
    // Note: This would normally be called after OAuth flow
    // For testing, we're just showing the expected headers
    console.log('📤 Request Headers for API Response:');
    console.log(JSON.stringify(options.headers, null, 2));
    
    console.log('\n✅ Expected API Response Structure:');
    console.log(`{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accessTokenExpiresIn": 900,
  "refreshTokenExpiresIn": 2592000,
  "tokenType": "Bearer",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  },
  "isNewUser": false,
  "provider": "google",
  "message": "OAuth authentication successful"
}`);

  } catch (error) {
    console.error('❌ API Response Test Error:', error.message);
  }
}

/**
 * Test HTML Response
 */
async function testHtmlResponse() {
  console.log('\n🔍 Testing HTML Response...');
  
  const options = {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }
  };

  try {
    console.log('📤 Request Headers for HTML Response:');
    console.log(JSON.stringify(options.headers, null, 2));
    
    console.log('\n✅ Expected HTML Response:');
    console.log('- Interactive HTML page with embedded tokens');
    console.log('- Auto-redirect functionality');
    console.log('- Manual redirect buttons');
    console.log('- Popup window support');
    console.log('- Error handling with retry options');

  } catch (error) {
    console.error('❌ HTML Response Test Error:', error.message);
  }
}

/**
 * Test OAuth Flow Initiation
 */
async function testOAuthInitiation() {
  console.log('\n🔍 Testing OAuth Flow Initiation...');
  
  console.log(`📍 OAuth Initiation URL: ${API_BASE}/google`);
  console.log(`📍 OAuth Callback URL: ${API_BASE}/google/callback`);
  
  console.log('\n🔄 OAuth Flow Steps:');
  console.log('1. User visits: GET /api/v1/auth/google');
  console.log('2. Redirected to Google OAuth');
  console.log('3. User authorizes application');
  console.log('4. Google redirects to: GET /api/v1/auth/google/callback');
  console.log('5. System analyzes request headers');
  console.log('6. Returns appropriate response (JSON or HTML)');
}

/**
 * Frontend Integration Examples
 */
function showFrontendExamples() {
  console.log('\n📚 Frontend Integration Examples:');
  
  console.log('\n🔧 React/Vue/Angular (API Response):');
  console.log(`
// Initiate OAuth with API response
const initiateOAuth = () => {
  const params = new URLSearchParams({
    'api-request': 'true'
  });
  
  window.location.href = \`${API_BASE}/google?\${params}\`;
};

// Handle OAuth callback (if using popup)
window.addEventListener('message', (event) => {
  if (event.data.type === 'OAUTH_SUCCESS') {
    const { accessToken, refreshToken, user } = event.data;
    // Store tokens and update UI
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }
});`);

  console.log('\n🌐 Traditional Web App (HTML Response):');
  console.log(`
<!-- Simple redirect - gets HTML response -->
<a href="${API_BASE}/google">Login with Google</a>

<!-- The callback will return an HTML page that handles the redirect -->`);

  console.log('\n📱 Popup Window (Mixed Response):');
  console.log(`
// Open OAuth in popup
const popup = window.open(
  '${API_BASE}/google?popup=true', 
  'oauth', 
  'width=500,height=600'
);

// Listen for success message
window.addEventListener('message', (event) => {
  if (event.data.type === 'OAUTH_SUCCESS') {
    const { accessToken, refreshToken, user } = event.data;
    // Handle tokens
    popup.close();
  }
});`);
}

/**
 * Security Considerations
 */
function showSecurityInfo() {
  console.log('\n🛡️ Security Features:');
  console.log('✅ Device fingerprinting validation');
  console.log('✅ Origin validation for security');
  console.log('✅ URL sanitization');
  console.log('✅ Secure token generation');
  console.log('✅ Refresh token rotation');
  console.log('✅ Access token blacklisting');
  console.log('✅ CSRF protection');
  console.log('✅ Rate limiting support');
  
  console.log('\n🔒 Security Headers for API Requests:');
  console.log('- Accept: application/json');
  console.log('- X-OAuth-API-Request: true');
  console.log('- X-Requested-With: XMLHttpRequest');
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 OAuth Dual Response System Test');
  console.log('=====================================');
  
  await testOAuthInitiation();
  await testApiResponse();
  await testHtmlResponse();
  showFrontendExamples();
  showSecurityInfo();
  
  console.log('\n✨ Test Complete!');
  console.log('\n📖 For more information, see: docs/oauth-dual-response-system.md');
  console.log(`🌐 Swagger docs: ${BASE_URL}/api/docs`);
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testApiResponse,
  testHtmlResponse,
  testOAuthInitiation,
  showFrontendExamples,
  showSecurityInfo
};
