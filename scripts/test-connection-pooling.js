#!/usr/bin/env node

/**
 * Test script to verify connection pooling implementation
 * This script validates that the configuration loads correctly
 * and the database connection pool is properly initialized
 */

const path = require('path');

// Set environment to test
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// Load environment variables from .env file
require('dotenv').config({ path: path.join(__dirname, '../.env') });

console.log('🧪 Testing Connection Pooling Implementation...\n');

// Test 1: Validate environment variables
console.log('1️⃣  Checking environment variables...');
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL is not set');
  process.exit(1);
}
console.log('✅ DATABASE_URL is configured');

// Test 2: Check if project is built
console.log('\n2️⃣  Checking if project is built...');
const distPath = path.join(__dirname, '../dist');
if (!require('fs').existsSync(distPath)) {
  console.error('❌ Project not built. Please run: yarn build');
  process.exit(1);
}
console.log('✅ Project is built');

// Test 3: Load database configuration module
console.log('\n3️⃣  Loading database configuration...');
try {
  const dbConfig = require('../dist/src/config/database.config');
  
  // Test getDatabaseConfig function
  const config = dbConfig.getDatabaseConfig();
  console.log('✅ Database configuration loaded successfully');
  console.log('   Configuration:');
  console.log(`   - Max Connections: ${config.connectionPool.max}`);
  console.log(`   - Min Connections: ${config.connectionPool.min}`);
  console.log(`   - Connection Timeout: ${config.connectionPool.timeout}ms`);
  console.log(`   - Idle Timeout: ${config.connectionPool.idleTimeoutMillis}ms`);
  console.log(`   - Query Timeout: ${config.queryTimeout}ms`);
  console.log(`   - Logging Enabled: ${config.enableLogging}`);
  
  // Test buildConnectionString function
  console.log('\n4️⃣  Testing connection string builder...');
  const connectionString = dbConfig.buildConnectionString(config);
  console.log('✅ Connection string built successfully');
  
  // Validate connection string has pool parameters
  const url = new URL(connectionString);
  const hasConnectionLimit = url.searchParams.has('connection_limit');
  const hasPoolTimeout = url.searchParams.has('pool_timeout');
  const hasApplicationName = url.searchParams.has('application_name');
  
  if (hasConnectionLimit && hasPoolTimeout && hasApplicationName) {
    console.log('✅ Pool parameters added to connection string');
    console.log(`   - connection_limit: ${url.searchParams.get('connection_limit')}`);
    console.log(`   - pool_timeout: ${url.searchParams.get('pool_timeout')}`);
    console.log(`   - application_name: ${url.searchParams.get('application_name')}`);
  } else {
    console.warn('⚠️  Some pool parameters missing from connection string');
  }
  
  // Test createPrismaClientOptions function
  console.log('\n5️⃣  Testing Prisma client options creation...');
  const prismaOptions = dbConfig.createPrismaClientOptions(config);
  console.log('✅ Prisma client options created successfully');
  
  if (prismaOptions.datasources && prismaOptions.datasources.db) {
    console.log('✅ Datasource configuration present');
  }
  
  if (prismaOptions.log && Array.isArray(prismaOptions.log)) {
    console.log(`✅ Logging configuration present (${prismaOptions.log.length} levels)`);
  }
  
} catch (error) {
  console.error('❌ Error loading database configuration:', error.message);
  console.error(error.stack);
  process.exit(1);
}

// Test 6: Verify PrismaService can be loaded
console.log('\n6️⃣  Checking PrismaService availability...');
try {
  // Note: We can't fully instantiate PrismaService without NestJS context
  // but we can verify the module exists
  const prismaServicePath = path.join(__dirname, '../dist/src/prisma/prisma.service');
  require.resolve(prismaServicePath);
  console.log('✅ PrismaService module found and loadable');
} catch (error) {
  console.error('❌ PrismaService module not found:', error.message);
  console.error('   Make sure to run: yarn build');
  process.exit(1);
}

// Test 7: Verify module structure
console.log('\n7️⃣  Verifying module structure...');
const fs = require('fs');

const filesToCheck = [
  { path: 'src/config/database.config.ts', desc: 'Database configuration', required: true },
  { path: 'src/prisma/prisma.service.ts', desc: 'Enhanced PrismaService', required: true },
  { path: 'src/prisma/prisma.module.ts', desc: 'Prisma module', required: true },
  { path: '.env-example', desc: 'Environment example file', required: true },
];

let allRequiredFilesPresent = true;
filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, '..', file.path);
  if (fs.existsSync(fullPath)) {
    console.log(`   ✅ ${file.desc}: ${file.path}`);
  } else {
    console.log(`   ❌ ${file.desc}: ${file.path} (missing)`);
    if (file.required) {
      allRequiredFilesPresent = false;
    }
  }
});

if (!allRequiredFilesPresent) {
  console.error('\n❌ Some required files are missing');
  process.exit(1);
}

// Summary
console.log('\n' + '='.repeat(60));
console.log('🎉 Connection Pooling Implementation Test Results');
console.log('='.repeat(60));
console.log('✅ All tests passed successfully!');
console.log('\nImplementation is ready for:');
console.log('  • Development environment');
console.log('  • Production deployment');
console.log('  • Connection pool monitoring');
console.log('  • Performance optimization');
console.log('\nNext steps:');
console.log('  1. Start the application: yarn start:dev');
console.log('  2. Check logs for connection pool statistics');
console.log('  3. Monitor connection usage in production');
console.log('  4. Adjust pool size based on load');
console.log('\nDocumentation:');
console.log('  • Full guide: docs/DATABASE_CONFIGURATION.md');
console.log('  • Quick start: docs/QUICK_START_CONNECTION_POOLING.md');
console.log('  • Implementation: docs/CONNECTION_POOLING_IMPLEMENTATION.md');
console.log('='.repeat(60));

process.exit(0);