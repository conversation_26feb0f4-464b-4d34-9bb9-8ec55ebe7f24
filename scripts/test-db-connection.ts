import { PrismaClient } from '../generated/prisma';

/**
 * Simple script to test database connection and performance
 */
async function testDatabaseConnection() {
  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

  console.log('🔌 Testing database connection...');
  const startTime = Date.now();

  try {
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // Test simple query
    const queryStart = Date.now();
    await prisma.$queryRaw`SELECT 1 as test`;
    const queryTime = Date.now() - queryStart;
    console.log(`✅ Simple query executed in ${queryTime}ms`);

    // Test table access
    const tableStart = Date.now();
    const userCount = await prisma.user.count();
    const tableTime = Date.now() - tableStart;
    console.log(`✅ User table accessed in ${tableTime}ms (${userCount} users found)`);

    // Test wallet table access
    const walletStart = Date.now();
    const walletCount = await prisma.wallet.count();
    const walletTime = Date.now() - walletStart;
    console.log(`✅ Wallet table accessed in ${walletTime}ms (${walletCount} wallets found)`);

    const totalTime = Date.now() - startTime;
    console.log(`🎉 All tests passed in ${totalTime}ms`);

  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database disconnected');
  }
}

// Run the test
testDatabaseConnection()
  .then(() => {
    console.log('✅ Database connection test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Database connection test failed:', error);
    process.exit(1);
  });
