#!/usr/bin/env node

const { Client } = require('pg');

async function clearAdvisoryLocks() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false
  });

  try {
    await client.connect();
    console.log('🔍 Checking for advisory locks...');

    // Check current advisory locks
    const locksResult = await client.query(`
      SELECT 
        locktype,
        classid,
        objid,
        pid,
        mode,
        granted
      FROM pg_locks 
      WHERE locktype = 'advisory'
    `);

    if (locksResult.rows.length > 0) {
      console.log('📋 Current advisory locks:');
      console.table(locksResult.rows);

      // Clear Prisma migration locks (classid = 72707369)
      const clearResult = await client.query(`
        SELECT pg_advisory_unlock_all()
      `);
      
      console.log('🧹 Cleared all advisory locks for current session');
    } else {
      console.log('✅ No advisory locks found');
    }

  } catch (error) {
    console.error('❌ Error checking/clearing locks:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

async function main() {
  try {
    await clearAdvisoryLocks();
    console.log('✅ Lock check/clear completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('💥 Lock clear script failed:', error);
    process.exit(1);
  }
}

main();
