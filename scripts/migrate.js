#!/usr/bin/env node

const { execSync } = require('child_process');
const { Client } = require('pg');

const MAX_RETRIES = 3;
const RETRY_DELAY = 10000; // 10 seconds - increased from 5

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function clearAdvisoryLocks() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
    connectionTimeoutMillis: 15000, // 15 seconds
    query_timeout: 30000, // 30 seconds
    statement_timeout: 30000 // 30 seconds
  });

  try {
    await client.connect();
    console.log('🧹 Clearing any existing advisory locks...');

    // Kill any existing connections that might be holding locks
    try {
      console.log('🧹 Checking for blocking connections...');
      const blockingQueries = await client.query(`
        SELECT pid, usename, application_name, state, query_start, query 
        FROM pg_stat_activity 
        WHERE state IN ('active', 'idle in transaction', 'idle in transaction (aborted)')
          AND (query ILIKE '%prisma%' OR query ILIKE '%migrate%' OR application_name ILIKE '%prisma%')
          AND pid != pg_backend_pid()
        ORDER BY query_start
      `);
      
      if (blockingQueries.rows.length > 0) {
        console.log(`💥 Found ${blockingQueries.rows.length} potentially blocking connections`);
        for (const row of blockingQueries.rows) {
          console.log(`  PID: ${row.pid}, State: ${row.state}, App: ${row.application_name}`);
          try {
            await client.query('SELECT pg_terminate_backend($1)', [row.pid]);
            console.log(`✅ Terminated PID ${row.pid}`);
          } catch (terminateError) {
            console.warn(`⚠️  Could not terminate PID ${row.pid}:`, terminateError.message);
          }
        }
        
        // Wait a bit for termination to take effect
        await sleep(3000);
      }
    } catch (queryError) {
      console.warn('⚠️  Could not check for blocking queries:', queryError.message);
    }

    // Clear all advisory locks - try multiple approaches
    try {
      await client.query('SELECT pg_advisory_unlock_all()');
      console.log('✅ Advisory locks cleared with pg_advisory_unlock_all()');
    } catch (unlockError) {
      console.warn('⚠️  pg_advisory_unlock_all() failed, trying specific locks...');
      
      // Try to clear the specific Prisma migration lock (72707369 from the error)
      try {
        await client.query('SELECT pg_advisory_unlock(72707369)');
        console.log('✅ Prisma migration lock 72707369 cleared');
      } catch (specificError) {
        console.warn('⚠️  Could not clear specific migration lock:', specificError.message);
      }
    }
    
  } catch (error) {
    console.warn('⚠️  Could not clear advisory locks:', error.message);
  } finally {
    await client.end();
  }
}

async function runMigration(attempt = 1) {
  try {
    console.log(`🚀 Running Prisma migration (attempt ${attempt}/${MAX_RETRIES})...`);

    // Always clear advisory locks before migration attempts
    await clearAdvisoryLocks();
    
    // Wait a bit after clearing locks
    await sleep(2000);

    // Set environment variables for better connection handling
    const migrationEnv = {
      ...process.env,
      PRISMA_MIGRATE_TIMEOUT: '90000',        // Increased to 90 seconds
      DATABASE_CONNECTION_TIMEOUT: '45000',    // Increased to 45 seconds
      DATABASE_STATEMENT_TIMEOUT: '90000',     // Statement timeout
      DATABASE_QUERY_TIMEOUT: '90000',         // Query timeout
      PRISMA_CLIENT_ENGINE_TYPE: 'binary',     // Use binary engine for better performance
      DEBUG: attempt > 1 ? 'prisma:client' : undefined // Enable debug on retries
    };

    // Modify DATABASE_URL to include timeout parameters
    const originalDbUrl = process.env.DATABASE_URL;
    const dbUrl = new URL(originalDbUrl);
    
    // Add connection timeout parameters
    dbUrl.searchParams.set('connect_timeout', '60');
    dbUrl.searchParams.set('application_name', 'prisma_migrate');
    dbUrl.searchParams.set('lock_timeout', '120000'); // 2 minutes
    
    const modifiedDbUrl = dbUrl.toString();
    
    try {
      execSync('npx prisma migrate deploy --schema=./prisma/schema.prisma', {
        stdio: 'inherit',
        timeout: 180000, // Increased to 3 minutes
        env: {
          ...migrationEnv,
          DATABASE_URL: modifiedDbUrl
        }
      });
    } catch (deployError) {
      console.warn(`⚠️  Standard migration deploy failed, trying force approach...`);
      
      // Try a more aggressive approach - reset and deploy
      try {
        console.log('💥 Attempting to force migration status reset...');
        execSync('npx prisma migrate resolve --applied "*" --schema=./prisma/schema.prisma', {
          stdio: 'inherit',
          timeout: 60000,
          env: {
            ...migrationEnv,
            DATABASE_URL: modifiedDbUrl
          }
        });
        
        // Try deploy again after resolve
        execSync('npx prisma migrate deploy --schema=./prisma/schema.prisma', {
          stdio: 'inherit',
          timeout: 180000,
          env: {
            ...migrationEnv,
            DATABASE_URL: modifiedDbUrl
          }
        });
      } catch (forceError) {
        console.error('❌ Both standard and force migration approaches failed');
        throw deployError; // Throw the original error
      }
    }

    console.log('✅ Migration completed successfully!');
    return true;
  } catch (error) {
    console.error(`❌ Migration failed (attempt ${attempt}/${MAX_RETRIES}):`, error.message);

    if (attempt < MAX_RETRIES) {
      console.log(`⏳ Waiting ${RETRY_DELAY/1000} seconds before retry...`);
      await sleep(RETRY_DELAY);
      return runMigration(attempt + 1);
    } else {
      console.error('💥 All migration attempts failed!');
      throw error;
    }
  }
}

async function main() {
  try {
    await runMigration();
    process.exit(0);
  } catch (error) {
    console.error('Migration script failed:', error);
    process.exit(1);
  }
}

main();
