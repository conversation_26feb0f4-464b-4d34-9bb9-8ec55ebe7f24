import { PrismaClient } from '../generated/prisma';
import { WalletService } from '../src/wallet/wallet.service';
import { PrismaService } from '../src/prisma/prisma.service';
import { CreateWalletDto } from '../src/wallet/dto/create-wallet.dto';

/**
 * Test script to verify wallet creation functionality
 */
async function testWalletCreation() {
  const prisma = new PrismaService();
  const walletService = new WalletService(prisma);

  console.log('🧪 Testing wallet creation...');

  try {
    // Use an existing user ID from the database
    const userId = '01994ba2-3c6e-7b53-9660-0f766d409577'; // stevan user
    console.log(`📝 Testing with user ID: ${userId} (length: ${userId.length})`);

    // Create test wallet data
    const createWalletDto: CreateWalletDto = {
      wallet_address: '******************************************',
      wallet_type: 'METAMASK' as any,
      chains: [
        {
          chainId: '1',
          chainName: 'Ethereum Mainnet',
          symbol: 'ETH' as any,
        },
        {
          chainId: '137',
          chainName: 'Polygon Mainnet',
          symbol: 'MATIC' as any,
        },
        {
          chainId: '42161',
          chainName: 'Arbitrum One',
          symbol: 'ARB' as any,
        }
      ]
    };

    console.log('📝 Creating wallet with data:', JSON.stringify(createWalletDto, null, 2));

    // Test wallet creation
    const result = await walletService.createWallet(userId, createWalletDto);
    
    console.log('✅ Wallet created successfully!');
    console.log('📄 Result:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('❌ Wallet creation failed:', error.message);
    console.error('📄 Full error:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database disconnected');
  }
}

// Run the test
testWalletCreation()
  .then(() => {
    console.log('✅ Wallet creation test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Wallet creation test failed:', error);
    process.exit(1);
  });
