#!/usr/bin/env node

const { Client } = require('pg');

async function forceClearLocks() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
    connectionTimeoutMillis: 15000,
  });

  try {
    await client.connect();
    console.log('🔌 Connected to database');

    // Check current advisory locks
    console.log('🔍 Checking current advisory locks...');
    const currentLocks = await client.query(`
      SELECT 
        classid, objid, objsubid, locktype, pid, mode, granted
      FROM pg_locks 
      WHERE locktype = 'advisory'
      ORDER BY pid
    `);

    console.log(`Found ${currentLocks.rows.length} advisory locks:`);
    currentLocks.rows.forEach((lock, i) => {
      console.log(`  ${i+1}. PID: ${lock.pid}, ClassID: ${lock.classid}, ObjID: ${lock.objid}, Mode: ${lock.mode}, Granted: ${lock.granted}`);
    });

    // Check for active connections
    console.log('🔍 Checking active connections...');
    const connections = await client.query(`
      SELECT 
        pid, usename, application_name, state, query_start, 
        CASE WHEN state = 'active' THEN query ELSE '[IDLE]' END as current_query
      FROM pg_stat_activity 
      WHERE state != 'idle'
        AND pid != pg_backend_pid()
      ORDER BY query_start
    `);

    console.log(`Found ${connections.rows.length} active connections:`);
    connections.rows.forEach((conn, i) => {
      console.log(`  ${i+1}. PID: ${conn.pid}, User: ${conn.usename}, App: ${conn.application_name}, State: ${conn.state}`);
      if (conn.state === 'active') {
        console.log(`      Query: ${conn.current_query?.substring(0, 100)}...`);
      }
    });

    // Force unlock the specific migration lock
    console.log('🔓 Attempting to force unlock the specific migration lock (72707369)...');
    try {
      const unlockResult = await client.query('SELECT pg_advisory_unlock(72707369)');
      console.log('✅ Unlock result:', unlockResult.rows[0]);
    } catch (unlockError) {
      console.warn('⚠️  Specific unlock failed:', unlockError.message);
    }

    // Try to unlock using the two-parameter version
    console.log('🔓 Trying two-parameter unlock...');
    try {
      const unlockResult2 = await client.query('SELECT pg_advisory_unlock(0, 72707369)');
      console.log('✅ Two-param unlock result:', unlockResult2.rows[0]);
    } catch (unlock2Error) {
      console.warn('⚠️  Two-param unlock failed:', unlock2Error.message);
    }

    // Clear all advisory locks for this session
    console.log('🔓 Clearing all advisory locks for current session...');
    try {
      await client.query('SELECT pg_advisory_unlock_all()');
      console.log('✅ All advisory locks cleared for current session');
    } catch (clearAllError) {
      console.warn('⚠️  Clear all failed:', clearAllError.message);
    }

    // Force terminate any connections that might be holding locks
    console.log('💥 Checking for connections to terminate...');
    const migrationConnections = await client.query(`
      SELECT pid, usename, application_name, state, query_start
      FROM pg_stat_activity 
      WHERE (query ILIKE '%migrate%' OR query ILIKE '%prisma%' OR application_name ILIKE '%prisma%')
        AND pid != pg_backend_pid()
    `);

    if (migrationConnections.rows.length > 0) {
      console.log(`Found ${migrationConnections.rows.length} migration-related connections to terminate:`);
      for (const conn of migrationConnections.rows) {
        console.log(`  Terminating PID: ${conn.pid} (${conn.application_name})`);
        try {
          await client.query('SELECT pg_terminate_backend($1)', [conn.pid]);
          console.log(`  ✅ Terminated PID ${conn.pid}`);
        } catch (terminateError) {
          console.warn(`  ⚠️  Could not terminate PID ${conn.pid}:`, terminateError.message);
        }
      }
    } else {
      console.log('No migration-related connections found to terminate');
    }

    // Final check of advisory locks
    console.log('🔍 Final advisory lock check...');
    const finalLocks = await client.query(`
      SELECT classid, objid, locktype, pid, mode, granted
      FROM pg_locks 
      WHERE locktype = 'advisory'
    `);

    if (finalLocks.rows.length === 0) {
      console.log('✅ All advisory locks cleared successfully!');
    } else {
      console.log(`⚠️  Still ${finalLocks.rows.length} advisory locks remaining:`);
      finalLocks.rows.forEach((lock, i) => {
        console.log(`  ${i+1}. PID: ${lock.pid}, ClassID: ${lock.classid}, ObjID: ${lock.objid}, Mode: ${lock.mode}`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

forceClearLocks();