#!/usr/bin/env node

const { Client } = require('pg');

async function killBlockingPID() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? {
      rejectUnauthorized: false
    } : false,
    connectionTimeoutMillis: 15000,
  });

  try {
    await client.connect();
    console.log('🔌 Connected to database');

    // Get information about PID 7060
    console.log('🔍 Investigating PID 7060...');
    const pidInfo = await client.query(`
      SELECT 
        pid, usename, application_name, client_addr, client_hostname, client_port,
        backend_start, xact_start, query_start, state_change, state, 
        wait_event_type, wait_event, query
      FROM pg_stat_activity 
      WHERE pid = 7060
    `);

    if (pidInfo.rows.length > 0) {
      const info = pidInfo.rows[0];
      console.log('📋 PID 7060 Information:');
      console.log(`  User: ${info.usename}`);
      console.log(`  Application: ${info.application_name}`);
      console.log(`  Client: ${info.client_addr}:${info.client_port}`);
      console.log(`  Backend Start: ${info.backend_start}`);
      console.log(`  Transaction Start: ${info.xact_start}`);
      console.log(`  Query Start: ${info.query_start}`);
      console.log(`  State: ${info.state}`);
      console.log(`  Wait Event: ${info.wait_event_type}/${info.wait_event}`);
      console.log(`  Query: ${info.query || '[No query]'}`);
      
      // Calculate how long this connection has been running
      const now = new Date();
      const backendStart = new Date(info.backend_start);
      const ageMinutes = Math.floor((now - backendStart) / (1000 * 60));
      console.log(`  Connection Age: ${ageMinutes} minutes`);
    } else {
      console.log('⚠️  PID 7060 not found in pg_stat_activity - it might be from a different connection or already terminated');
    }

    // Check all locks for this PID
    console.log('🔍 Checking all locks held by PID 7060...');
    const allLocks = await client.query(`
      SELECT 
        locktype, database, relation::regclass, page, tuple, virtualxid, 
        transactionid, classid, objid, objsubid, virtualtransaction, 
        mode, granted, fastpath
      FROM pg_locks 
      WHERE pid = 7060
      ORDER BY locktype, granted DESC
    `);

    console.log(`Found ${allLocks.rows.length} locks held by PID 7060:`);
    allLocks.rows.forEach((lock, i) => {
      console.log(`  ${i+1}. Type: ${lock.locktype}, Mode: ${lock.mode}, Granted: ${lock.granted}`);
      if (lock.locktype === 'relation' && lock.relation) {
        console.log(`      Relation: ${lock.relation}`);
      } else if (lock.locktype === 'advisory') {
        console.log(`      Advisory: ClassID=${lock.classid}, ObjID=${lock.objid}`);
      }
    });

    // Attempt to cancel the query first (less aggressive)
    console.log('🔄 Attempting to cancel query for PID 7060...');
    try {
      const cancelResult = await client.query('SELECT pg_cancel_backend(7060)');
      console.log('✅ Cancel result:', cancelResult.rows[0]);
      
      // Wait a moment and check if locks are released
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const remainingLocks = await client.query(`
        SELECT COUNT(*) as count FROM pg_locks WHERE pid = 7060 AND locktype = 'advisory'
      `);
      
      if (remainingLocks.rows[0].count === '0') {
        console.log('🎉 Success! Advisory locks were released after canceling the query');
        return;
      } else {
        console.log('⚠️  Advisory locks still present after cancel, proceeding to terminate...');
      }
    } catch (cancelError) {
      console.warn('⚠️  Cancel backend failed:', cancelError.message);
    }

    // If cancel doesn't work, terminate the backend
    console.log('💥 Attempting to terminate PID 7060...');
    try {
      const terminateResult = await client.query('SELECT pg_terminate_backend(7060)');
      console.log('✅ Terminate result:', terminateResult.rows[0]);
      
      // Wait a moment for termination to take effect
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check if locks are now gone
      const finalLocks = await client.query(`
        SELECT COUNT(*) as count FROM pg_locks WHERE locktype = 'advisory'
      `);
      
      if (finalLocks.rows[0].count === '0') {
        console.log('🎉 Success! All advisory locks cleared after terminating the backend');
      } else {
        console.log(`⚠️  Still ${finalLocks.rows[0].count} advisory locks remaining`);
      }
      
    } catch (terminateError) {
      console.error('❌ Terminate backend failed:', terminateError.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

killBlockingPID();