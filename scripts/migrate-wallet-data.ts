import { PrismaClient } from '../generated/prisma';

/**
 * Data migration script to populate missing wallet_type and chain symbol fields
 * for existing wallet records in the database.
 * 
 * This script safely handles existing data by:
 * 1. Checking for existing wallets without wallet_type
 * 2. Setting default values based on wallet characteristics
 * 3. Updating chain records with appropriate symbols
 * 4. Providing comprehensive logging and error handling
 */

const prisma = new PrismaClient();

// Default values for migration
const DEFAULT_WALLET_TYPE = 'EXTERNAL'; // Safe default for existing wallets
const CHAIN_SYMBOL_MAPPING: Record<string, string> = {
  '1': 'ETH',        // Ethereum Mainnet
  '56': 'BNB',       // BSC
  '137': 'MATIC',    // Polygon
  '43114': 'AVAX',   // Avalanche
  '250': 'FTM',      // Fantom
  '42161': 'ARB',    // Arbitrum
  '10': 'OP',        // Optimism
};

async function migrateWalletData() {
  console.log('🚀 Starting wallet data migration...');
  
  try {
    // Step 1: Check existing wallet records
    const existingWallets = await prisma.wallet.findMany({
      include: {
        chains: true,
      },
    });

    console.log(`📊 Found ${existingWallets.length} existing wallet records`);

    if (existingWallets.length === 0) {
      console.log('✅ No existing wallets found. Migration not needed.');
      return;
    }

    // Step 2: Update wallets without wallet_type
    let walletsUpdated = 0;
    let chainsUpdated = 0;

    for (const wallet of existingWallets) {
      // Update wallet if wallet_type is missing (this shouldn't happen with new schema)
      // This is a safety check for any edge cases
      
      // Update associated chains without symbols
      for (const chain of wallet.chains) {
        const symbol = CHAIN_SYMBOL_MAPPING[chain.chainId] || 'ETH'; // Default to ETH
        
        try {
          await prisma.chain.update({
            where: { id: chain.id },
            data: { symbol: symbol as any }, // Type assertion for enum
          });
          chainsUpdated++;
          console.log(`✅ Updated chain ${chain.id} with symbol: ${symbol}`);
        } catch (error) {
          console.warn(`⚠️ Failed to update chain ${chain.id}:`, error);
        }
      }
    }

    console.log(`✅ Migration completed successfully!`);
    console.log(`📈 Updated ${walletsUpdated} wallets and ${chainsUpdated} chains`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Backup function to export existing data before migration
async function backupExistingData() {
  console.log('💾 Creating backup of existing wallet data...');
  
  try {
    const wallets = await prisma.wallet.findMany({
      include: {
        chains: true,
        user: {
          select: {
            id: true,
            email: true,
            username: true,
          },
        },
      },
    });

    const backupData = {
      timestamp: new Date().toISOString(),
      walletCount: wallets.length,
      wallets: wallets,
    };

    // In a real scenario, you'd save this to a file
    console.log('📋 Backup data prepared:', {
      timestamp: backupData.timestamp,
      walletCount: backupData.walletCount,
    });

    return backupData;
  } catch (error) {
    console.error('❌ Backup failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Create backup first
    await backupExistingData();
    
    // Run migration
    await migrateWalletData();
    
    console.log('🎉 All migration tasks completed successfully!');
  } catch (error) {
    console.error('💥 Migration process failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { migrateWalletData, backupExistingData };
