# Application Configuration
PORT=
NODE_ENV=development

# Database Configuration
DATABASE_URL=

# Database Connection Pool Configuration
# Maximum number of database connections in the pool (default: 20)
# Adjust based on your application's concurrent request volume
DATABASE_POOL_MAX=20

# Minimum number of database connections in the pool (default: 2)
# Keeping minimum connections helps reduce connection overhead
DATABASE_POOL_MIN=2

# Connection timeout in milliseconds (default: 20000)
# Time to wait before timing out when acquiring a connection
DATABASE_CONNECTION_TIMEOUT=20000

# Idle timeout in milliseconds (default: 10000)
# Maximum time a connection can remain idle before being closed
DATABASE_IDLE_TIMEOUT=10000

# Query timeout in milliseconds (default: 60000)
# Maximum time a query can run before being cancelled
DATABASE_QUERY_TIMEOUT=60000

# Authentication & Security
JWT_SECRET=
SWAGGER_USER=
SWAGGER_PASS=

# Email Configuration
SENDGRID_API_KEY=
SENDGRID_FROM_EMAIL=

# Frontend Configuration
FRONTEND_URL=

# OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=

# Safe Configuration
SAFE_ADDRESS=
SAFE_API_KEY=
SAFE_SENDER_ADDRESS=
SAFE_SENDER_PRIVATE_KEY=
