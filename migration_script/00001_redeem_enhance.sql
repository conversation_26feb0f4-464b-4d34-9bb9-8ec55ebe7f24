ALTER TABLE redeem 
ADD COLUMN user_id UUID,
ADD COLUMN wallet_id UUID,
ADD COLUMN chain_id VARCHAR(64),
ADD COLUMN payment_provider TEXT,
ADD COLUMN currencies TEXT;

CREATE INDEX idx_redeem_user_id ON redeem(user_id);
CREATE INDEX idx_redeem_wallet_id ON redeem(wallet_id) WHERE wallet_id IS NOT NULL;
CREATE INDEX idx_redeem_chain_id ON redeem(chain_id) WHERE chain_id IS NOT NULL;

ALTER TABLE redeem 
ADD CONSTRAINT fk_redeem_user_id 
FOREIGN KEY (user_id) REFERENCES public."user"(id) ON DELETE CASCADE ON UPDATE cascade;

ALTER TABLE redeem 
ADD CONSTRAINT fk_redeem_wallet_id 
FOREIGN KEY (wallet_id) REFERENCES public.wallet(id) ON DELETE CASCADE ON UPDATE cascade;