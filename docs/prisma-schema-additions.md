# Schema Prisma Additions untuk Sistem Tagging

## Schema yang Perlu Ditambahkan ke `prisma/schema.prisma`

### 1. Model Category

```prisma
model Category {
  id          String   @id @default(uuid(7)) @db.Uuid
  slug        String   @unique
  label       String
  layer       String   // "1", "2", "3a", "3b", "4"
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  sortOrder   Int?     @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relasi dengan tags
  tags Tag[]

  @@index([layer, sortOrder])
  @@index([slug])
  @@map("category")
}
```

### 2. Model Tag

```prisma
model Tag {
  id          String   @id @default(uuid(7)) @db.Uuid
  slug        String   @unique
  label       String
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  categoryId  String   @map("category_id") @db.Uuid
  sortOrder   Int?     @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relasi
  category    Category     @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  productTags ProductTag[]

  @@index([categoryId])
  @@index([slug])
  @@map("tag")
}
```

### 3. Model ProductTag

```prisma
model ProductTag {
  id        String   @id @default(uuid(7)) @db.Uuid
  productId String   @map("product_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")

  // Relasi
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([productId, tagId])
  @@index([productId])
  @@index([tagId])
  @@map("product_tag")
}
```

### 4. Update Model Product

Tambahkan relasi ini ke model Product yang sudah ada:

```prisma
model Product {
  // ... semua field yang sudah ada ...
  
  supportedNetworks supported_network[]
  productTags       ProductTag[]  // TAMBAHKAN INI

  @@map("product")
}
```

## Migration Script

### Perintah untuk membuat migration:

```bash
npx prisma migrate dev --name add-product-tagging-system
```

### SQL Script Manual (jika diperlukan):

```sql
-- Create category table
CREATE TABLE "category" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "slug" VARCHAR(255) NOT NULL,
    "label" VARCHAR(255) NOT NULL,
    "layer" VARCHAR(10) NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "sort_order" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "category_pkey" PRIMARY KEY ("id")
);

-- Create tag table
CREATE TABLE "tag" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "slug" VARCHAR(255) NOT NULL,
    "label" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "category_id" UUID NOT NULL,
    "sort_order" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tag_pkey" PRIMARY KEY ("id")
);

-- Create product_tag junction table
CREATE TABLE "product_tag" (
    "id" UUID NOT NULL DEFAULT uuid_generate_v4(),
    "product_id" UUID NOT NULL,
    "tag_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_tag_pkey" PRIMARY KEY ("id")
);

-- Create unique constraints
ALTER TABLE "category" ADD CONSTRAINT "category_slug_key" UNIQUE ("slug");
ALTER TABLE "tag" ADD CONSTRAINT "tag_slug_key" UNIQUE ("slug");
ALTER TABLE "product_tag" ADD CONSTRAINT "product_tag_product_id_tag_id_key" UNIQUE ("product_id", "tag_id");

-- Create indexes
CREATE INDEX "category_layer_sort_order_idx" ON "category"("layer", "sort_order");
CREATE INDEX "category_slug_idx" ON "category"("slug");
CREATE INDEX "tag_category_id_idx" ON "tag"("category_id");
CREATE INDEX "tag_slug_idx" ON "tag"("slug");
CREATE INDEX "product_tag_product_id_idx" ON "product_tag"("product_id");
CREATE INDEX "product_tag_tag_id_idx" ON "product_tag"("tag_id");

-- Add foreign key constraints
ALTER TABLE "tag" ADD CONSTRAINT "tag_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "category"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "product_tag" ADD CONSTRAINT "product_tag_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "product_tag" ADD CONSTRAINT "product_tag_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "tag"("id") ON DELETE CASCADE ON UPDATE CASCADE;
```

## Seed Data untuk Testing

```typescript
// seed-data.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedTaggingSystem() {
  // 1. Buat Categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { slug: 'asset-class' },
      update: {},
      create: {
        slug: 'asset-class',
        label: 'Asset Class',
        layer: '1',
        description: 'Primary asset classification',
        sortOrder: 1
      }
    }),
    prisma.category.upsert({
      where: { slug: 'instrument-type' },
      update: {},
      create: {
        slug: 'instrument-type',
        label: 'Instrument Type',
        layer: '2',
        description: 'Type of financial instrument',
        sortOrder: 1
      }
    }),
    prisma.category.upsert({
      where: { slug: 'sector-industry' },
      update: {},
      create: {
        slug: 'sector-industry',
        label: 'Sector/Industry',
        layer: '3a',
        description: 'Industry sector classification',
        sortOrder: 1
      }
    }),
    prisma.category.upsert({
      where: { slug: 'type-factor-risk-profile' },
      update: {},
      create: {
        slug: 'type-factor-risk-profile',
        label: 'Type/Factor/Risk Profile',
        layer: '3b',
        description: 'Investment factor and risk characteristics',
        sortOrder: 2
      }
    }),
    prisma.category.upsert({
      where: { slug: 'region-market-exposure' },
      update: {},
      create: {
        slug: 'region-market-exposure',
        label: 'Region / Market Exposure',
        layer: '4',
        description: 'Geographic market exposure',
        sortOrder: 1
      }
    })
  ]);

  // 2. Buat Tags
  const categoryMap = categories.reduce((acc, cat) => {
    acc[cat.slug] = cat.id;
    return acc;
  }, {} as Record<string, string>);

  const tags = await Promise.all([
    // Asset Class Tags
    prisma.tag.upsert({
      where: { slug: 'equities' },
      update: {},
      create: {
        slug: 'equities',
        label: 'Equities',
        categoryId: categoryMap['asset-class'],
        sortOrder: 1
      }
    }),
    prisma.tag.upsert({
      where: { slug: 'fixed-income' },
      update: {},
      create: {
        slug: 'fixed-income',
        label: 'Fixed Income',
        categoryId: categoryMap['asset-class'],
        sortOrder: 2
      }
    }),
    
    // Instrument Type Tags
    prisma.tag.upsert({
      where: { slug: 'stock' },
      update: {},
      create: {
        slug: 'stock',
        label: 'Stock',
        categoryId: categoryMap['instrument-type'],
        sortOrder: 1
      }
    }),
    prisma.tag.upsert({
      where: { slug: 'etf' },
      update: {},
      create: {
        slug: 'etf',
        label: 'ETF',
        categoryId: categoryMap['instrument-type'],
        sortOrder: 2
      }
    }),
    
    // Sector Tags
    prisma.tag.upsert({
      where: { slug: 'technology' },
      update: {},
      create: {
        slug: 'technology',
        label: 'Technology',
        categoryId: categoryMap['sector-industry'],
        sortOrder: 1
      }
    }),
    prisma.tag.upsert({
      where: { slug: 'healthcare' },
      update: {},
      create: {
        slug: 'healthcare',
        label: 'Healthcare',
        categoryId: categoryMap['sector-industry'],
        sortOrder: 2
      }
    }),
    
    // Risk Profile Tags
    prisma.tag.upsert({
      where: { slug: 'value' },
      update: {},
      create: {
        slug: 'value',
        label: 'Value',
        categoryId: categoryMap['type-factor-risk-profile'],
        sortOrder: 1
      }
    }),
    prisma.tag.upsert({
      where: { slug: 'large-cap' },
      update: {},
      create: {
        slug: 'large-cap',
        label: 'Large Cap',
        categoryId: categoryMap['type-factor-risk-profile'],
        sortOrder: 2
      }
    }),
    
    // Region Tags
    prisma.tag.upsert({
      where: { slug: 'china' },
      update: {},
      create: {
        slug: 'china',
        label: 'China',
        categoryId: categoryMap['region-market-exposure'],
        sortOrder: 1
      }
    }),
    prisma.tag.upsert({
      where: { slug: 'usa' },
      update: {},
      create: {
        slug: 'usa',
        label: 'USA',
        categoryId: categoryMap['region-market-exposure'],
        sortOrder: 2
      }
    })
  ]);

  console.log('Seeded categories:', categories.length);
  console.log('Seeded tags:', tags.length);
  
  return { categories, tags };
}

// Jalankan seeding
seedTaggingSystem()
  .then(() => {
    console.log('Tagging system seeded successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Seeding failed:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

## Usage Examples

### 1. Membuat Product dengan Tags

```typescript
const product = await prisma.product.create({
  data: {
    displayName: 'China Tech Large Cap Fund',
    tokenName: 'CTLC',
    underlyingName: 'Chinese Technology Large Cap Index',
    symbol: 'CTLC',
    price: new Decimal('100.50'),
    priceChange24h: new Decimal('2.30'),
    priceChangePct24h: new Decimal('2.34'),
    productTags: {
      create: [
        { tagId: 'equities-tag-id' },
        { tagId: 'stock-tag-id' },
        { tagId: 'technology-tag-id' },
        { tagId: 'large-cap-tag-id' },
        { tagId: 'china-tag-id' }
      ]
    }
  }
});
```

### 2. Query Product dengan Tags

```typescript
const productWithTags = await prisma.product.findMany({
  include: {
    productTags: {
      include: {
        tag: {
          include: {
            category: true
          }
        }
      }
    }
  }
});
```

### 3. Filter Products by Tags

```typescript
const techProducts = await prisma.product.findMany({
  where: {
    productTags: {
      some: {
        tag: {
          slug: 'technology'
        }
      }
    }
  },
  include: {
    productTags: {
      include: {
        tag: {
          include: {
            category: true
          }
        }
      }
    }
  }
});
```

## Langkah Implementasi

1. **Tambahkan schema** ke `prisma/schema.prisma`
2. **Jalankan migration**: `npx prisma migrate dev --name add-product-tagging-system`
3. **Generate Prisma client**: `npx prisma generate`
4. **Jalankan seed data** untuk testing
5. **Update service layer** untuk handle tagging operations
6. **Buat API endpoints** untuk CRUD tags dan categories